package com.lovebeats.customViews.bottomSheet

import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.lovebeats.databinding.ViewBottomSheetBinding

class CustomBottomSheet(private val mActivity: AppCompatActivity,
                        private val mOptionsList: List<Item>,
                        private val bottomTextTile: String? = null,
                        val bottomTextAction: ((Boolean) -> Unit)? = null) : BottomSheetDialogFragment() {

    private lateinit var binding: ViewBottomSheetBinding


    data class Item(val title: String,
                    val subTitle: String,
                    val action: ((Boolean) -> Unit)? = null)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        binding = ViewBottomSheetBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View,
                               savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.recyclerViewBottomSheet.layoutManager = LinearLayoutManager(mActivity)
        binding.recyclerViewBottomSheet.adapter = BottomSheetListAdapter(mActivity, mOptionsList, this)
        binding.subscriptionTermsText.movementMethod = LinkMovementMethod.getInstance()
        binding.closeButton.setOnClickListener {
            dismiss()
        }

        if (!bottomTextTile.isNullOrEmpty()) {
            binding.restoreTextView.visibility = View.VISIBLE
            binding.restoreTextView.text = bottomTextTile
            binding.restoreTextView.setOnClickListener {
                bottomTextAction?.let { actionCallBack ->
                    actionCallBack(true)
                }
            }
        }
    }

    fun show() {

        show(mActivity.supportFragmentManager, this.tag)
    }
}
