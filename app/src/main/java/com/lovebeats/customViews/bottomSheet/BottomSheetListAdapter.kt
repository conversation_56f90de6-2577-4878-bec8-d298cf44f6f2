package com.lovebeats.customViews.bottomSheet

import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.Button
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.R
import com.lovebeats.databinding.ItemBottomSheetAdapterBinding

class BottomSheetListAdapter(private val mContext: Context,
                             private val mOptionsList: List<CustomBottomSheet.Item>,
                             private val mCustomBottomSheet: CustomBottomSheet
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val itemBinding = ItemBottomSheetAdapterBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(itemBinding, mCustomBottomSheet)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = mOptionsList[position]
        (holder as ViewHolder).bindItems(item.title, item.subTitle, item.action)
    }

    override fun getItemCount(): Int {
        return mOptionsList.size
    }
}

class ViewHolder(itemView: ItemBottomSheetAdapterBinding, private val customBottomSheet: CustomBottomSheet) : RecyclerView.ViewHolder(itemView.root) {

    fun bindItems(title: String?, subTitle: String?, action: ((Boolean) -> Unit)?) {

        val actionButton = itemView.findViewById<Button>(R.id.button_large_active_button)
        val combinedText = "$title"
        actionButton.text = combinedText

        action?.let { actionCallBack ->
            actionButton.setOnClickListener {
                actionCallBack(true)
                customBottomSheet.dismiss()
            }
        }
    }
}
