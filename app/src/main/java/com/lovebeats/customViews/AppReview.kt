package com.lovebeats.customViews

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.app.AlertDialog
import com.lovebeats.R
import com.lovebeats.analytics.APP_REVIEW_CLOSED
import com.lovebeats.analytics.APP_REVIEW_LOVE_IT
import com.lovebeats.analytics.APP_REVIEW_NEEDS_WORK
import com.lovebeats.analytics.APP_REVIEW_WRITE
import com.lovebeats.analytics.AnalyticsTrackingService
import com.lovebeats.databinding.EnjoyingDialogLayoutBinding
import com.lovebeats.databinding.ReviewDialogLayoutBinding
import com.lovebeats.subscriptions.Constants.Companion.PLAY_STORE_APP_URL
import com.lovebeats.utils.Utils

class AppReview {

    fun showEnjoyingLoveBeatDialog(activity: Activity) {

        val binding: EnjoyingDialogLayoutBinding
        val inflater: LayoutInflater = activity.layoutInflater
        binding = EnjoyingDialogLayoutBinding.inflate(inflater)
        val view = binding.root


        val builder = AlertDialog.Builder(activity).create()
        builder.setView(view)
        builder.setCancelable(false)

        binding.needsWorkContainer.setOnClickListener {
            builder.dismiss()
            showReviewLoveBeatDialog(activity, ReviewStatus.LEAVE_FEEDBACK)
            AnalyticsTrackingService.logEvent(activity, APP_REVIEW_NEEDS_WORK)
        }

        binding.loveItContainer.setOnClickListener {
            builder.dismiss()
            showReviewLoveBeatDialog(activity, ReviewStatus.WRITE_REVIEW)
            AnalyticsTrackingService.logEvent(activity, APP_REVIEW_LOVE_IT)
        }

        builder.show()
    }

    private fun showReviewLoveBeatDialog(activity: Activity, status: ReviewStatus) {
        val binding: ReviewDialogLayoutBinding
        val inflater: LayoutInflater = activity.layoutInflater
        binding = ReviewDialogLayoutBinding.inflate(inflater)
        val view = binding.root

        val builder = AlertDialog.Builder(activity).create()
        builder.setView(view)
        builder.setCancelable(false)

        if (status == ReviewStatus.WRITE_REVIEW) {
            binding.reviewTitle.text = activity.getString(R.string.review_title)
            binding.reviewDesc.text = activity.getString(R.string.review_desc)
            binding.writeReviewButton.text = activity.getString(R.string.review_button_write_review)
            binding.writeReviewButton.setOnClickListener {
                openPlayStore(activity)
                AnalyticsTrackingService.logEvent(activity, APP_REVIEW_WRITE)
            }
        } else {
            binding.starsImage.visibility = View.GONE
            binding.loveItImageReview.setImageDrawable(activity.getDrawable(R.drawable.ic_sad_face))
            binding.reviewTitle.visibility = View.GONE
            binding.reviewDesc.text = activity.getString(R.string.review_give_feedback)
            binding.writeReviewButton.text = activity.getString(R.string.review_button_leave_feedback)
            binding.writeReviewButton.setOnClickListener {
                Utils.openGmailApp(activity, "LoveBeats Feedback")
            }
        }

        binding.closeButton.setOnClickListener {
            AnalyticsTrackingService.logEvent(activity, APP_REVIEW_CLOSED)
            builder.dismiss()
        }

        builder.show()
    }

    private fun openPlayStore(activity: Activity) {
        val intent = Intent(Intent.ACTION_VIEW)
        intent.data = Uri.parse(PLAY_STORE_APP_URL)
        activity.startActivity(intent)
    }

    enum class ReviewStatus {
        WRITE_REVIEW,
        LEAVE_FEEDBACK
    }
}