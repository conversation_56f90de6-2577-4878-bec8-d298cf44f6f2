package com.lovebeats.customViews

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.Player.REPEAT_MODE_OFF
import androidx.media3.exoplayer.ExoPlayer
import com.lovebeats.R
import com.lovebeats.glide.GlideApp
import com.lovebeats.models.Song
import com.lovebeats.models.UserObject.userVolumeMuted
import timber.log.Timber


class PlayerView(context: Context, attrs: AttributeSet) : ConstraintLayout(context, attrs) {

    private val playbackStateListener: Player.Listener = playbackStateListener()
    private var player: ExoPlayer? = null

    private var playWhenReady = true
    private var currentItem = 0
    private var playbackPosition = 0L
    private var playerView: androidx.media3.ui.PlayerView
    val titleTextView: TextView
    val subTitleTextView: TextView
    val songImageView: ImageView
    val muteImageView: ImageView
    val spotifyImageView: ImageView

    private var volume: Float? = null

    init {
        val view: View = LayoutInflater.from(context).inflate(R.layout.view_player, this, true)
        playerView = view.findViewById(R.id.player_view)
        titleTextView = playerView.findViewById(R.id.song_title)
        subTitleTextView = playerView.findViewById(R.id.song_sub_title)
        songImageView = playerView.findViewById(R.id.song_image)
        muteImageView = playerView.findViewById(R.id.mute_image)
        spotifyImageView = playerView.findViewById(R.id.spotify_image)

        if (playerView.player?.isDeviceMuted == true) {
            muteImageView.setImageDrawable(AppCompatResources.getDrawable(context, R.drawable.mute_svgrepo_com))
        } else {
            muteImageView.setImageDrawable(AppCompatResources.getDrawable(context, R.drawable.speaker_wave_2_svgrepo_com))
        }
    }
    fun show(activity: Activity, song: Song?) {
        if (player == null && !activity.isFinishing) {
            song?.audioUrl?.let {
                initializePlayer(it, activity)
                titleTextView.text = song.title
                subTitleTextView.text = song.subTitle
                GlideApp.with(activity)
                    .load(song.imageUrl)
                    .into(songImageView)
            }

            volume = playerView.player?.volume

            if (userVolumeMuted) {
                playerView.player?.volume = 0F
            }

            muteImageView.setOnClickListener {
                if (playerView.player?.volume == 0F) {
                    muteImageView.setImageDrawable(AppCompatResources.getDrawable(activity, R.drawable.speaker_wave_2_svgrepo_com))
                    volume?.let {
                        playerView.player?.volume = it
                    }
                    userVolumeMuted = false
                } else {
                    muteImageView.setImageDrawable(AppCompatResources.getDrawable(activity, R.drawable.mute_svgrepo_com))
                    playerView.player?.volume = 0F
                    userVolumeMuted = true
                }
            }

            spotifyImageView.setOnClickListener {
                if (song?.spotifyUrl?.startsWith("http") == true || song?.spotifyUrl?.startsWith("https") == true) {
                    val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(song.spotifyUrl))
                    activity.startActivity(browserIntent)
                }
            }
        }
    }

    private fun initializePlayer(track: String, context: Activity) {
        player = ExoPlayer.Builder(context)
            .build()
            .also { exoPlayer ->
                playerView.player = exoPlayer

                val mediaItem = MediaItem.fromUri(track)
                exoPlayer.setMediaItem(mediaItem)
                exoPlayer.repeatMode = REPEAT_MODE_OFF
                exoPlayer.playWhenReady = playWhenReady
                exoPlayer.seekTo(currentItem, playbackPosition)
                exoPlayer.prepare()
            }
    }

    fun releasePlayer() {
        player?.let { exoPlayer ->
            playbackPosition = exoPlayer.currentPosition
            currentItem = exoPlayer.currentMediaItemIndex
            playWhenReady = exoPlayer.playWhenReady
            exoPlayer.removeListener(playbackStateListener)
            exoPlayer.release()
        }
        player = null
    }
}

private fun playbackStateListener() = object : Player.Listener {
    override fun onPlaybackStateChanged(playbackState: Int) {
        val stateString: String = when (playbackState) {
            ExoPlayer.STATE_IDLE -> "ExoPlayer.STATE_IDLE      -"
            ExoPlayer.STATE_BUFFERING -> "ExoPlayer.STATE_BUFFERING -"
            ExoPlayer.STATE_READY -> "ExoPlayer.STATE_READY     -"
            ExoPlayer.STATE_ENDED -> "ExoPlayer.STATE_ENDED     -"
            else -> "UNKNOWN_STATE             -"
        }
        Timber.d("PlayerView", "changed state to $stateString")
    }
}