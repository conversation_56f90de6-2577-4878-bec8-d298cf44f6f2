package com.lovebeats.extensions

import timber.log.Timber

fun String.getDoubleValue(): Double? {

    var result: Double? = null

    try {

        result = this.toDouble()
    } catch (ex: NumberFormatException) {

        Timber.e("Exception in getting double value from string: $ex")
    }

    return result
}

fun String.getFloatValue(): Float? {

    var result: Float? = null

    try {

        result = this.toFloat()
    } catch (ex: NumberFormatException) {

        Timber.e("Exception in getting float value from string: $ex")
    }

    return result
}

fun String.getIntValue(): Int? {

    var result: Int? = null

    try {
        result = this.toInt()
    } catch (ex: NumberFormatException) {
        try {
            result = this.toFloat().toInt()
        }catch (ex2: java.lang.NumberFormatException) {
            Timber.e("Exception in getting int value from string= $this : $ex")
        }
    }

    return result
}
