package com.lovebeats.subscriptions.repo

import android.content.Context
import androidx.lifecycle.MediatorLiveData
import com.android.billingclient.api.Purchase
import com.lovebeats.services.ServiceResponse
import com.lovebeats.storage.room.LocalDataSource
import com.lovebeats.subscriptions.model.PurchaseAcknowledgmentStatus
import com.lovebeats.subscriptions.network.ServerFunctionsImpl
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber

/**
 * Repository handling the work with subscriptions.
 */
class DataRepository private constructor(
    private val serverFunctions: ServerFunctionsImpl,
    private val localDataSource: LocalDataSource
) {

    /**
     * [MediatorLiveData] to coordinate updates from the database and the network.
     *
     * The mediator observes multiple sources. The database source is immediately exposed.
     * The network source is stored in the database, which will eventually be exposed.
     * The mediator provides an easy way for us to use LiveData for both the local data source
     * and the network data source, without implementing a new callback interface.
     */
    val subscriptions = serverFunctions.subscriptions

    /**
     * Register subscription to this account
     */
    suspend fun registerSubscription(context: Context?, sku: String, purchaseToken: String, callback: (ServiceResponse) -> Unit) {
        withContext(Dispatchers.IO) {
            serverFunctions.registerSubscription(context, sku = sku, purchaseToken = purchaseToken, callback)
            saveSubscription()
        }
    }

    private suspend fun saveSubscription() {
        val subscriptionStatusList = subscriptions.value
        if (!subscriptionStatusList.isNullOrEmpty()) {
            for (subscriptionStatus in subscriptionStatusList) {
                subscriptionStatus.is_server_processed = true
            }
        }
        subscriptionStatusList?.let { localDataSource.updateAllSubscriptions(it) }
    }

    suspend fun saveAcknowledgment(purchaseAcknowledgmentStatusList: List<PurchaseAcknowledgmentStatus>) {
        withContext(Dispatchers.IO) {
            for (purchaseAcknowledgment in purchaseAcknowledgmentStatusList) {
                localDataSource.updateSubscription(purchaseToken = purchaseAcknowledgment.purchaseToken, isAcknowledged = purchaseAcknowledgment.isAcknowledged)
            }
        }
    }

    suspend fun checkForSubscriptionStatusAndUpdateDB(context: Context?, purchaseList: List<Purchase>) {
        try {
            withContext(Dispatchers.IO) {
                for (purchase in purchaseList) {
                    val subscriptionStatus = localDataSource.getSubscription(purchaseToken = purchase.purchaseToken)
                    if (subscriptionStatus.value == null) {
                        registerSubscription(context, purchase.skus[0], purchase.purchaseToken){}
                    }else {
                        subscriptionStatus.value?.let { status ->
                            if (status.sku != null && !status.is_server_processed)  {
                                val skuString = status.sku as String
                                registerSubscription(context, skuString, status.purchaseToken) {}
                            }
                        }
                    }
                }
            }
        }catch (ex: Exception) {
            Timber.d("Exception in checkForSubscriptionStatusAndUpdateDB : $ex")
        }
    }

    suspend fun checkForAcknowledgementAndUpdateDB(purchaseList: List<Purchase>, callback: (List<Purchase>) -> Unit) {
        val nonAcknowledgedPurchaseList = arrayListOf<Purchase>()
        try {
            withContext(Dispatchers.IO) {
                for (purchase in purchaseList) {
                    val subscriptionStatus = localDataSource.getSubscription(purchaseToken = purchase.purchaseToken)
                    subscriptionStatus.value?.let { status ->
                        if (status.is_server_processed && !status.is_purchase_acknowledged) {
                            nonAcknowledgedPurchaseList.add(purchase)
                        }
                    }
                }
            }
        }catch (ex: Exception) {
            Timber.d("Exception in checkForSubscriptionStatusAndUpdateDB : $ex")
        }
        callback(nonAcknowledgedPurchaseList)
    }

    companion object {

        @Volatile
        private var INSTANCE: DataRepository? = null

        fun getInstance(serverFunctions: ServerFunctionsImpl, localDataSource: LocalDataSource): DataRepository =
                INSTANCE ?: synchronized(this) {
                    INSTANCE
                            ?: DataRepository(serverFunctions, localDataSource)
                                    .also { INSTANCE = it }
                }
    }
}
