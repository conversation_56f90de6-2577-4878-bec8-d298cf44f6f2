package com.lovebeats.subscriptions.views

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.lovebeats.R
import com.lovebeats.databinding.ActivityPlusCelebrationBinding
import com.lovebeats.extensions.launchModeWithNoBackStack
import com.lovebeats.ui.home.browseProfiles.BrowseProfilesActivity
import com.lovebeats.utils.AppUtils
import nl.dionsegijn.konfetti.models.Shape
import nl.dionsegijn.konfetti.models.Size

class SubscriptionPlusCelebrationActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPlusCelebrationBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPlusCelebrationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.buttonLargeActiveButton.setOnClickListener {
            startProfileActivity()
        }

        startKonfetti()
    }

    private fun startKonfetti() {
        binding.viewKonfetti.build()
            .addColors(resources.getColor(R.color.grey4, null), resources.getColor(R.color.konfetti_color2, null))
            .setDirection(0.0, 359.0)
            .setSpeed(1f, 5f)
            .setFadeOutEnabled(true)
            .setTimeToLive(2000L)
            .addShapes(Shape.Square, Shape.Circle)
            .addSizes(Size(12))
            .setPosition(-50f, AppUtils.getScreenWidth(this) + 50f, -50f, -50f)
            .streamFor(300, 2000L)
    }

    private fun startProfileActivity() {
        val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
        this.startActivity(intent)
    }

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, SubscriptionPlusCelebrationActivity::class.java)
        }
    }
}