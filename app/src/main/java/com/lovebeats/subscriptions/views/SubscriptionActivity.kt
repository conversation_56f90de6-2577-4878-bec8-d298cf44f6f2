package com.lovebeats.subscriptions.views

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.android.billingclient.api.Purchase
import com.lovebeats.LoveBeatApplication
import com.lovebeats.R
import com.lovebeats.customViews.bottomSheet.CustomBottomSheet
import com.lovebeats.databinding.ActivitySubscriptionBinding
import com.lovebeats.extensions.launchModeWithNoBackStack
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.SubscriptionsListItem
import com.lovebeats.services.ServiceResponse
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.subscriptions.Constants
import com.lovebeats.subscriptions.billing.BillingClientLifecycle
import com.lovebeats.subscriptions.billing.isSubscriptionRestore
import com.lovebeats.subscriptions.viewModels.BillingViewModel
import com.lovebeats.ui.home.browseProfiles.BrowseProfilesActivity
import com.lovebeats.utils.AlertDialogView
import com.lovebeats.utils.ScreenRouter

class SubscriptionActivity : AppCompatActivity() {

    private lateinit var billingClientLifecycle: BillingClientLifecycle
    private lateinit var billingViewModel: BillingViewModel
    private lateinit var subscriptionsRecyclerAdapter: SubscriptionsListAdapter

    private lateinit var binding: ActivitySubscriptionBinding

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)

        binding = ActivitySubscriptionBinding.inflate(layoutInflater)
        setContentView(binding.root)

        billingViewModel = ViewModelProvider(this).get(BillingViewModel::class.java)

        billingClientLifecycle = (application as LoveBeatApplication).billingClientLifecycle

        binding.subscriptionBenefitsList.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        subscriptionsRecyclerAdapter = SubscriptionsListAdapter(listOf())
        binding.subscriptionBenefitsList.adapter = subscriptionsRecyclerAdapter

        getAndSetSubscriptionsList()

        binding.buttonJoinLovebeatPlus.setOnClickListener {
            showChooserDialog()
        }

        binding.closeButton.setOnClickListener {
            startBrowseProfilesScreen()
        }

        checkForSubscriptionRestore()

        val influencerId = AccountPreferences.getInstance(applicationContext).getStringValue(com.lovebeats.utils.Constants.influencerId, "")
        val influencerName = AccountPreferences.getInstance(applicationContext).getStringValue(com.lovebeats.utils.Constants.influencerName, "")

        if(influencerName.isNotEmpty()) {
            binding.subscriptionSubTitleText.visibility = View.VISIBLE
            binding.subscriptionSubTitleText.text = getString(R.string.influencer_offer_title, influencerName)
        }else {
            binding.subscriptionSubTitleText.visibility = View.GONE
        }

        // Register purchases when they change.
        billingClientLifecycle.purchaseUpdateEvent.observe(this) {
            it.let { purchaseList ->
                registerPurchases(it) { serviceResponse ->
                    if (serviceResponse.isSuccess()) {
                        billingClientLifecycle.acknowledgePurchases(purchaseList) { purchaseAcknowledgementStatusList ->
                            billingViewModel.saveAcknowledgment(purchaseAcknowledgementStatusList)
                        }
                    }
                }

                if (purchaseList.isNotEmpty()) {
                    val purchase = purchaseList[0]
                    when (purchase.purchaseState) {
                        Purchase.PurchaseState.PURCHASED -> {
                            startLoveBeatCelebrationActivity()

                            val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
                            if (!influencerId.isNullOrEmpty()) {
                                firebaseDatabaseReference.saveInfluencerSignupInFirebaseAndResetInPrefs(
                                    true,
                                    this@SubscriptionActivity
                                )
                            }
                        }

                        Purchase.PurchaseState.PENDING -> {
                            AlertDialogView.showAlertDialog(
                                context = this,
                                message = getString(R.string.purchase_pending),
                                buttonPositiveText = getString(R.string.okay),
                                buttonNegativeText = null
                            ) { _, which ->
                                if (which == DialogInterface.BUTTON_POSITIVE) {
                                    startBrowseProfilesScreen()
                                }
                            }
                        }

                        else -> {
                            AlertDialogView.showAlertDialog(
                                context = this,
                                message = getString(R.string.purchase_unspecified),
                                buttonPositiveText = getString(R.string.okay),
                                buttonNegativeText = null
                            ) { _, which ->
                                if (which == DialogInterface.BUTTON_POSITIVE) {
                                    startBrowseProfilesScreen()
                                }
                            }
                        }
                    }
                } else {
                    AlertDialogView.showAlertDialog(
                        context = this,
                        message = getString(R.string.purchase_unspecified),
                        buttonPositiveText = getString(R.string.okay),
                        buttonNegativeText = null
                    ) { _, which ->
                        if (which == DialogInterface.BUTTON_POSITIVE) {
                            startBrowseProfilesScreen()
                        }
                    }
                }
            }
        }

        // Launch the billing flow when the user clicks a button to buy something.
        billingViewModel.buyEvent.observe(this) {
            it.let {
                billingClientLifecycle.launchBillingFlow(this, it)
            }
        }
        setupTitle()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun setupTitle() {
        val subOriginIntent = intent.getStringExtra(SUBSCRIPTION_ORIGIN)
        if (subOriginIntent == OUT_OF_LIKES) {
            val spannableString: Spannable = SpannableString(getString(R.string.out_of_likes_subscription))
            spannableString.setSpan(ForegroundColorSpan(getColor(R.color.color_primary)), 45, 56, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            binding.subscriptionTitleText.text = spannableString
        }
    }

    private fun startLoveBeatCelebrationActivity() {
        val intent = SubscriptionPlusCelebrationActivity.newIntent(this).launchModeWithNoBackStack()
        startActivity(intent)
    }

    private fun startBrowseProfilesScreen() {
        val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
        this.startActivity(intent)
    }

    /**
     * Register SKUs and purchase tokens with the server.
     */
    private fun registerPurchases(
        purchaseList: List<Purchase>,
        callback: (ServiceResponse) -> Unit
    ) {
        for (purchase in purchaseList) {
            val sku = purchase.skus[0]
            val purchaseToken = purchase.purchaseToken
            billingViewModel.registerSubscription(
                sku = sku,
                purchaseToken = purchaseToken,
                callback
            )
        }
    }

    private fun getAndSetSubscriptionsList() {
        val list = listOf(
            SubscriptionsListItem(
                getDrawable(R.drawable.direct_messaging),
                getString(R.string.direct_messages_title),
                getString(R.string.direct_messages_desc)
            ),
            SubscriptionsListItem(
                getDrawable(R.drawable.passed_profiles),
                getString(R.string.passed_profiles_title),
                getString(R.string.passed_profiles_desc)
            ),
            SubscriptionsListItem(
                getDrawable(R.drawable.ic_woman_thinking),
                getString(R.string.subscription_all_likes),
                getString(R.string.subscription_all_likes_desc)
            ),
            SubscriptionsListItem(
                getDrawable(R.drawable.teleport_header),
                getString(R.string.subscription_teleport),
                getString(R.string.subscription_teleport_desc)
            ),
            SubscriptionsListItem(
                getDrawable(R.drawable.ic_likes),
                getString(R.string.subscription_unlimited_likes),
                getString(R.string.subscription_unlimited_likes_desc)
            ),
            SubscriptionsListItem(
                getDrawable(R.drawable.ic_read_receipts),
                getString(R.string.subscription_read_receipts),
                getString(R.string.subscription_read_receipts_desc)
            ),

        )
        subscriptionsRecyclerAdapter = SubscriptionsListAdapter(list)
        binding.subscriptionBenefitsList.adapter = subscriptionsRecyclerAdapter
    }

    private fun checkForSubscriptionRestore() {
        if (shouldShowSubscriptionRestoreAction().first) {
            binding.restoreTextView.visibility = View.VISIBLE
            binding.restoreTextView.setOnClickListener {
                manageSubscriptions(shouldShowSubscriptionRestoreAction().second)
            }
        }
    }

    private fun shouldShowSubscriptionRestoreAction(): Pair<Boolean, String?> {
        val subscriptionsList = billingViewModel.subscriptions.value
        if (subscriptionsList != null) {
            for (subscription in subscriptionsList) {
                if (isSubscriptionRestore(subscription)) {
                    return Pair(true, subscription.sku)
                }
            }
        }
        return Pair(false, null)
    }

    private fun showChooserDialog() {
        val optionsList: MutableList<CustomBottomSheet.Item> = arrayListOf()
        billingClientLifecycle.skusWithSkuDetails.observe(this) { skuList ->
            for ((key, value) in skuList) {
                when (key) {
                    Constants.MONTHLY_SKU -> {
                        optionsList.add(
                            CustomBottomSheet.Item(
                                getString(
                                    R.string.subscription_monthly_title,
                                    value.price
                                ),
                                ""
                            ) {
                                billingViewModel.buySubscription(Constants.MONTHLY_SKU)
                            })
                    }

                    Constants.THREE_MONTHS_SKU -> {
                        optionsList.add(
                            CustomBottomSheet.Item(
                                getString(
                                    R.string.subscription_three_months_title,
                                    value.price + " (save 50%)"
                                ),
                                ""
                            ) {
                                billingViewModel.buySubscription(Constants.THREE_MONTHS_SKU)
                            })
                    }

                    Constants.HALF_YEARLY_SKU -> {
                        optionsList.add(
                            CustomBottomSheet.Item(
                                getString(
                                    R.string.subscription_half_yearly_title,
                                    value.price + " (save 67%)"
                                ),
                                ""
                            ) {
                                billingViewModel.buySubscription(Constants.HALF_YEARLY_SKU)
                            })
                    }
                }
            }

            val sortedList = optionsList.sortedWith(compareBy { it.title })

            if (shouldShowSubscriptionRestoreAction().first) {
                CustomBottomSheet(
                    this,
                    sortedList,
                    getString(R.string.subscription_restore_title)
                ) {
                    manageSubscriptions(shouldShowSubscriptionRestoreAction().second)
                }.show()
            } else {
                CustomBottomSheet(this, sortedList).show()
            }
        }
    }

    private fun manageSubscriptions(sku: String?) {
        // Open the Play Store when this event is triggered.
        val url = if (sku == null) {
            // If the SKU is not specified, just open the Google Play subscriptions URL.
            Constants.PLAY_STORE_SUBSCRIPTION_URL
        } else {
            // If the SKU is specified, open the deeplink for this SKU on Google Play.
            String.format(Constants.PLAY_STORE_SUBSCRIPTION_DEEPLINK_URL, sku, packageName)
        }
        val intent = Intent(Intent.ACTION_VIEW)
        intent.data = Uri.parse(url)
        startActivity(intent)
    }

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, SubscriptionActivity::class.java)
        }
        const val INFLUENCER_FREE_TRAIL = "INFLUENCER_FREE_TRAIL"
        const val SUBSCRIPTION_ORIGIN = "SUBSCRIPTION_ORIGIN"
        const val OUT_OF_LIKES = "OUT_OF_LIKES"
    }
}