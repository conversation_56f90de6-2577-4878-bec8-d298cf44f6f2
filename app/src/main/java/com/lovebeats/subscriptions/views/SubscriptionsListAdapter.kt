package com.lovebeats.subscriptions.views

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.R
import com.lovebeats.models.SubscriptionsListItem

class SubscriptionsListAdapter(private val subscriptionsItemList: List<SubscriptionsListItem>) : RecyclerView.Adapter<SubscriptionsListAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.subscriptions_benefits_list_item, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = subscriptionsItemList[position]
        holder.bindItems(item)
    }

    override fun getItemCount(): Int = subscriptionsItemList.size

    inner class ViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
        fun bindItems(item: SubscriptionsListItem) {
            val title: TextView = view.findViewById(R.id.subscription_item_name)
            val desc: TextView = view.findViewById(R.id.subscription_item_desc)
            val image: ImageView = view.findViewById(R.id.subscription_item_image)
            title.text = item.title
            desc.text = item.desc
            item.image.let {
                image.setImageDrawable(item.image)
            }
        }
    }
}