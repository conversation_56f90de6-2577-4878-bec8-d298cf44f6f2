/*
 * Copyright 2018 Google LLC. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.lovebeats.subscriptions.network

import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.google.firebase.functions.FirebaseFunctions
import com.google.firebase.functions.FirebaseFunctionsException
import com.lovebeats.analytics.AttributionTrackingService
import com.lovebeats.analytics.MP_SUBSCRIPTION_REGISTRATION
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.models.UserObject
import com.lovebeats.services.ServiceResponse
import com.lovebeats.subscriptions.model.SubscriptionStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber

class ServerFunctionsImpl {

    /**
     * The latest subscription data from the Firebase server.
     *
     * Use this class by observing the subscriptions LiveData.
     * Any server updates will be communicated through this LiveData.
     */
    val subscriptions = MutableLiveData<List<SubscriptionStatus>>()

    /**
     * Expected errors.
     *
     * NOT_FOUND: Invalid SKU or purchase token.
     * ALREADY_OWNED: Subscription is claimed by a different user.
     * INTERNAL: Server error.
     */
    enum class ServerError {
        NOT_FOUND, ALREADY_OWNED, PERMISSION_DENIED, INTERNAL
    }

    /**
     * Singleton instance of the Firebase Functions API.
     */
    private val firebaseFunctions = FirebaseFunctions.getInstance()

    /**
     * Register a subscription with the server and posts successful results to [subscriptions].
     * This is the function that validates the purchase against google play.
     */
    suspend fun registerSubscription(context: Context?, sku: String, purchaseToken: String, callback: (ServiceResponse) -> Unit) {
        withContext(Dispatchers.IO) {
            Timber.tag(TAG).d("Calling: $REGISTER_SUBSCRIPTION_CALLABLE")
            val data = HashMap<String, String>().apply {
                put(SKU_KEY, sku)
                put(PURCHASE_TOKEN_KEY, purchaseToken)
            }
            firebaseFunctions
                    .getHttpsCallable(REGISTER_SUBSCRIPTION_CALLABLE)
                    .call(data)
                    .addOnCompleteListener { task ->
                        if (task.isSuccessful) {

                            MixPanelAnalyticsTrackingService.logApiSuccessEvent(context, MP_SUBSCRIPTION_REGISTRATION)

                            val result = (task.result?.getData() as? Map<String, Any>)?.let {
                                SubscriptionStatus.listFromMap(it)
                            }
                            Timber.tag(TAG).i("Subscription registration successful: $result")
                            if (result == null) {
                                Timber.tag(TAG).e("Invalid subscription registration data")
                            } else {
                                UserObject.isLoveBeatPlusUser = true
                                subscriptions.postValue(result)

                                // Log subscription success with attribution
                                context?.let { ctx ->
                                    AttributionTrackingService.logSubscriptionSuccess(ctx, sku)
                                }
                            }
                            callback(ServiceResponse.getServiceResponseOnSuccess())
                        } else {

                            MixPanelAnalyticsTrackingService.logApiFailedEvent(
                                context,
                                MP_SUBSCRIPTION_REGISTRATION,
                                task.exception?.message.toString()
                            )

                            callback(ServiceResponse.getServiceResponseOnError())

                            // Log subscription failure with attribution
                            val errorMessage = task.exception?.message ?: "Unknown error"
                            context?.let { ctx ->
                                AttributionTrackingService.logSubscriptionFailure(ctx, sku, errorMessage)
                            }

                            when (serverErrorFromFirebaseException(task.exception)) {
                                ServerError.NOT_FOUND -> {
                                    Timber.tag(TAG).e("Invalid SKU or purchase token during registration: ${task.exception}")
                                }
                                ServerError.ALREADY_OWNED -> {
                                    Timber.tag(TAG).i("Subscription already owned by another user: ${task.exception}")
                                }
                                ServerError.INTERNAL -> {
                                    Timber.tag(TAG).e("Subscription registration server error: ${task.exception}")
                                }
                                else -> {
                                    Timber.tag(TAG).e("Unknown error during subscription registration: ${task.exception}")
                                }
                            }
                        }
                    }
        }
    }

    /**
     * Convert Firebase error codes to the app-specific meaning.
     */
    private fun serverErrorFromFirebaseException(exception: Exception?): ServerError? {
        if (exception !is FirebaseFunctionsException) {
            Timber.tag(TAG).d("Unrecognized Exception: $exception")
            return null
        }
        val code = exception.code
        return when (exception.code) {
            FirebaseFunctionsException.Code.NOT_FOUND -> ServerError.NOT_FOUND
            FirebaseFunctionsException.Code.ALREADY_EXISTS -> ServerError.ALREADY_OWNED
            FirebaseFunctionsException.Code.PERMISSION_DENIED -> ServerError.PERMISSION_DENIED
            FirebaseFunctionsException.Code.INTERNAL -> ServerError.INTERNAL
            FirebaseFunctionsException.Code.RESOURCE_EXHAUSTED -> {
                Timber.tag(TAG).e("RESOURCE_EXHAUSTED: Check your server quota")
                ServerError.INTERNAL
            }
            else -> {
                Timber.tag(TAG).d("Unexpected Firebase Exception: $code")
                null
            }
        }
    }

    companion object {
        private const val TAG = "ServerImpl"

        // Keys for sending the cloud function in order to validate purchase
        private const val SKU_KEY = "sku"
        private const val PURCHASE_TOKEN_KEY = "token"

        // firebase cloud functions
        private const val REGISTER_SUBSCRIPTION_CALLABLE = "subscriptionRegistration"
    }

}
