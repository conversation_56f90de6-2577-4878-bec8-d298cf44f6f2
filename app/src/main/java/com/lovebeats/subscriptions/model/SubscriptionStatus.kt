package com.lovebeats.subscriptions.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "SubscriptionStatus")
data class SubscriptionStatus (
        var subAlreadyOwned: Boolean = false,
        var isLocalPurchase: Boolean = false,
        var is_server_processed: Boolean = false,
        var is_purchase_acknowledged: Boolean = false,

        // Remote fields.
        @PrimaryKey var purchaseToken: String,
        var subscriptionStatus: String? = null,
        var sku: String? = null,
        var isEntitlementActive: Boolean = false,
        var willRenew: Boolean = false,
        var activeUntilMillisec: Long = 0,
        var isFreeTrial: Boolean = false,
        var isGracePeriod: Boolean = false,
        var isAccountHold: Boolean = false,
        var isPaused: Boolean = false,
        var autoResumeTimeMillis: Long = 0
) {
    companion object {

        const val SUBSCRIPTIONS_KEY = "subscriptions"
        const val SKU_KEY = "sku"
        const val PURCHASE_TOKEN_KEY = "purchaseToken"
        const val IS_ENTITLEMENT_ACTIVE_KEY = "isEntitlementActive"
        const val WILL_RENEW_KEY = "willRenew"
        const val ACTIVE_UNTIL_MILLISEC_KEY = "activeUntilMillisec"
        const val IS_FREE_TRIAL_KEY = "isFreeTrial"
        const val IS_GRACE_PERIOD_KEY = "isGracePeriod"
        const val IS_ACCOUNT_HOLD_KEY = "isAccountHold"
        const val IS_PAUSED_KEY = "isPaused"
        const val AUTO_RESUME_TIME_MILLISEC_KEY = "autoResumeTimeMillis"

        /**
         * Parse subscription data from Map and return null if data is not valid.
         */
        fun listFromMap(map: Map<String, Any>): List<SubscriptionStatus>? {
            val subscriptions = ArrayList<SubscriptionStatus>()
            val subList = map[SUBSCRIPTIONS_KEY] as? ArrayList<Map<String, Any>> ?: return null

            for (subStatus in subList) {
                val purchaseToken = subStatus[PURCHASE_TOKEN_KEY] as? String
                purchaseToken?.let {
                    subscriptions.add(SubscriptionStatus(purchaseToken = it).apply {
                        (subStatus[SKU_KEY] as? String?)?.let {
                            sku = it
                        }
                        (subStatus[IS_ENTITLEMENT_ACTIVE_KEY] as? Boolean)?.let {
                            isEntitlementActive = it
                        }
                        (subStatus[WILL_RENEW_KEY] as? Boolean)?.let {
                            willRenew = it
                        }
                        (subStatus[ACTIVE_UNTIL_MILLISEC_KEY] as? Long)?.let {
                            activeUntilMillisec = it
                        }
                        (subStatus[IS_FREE_TRIAL_KEY] as? Boolean)?.let {
                            isFreeTrial = it
                        }
                        (subStatus[IS_GRACE_PERIOD_KEY] as? Boolean)?.let {
                            isGracePeriod = it
                        }
                        (subStatus[IS_ACCOUNT_HOLD_KEY] as? Boolean)?.let {
                            isAccountHold = it
                        }
                        (subStatus[IS_PAUSED_KEY] as? Boolean)?.let {
                            isPaused = it
                        }
                        (subStatus[AUTO_RESUME_TIME_MILLISEC_KEY] as? Long)?.let {
                            autoResumeTimeMillis = it
                        }
                    })
                }
            }
            return subscriptions
        }
    }
}

