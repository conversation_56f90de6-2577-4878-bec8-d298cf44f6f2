package com.lovebeats.ui.paidVersion.likesYou

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.view.View
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import com.lovebeats.R
import com.lovebeats.extensions.launchModeWithSingleTop
import com.lovebeats.models.UserObject
import com.lovebeats.subscriptions.views.SubscriptionActivity
import com.lovebeats.ui.home.BottomNavigationBarActivity
import com.lovebeats.utils.ScreenRouter

class UpgradeActivity : BottomNavigationBarActivity() {

    private lateinit var mLayoutContainer: FrameLayout
    private lateinit var mProgressBar: ProgressBar
    private lateinit var mLikedYouTextView: TextView
    private lateinit var upgradeButton: Button
    private lateinit var likesYouImageView: ImageView

    companion object {

        fun newIntent(context: Context): Intent {

            return Intent(context, UpgradeActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        mLayoutContainer = findViewById(R.id.layout_container)

        val upgradeLayout = View.inflate(this, R.layout.activity_upgrade, null)

        mLayoutContainer.addView(upgradeLayout)

        mProgressBar = findViewById(R.id.progressBar)
        mLikedYouTextView = findViewById(R.id.liked_you_text_view)
        upgradeButton = findViewById(R.id.button_upgrade)
        likesYouImageView = findViewById(R.id.likes_you_image_view)

        showLoading()

        getAllLikedAndSet()

        if (UserObject.isLoveBeatPlusUser == true) {
            val intent = LikesYouActivity.newIntent(this).launchModeWithSingleTop()
            startActivity(intent)
        }

        upgradeButton.setOnClickListener {
            val subIntent = SubscriptionActivity.newIntent(this)
            startActivity(subIntent)
        }

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun showLoading() {
        mProgressBar.visibility = View.VISIBLE
        mProgressBar.isIndeterminate = true
    }

    private fun hideLoading() {
        mProgressBar.isIndeterminate = false
        mProgressBar.visibility = View.GONE
    }

    override fun getBottomNavigationBarItem(): Int {

        return R.id.navigation_likes_you
    }

    private fun getAllLikedAndSet() {
        getAllLikedByUsersCount(true) { usersList ->
            if (!usersList.isNullOrEmpty()) {
                likesYouImageView.setImageResource(R.drawable.likes_you_woman_thinking)
                var spannedHeightString = SpannableString(getString(R.string.likes_you_no, usersList.size.toString()))
                if (usersList.size == 1) {
                    spannedHeightString = SpannableString(getString(R.string.one_person_likes_you_no))
                }

                mLikedYouTextView.text = spannedHeightString
            }
            hideLoading()
        }
    }
}
