package com.lovebeats.ui.paidVersion.changeLocation

import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.GenericTypeIndicator
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.firebase.database.FirebaseRetrieveSingleUserListenerInterface
import com.lovebeats.models.UnlockedLocation
import com.lovebeats.models.UnlockedLocationsResponse
import com.lovebeats.utils.Constants
import timber.log.Timber

object UnlockedLocationsApi {

    enum class TeleportPreference {
        CurrentLocation,
        UnlockedLocation
    }

    var unlockedLocationsList: MutableList<UnlockedLocation>? = null
    var userTeleportLocationPreference = MutableLiveData(TeleportPreference.CurrentLocation)

    fun getUnlockedLocations(context: Context, callback: (List<UnlockedLocation>?) -> Unit) {
        if (unlockedLocationsList != null) {
            callback(unlockedLocationsList)
        }else {
            val firebaseDatabaseUtil = FirebaseDatabaseUtil(context)
            firebaseDatabaseUtil.readCitiesInfoFromFirebase(object :
                FirebaseRetrieveSingleUserListenerInterface {
                override fun onSuccess(dataSnapshot: DataSnapshot) {
                    try {
                        for (postSnapshot in dataSnapshot.children) {
                            val genericTypeIndicator: GenericTypeIndicator<Map<String?, Any?>?> = object : GenericTypeIndicator<Map<String?, Any?>?>() {}
                            val cityInfoMap = postSnapshot.getValue(genericTypeIndicator)
                            val cityKey = postSnapshot.key
                            val city = cityInfoMap?.get(Constants.city)?.toString()
                            val state = cityInfoMap?.get(Constants.state)?.toString()
                            val lat = cityInfoMap?.get(Constants.lat) as Double
                            val lng = cityInfoMap[Constants.lng] as Double
                            if (unlockedLocationsList == null) {
                                unlockedLocationsList = mutableListOf()
                            }
                            val unlockedLocation = UnlockedLocation(lat, lng, city, state)
                            if (unlockedLocationsList != null && !unlockedLocationsList!!.contains(unlockedLocation)) {
                                unlockedLocationsList?.add(unlockedLocation)
                            }
                        }
                        callback(unlockedLocationsList)
                    }catch (ex: Exception) {
                        Timber.e("exception in getUnlockedLocations: $ex")
                        callback(null)
                    }
                }
                override fun onFailure() {
                    Timber.e("failed to getUnlockedLocations")
                    callback(null)
                }
            })
        }
    }

    @Deprecated("No longer using this cloud function api as we opened up all cities")
    fun getUnlockedLocationsFromCloudFunctions(callback: (List<UnlockedLocation>?) -> Unit) {
        try {
            if (unlockedLocationsList.isNullOrEmpty()) {
                val functions = Firebase.functions
                functions
                        .getHttpsCallable("getUnlockedLocations")
                        .call()
                        .addOnCompleteListener { task ->
                            if (task.isSuccessful) {
                                val result = Gson().fromJson(task.result?.getData()?.toString(), UnlockedLocationsResponse::class.java)
                                //unlockedLocationsList = result.unlockedLocations
                                callback(unlockedLocationsList)
                            } else {
                                callback(null)
                            }
                        }
            }else {
                callback(unlockedLocationsList)
            }
        } catch (exception: Exception) {
            Timber.e("Error in unlockedLocation: $exception")
            callback(null)
        }
    }
}