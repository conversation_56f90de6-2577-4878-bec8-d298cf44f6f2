package com.lovebeats.ui.paidVersion.likesYou

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration

class LikesYouItemDecoration(private val space: Int) : ItemDecoration() {

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {

        outRect.left = space
        outRect.bottom = space

        if (parent.getChildLayoutPosition(view) % 2 == 0) {
            outRect.right = 0
        } else {
            outRect.right = space
        }
    }
}