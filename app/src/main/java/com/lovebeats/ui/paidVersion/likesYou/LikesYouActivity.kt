package com.lovebeats.ui.paidVersion.likesYou

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import android.widget.ProgressBar
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.dateup.android.paidVersion.likesYou.LikesYouViewPagerAdapter
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.firebase.firestore.DocumentSnapshot
import com.lovebeats.R
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.firebase.database.FirebaseGetAllLikedByUsersListListener
import com.lovebeats.firebase.database.FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore
import com.lovebeats.models.LikesTabUser
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.SpacesImagesViewModel
import com.lovebeats.ui.home.BottomNavigationBarActivity
import com.lovebeats.ui.home.browseProfiles.OtherUserProfileModal.Companion.EXTRA_USER_FROM_OTHER_PROFILE_MODAL
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils

class LikesYouActivity : BottomNavigationBarActivity() {

    companion object {

        fun newIntent(context: Context): Intent {

            return Intent(context, LikesYouActivity::class.java)
        }
    }
    private lateinit var mLayoutContainer: FrameLayout


    override fun getBottomNavigationBarItem(): Int = R.id.navigation_likes_you

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        mLayoutContainer = findViewById(R.id.layout_container)

        val layout = View.inflate(this, R.layout.activity_likes_you, null)
        mLayoutContainer.addView(layout)

        val tabLayout = findViewById<TabLayout>(R.id.tab_layout)
        val viewPager = findViewById<ViewPager2>(R.id.view_pager)

        val adapter = LikesYouViewPagerAdapter(this)
        viewPager.adapter = adapter

        TabLayoutMediator(tabLayout, viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> "Likes You"
                1 -> "Passed"
                else -> ""
            }
        }.attach()
    }
}
