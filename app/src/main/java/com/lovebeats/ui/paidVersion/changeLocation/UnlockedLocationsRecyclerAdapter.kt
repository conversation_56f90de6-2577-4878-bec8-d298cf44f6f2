package com.lovebeats.ui.paidVersion.changeLocation

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.R
import com.lovebeats.models.UnlockedLocation
import com.lovebeats.models.UserObject

class UnlockedLocationsRecyclerAdapter(private val context: Context?, private val unlockedLocationsList: List<UnlockedLocation>, selectedItem:String, val fromSettings: Boolean) : RecyclerView.Adapter<UnlockedLocationsRecyclerAdapter.ViewHolder>() {

    private var selectedCity: String = selectedItem

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = if (fromSettings) {
            LayoutInflater.from(parent.context).inflate(R.layout.fragment_unlocked_locations_item_settings, parent, false)
        }else {
            LayoutInflater.from(parent.context).inflate(R.layout.fragment_unlocked_locations_item, parent, false)
        }
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = unlockedLocationsList[position]
        holder.bindItems(item)
    }


    override fun getItemCount(): Int = unlockedLocationsList.size

    inner class ViewHolder(val view: View) : RecyclerView.ViewHolder(view) {
        fun bindItems(item: UnlockedLocation) {
            val cityName: TextView = view.findViewById(R.id.city_name)
            cityName.text = item.city
            if (!item.state.isNullOrEmpty()) {
                cityName.text = "${item.city}, ${item.state}"
            }
            if (fromSettings) {
                val container: RelativeLayout = view.findViewById(R.id.other_location_item_container)
                val tickerIcon: ImageView = view.findViewById(R.id.ticker_icon)
                if (selectedCity == item.city) {
                    tickerIcon.visibility = View.VISIBLE
                }else {
                    tickerIcon.visibility = View.GONE
                }
                container.setOnClickListener {
                    updateLocation(item)
                }
            }else {
                val container: LinearLayout = view.findViewById(R.id.other_location_item_container)
                val cityImage: ImageView = view.findViewById(R.id.city_image)
                container.setOnClickListener {
                    updateLocation(item)
                }
            }
        }

        fun updateLocation(item: UnlockedLocation) {
            selectedCity = item.city?: ""
            UnlockedLocationsApi.userTeleportLocationPreference.postValue(UnlockedLocationsApi.TeleportPreference.UnlockedLocation)
            notifyDataSetChanged()
            UserObject.selectedUnlockedLocation = item
            UserObject.userPreferencesChangedLiveData.postValue(true)
        }
    }
}