package com.lovebeats.ui

import android.app.Application
import android.content.Context
import android.graphics.Bitmap
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.spaces.SpacesRepository
import com.lovebeats.spaces.SpacesRepository.Companion.isSpacesConfigValid
import kotlinx.coroutines.launch

class SpacesImagesViewModel(application: Application) : AndroidViewModel(application) {

    private val spacesRepository by lazy { SpacesRepository() }

    init {
        if (!isSpacesConfigValid()) {
            val firebaseDatabaseUtil = FirebaseDatabaseUtil(application)
            firebaseDatabaseUtil.getSpacesConfigs()
        }
    }

    fun getImageUrl(imagePath: String, callback: (String?, String?) -> Unit) {
        viewModelScope.launch {
            spacesRepository.getImageUrl(imagePath, callback)
        }
    }

    fun uploadBitmap(context: Context, uid: String, fileName: String, bitmap: Bitmap, callback: (SpacesRepository.SpacesStatus) -> Unit) {
        viewModelScope.launch {
            spacesRepository.uploadBitmap(context, uid, fileName, bitmap, callback)
        }
    }

    fun deleteImage(imagePath: String, callback: (SpacesRepository.SpacesStatus) -> Unit) {
        viewModelScope.launch {
            spacesRepository.delete(imagePath, callback)
        }
    }
}