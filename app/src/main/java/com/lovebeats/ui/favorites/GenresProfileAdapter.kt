package com.lovebeats.ui.favorites

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.databinding.ItemGenresProfileBinding

class GenresProfileAdapter(private var genres: List<String>, val context: Context) : RecyclerView.Adapter<GenresProfileAdapter.FavoritesViewHolder>() {
    override fun onBindViewHolder(holder: FavoritesViewHolder, position: Int) {
        holder.genreButton.text = genres[position]
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FavoritesViewHolder {
        val itemBinding = ItemGenresProfileBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return FavoritesViewHolder(itemBinding)
    }

    override fun getItemCount(): Int {
        return genres.size
    }
    class FavoritesViewHolder(itemView: ItemGenresProfileBinding) : RecyclerView.ViewHolder(itemView.root) {
        val genreButton: TextView = itemView.genreButton
    }
}