package com.lovebeats.ui.favorites

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.lovebeats.R
import com.lovebeats.databinding.ActivityGenresBinding
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.home.settings.EditProfileModalActivity
import com.lovebeats.ui.onboarding.Icebreaker1Activity
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils.Companion.getMusicGenres

class MusicListActivity : AppCompatActivity() {

    private lateinit var binding: ActivityGenresBinding
    private lateinit var genresListRecyclerAdapter: GenresListRecyclerAdapter
    var selectedGenres = mutableSetOf<String>()

    companion object {

        const val EXTRA_FROM_SETTINGS = "EXTRA_FROM_SETTINGS"
        fun newIntent(context: Context): Intent {
            return Intent(context, MusicListActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGenresBinding.inflate(layoutInflater)
        setContentView(binding.root)
        init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {
        binding.accountHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        binding.header.text = getString(R.string.genres_title_music)

        val fromSettings = intent.getBooleanExtra(EXTRA_FROM_SETTINGS, false)
        if (fromSettings) {
            selectedGenres = AccountPreferences.getInstance(this).getStringSetValue(Constants.musicGenres, mutableSetOf())
            if (selectedGenres.size >= 3) {
                binding.buttonNext.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                binding.buttonNext.isEnabled = true
            }
        }

        val layoutManager = FlexboxLayoutManager(this)
        layoutManager.flexDirection = FlexDirection.ROW
        layoutManager.justifyContent = JustifyContent.FLEX_START

        binding.favoritesViewRecyclerView.layoutManager = layoutManager
        genresListRecyclerAdapter = GenresListRecyclerAdapter(getMusicGenres(), this, selectedGenres)
        binding.favoritesViewRecyclerView.adapter = genresListRecyclerAdapter
        genresListRecyclerAdapter.setOnItemClickListener(object : GenresListRecyclerAdapter.OnItemClickListener {
            override fun onClick(view: View, data: Set<String>) {
                selectedGenres = mutableSetOf()
                selectedGenres.addAll(data)
            }

            override fun enableDisableButton(enable: Boolean) {
                if (enable) {
                    binding.buttonNext.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                    binding.buttonNext.isEnabled = true
                } else {
                    binding.buttonNext.setBackgroundResource(R.drawable.bottom_button_disabled_state)
                    binding.buttonNext.isEnabled = false
                }
            }
        })

        if (fromSettings) {
            binding.buttonNext.text = getString(R.string.save)
            binding.buttonNext.setOnClickListener {
                if (binding.buttonNext.isEnabled) {
                    binding.progressBar.visibility = View.VISIBLE
                    updateFavoriteToFirebase(selectedGenres)
                    Handler(Looper.getMainLooper()).postDelayed({
                        binding.progressBar.visibility = View.GONE
                        val intent = Intent(this, EditProfileModalActivity::class.java)
                        startActivity(intent)
                    }, 2000)
                }
            }
        } else {
            binding.buttonNext.setOnClickListener {
                if (binding.buttonNext.isEnabled) {
                    updateFavoriteToFirebase(selectedGenres)
                    startActivity(Icebreaker1Activity.newIntent(this))
                }
            }
        }
    }

    private fun updateFavoriteToFirebase(favorite: Set<String>) {
        AccountPreferences.getInstance(this).setValue(Constants.musicGenres, favorite)
        val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
        firebaseDatabaseUtil.saveUserInfoToFirebase(Constants.musicGenres, favorite)
    }
}
