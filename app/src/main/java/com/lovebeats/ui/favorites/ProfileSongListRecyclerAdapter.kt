package com.lovebeats.ui.favorites

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.databinding.FavoritesTypeBinding
import com.lovebeats.glide.GlideApp
import com.lovebeats.models.Song

class ProfileSongListRecyclerAdapter(private var songs: ArrayList<Song>, val context: Context) : RecyclerView.Adapter<ProfileSongListRecyclerAdapter.FavoritesViewHolder>() {

    private lateinit var listener: OnItemClickListener

    override fun onBindViewHolder(holder: FavoritesViewHolder, position: Int) {
        holder.name.text = songs[position].title
        GlideApp.with(context)
            .load(songs[position].imageUrl)
            .into(holder.image)

        holder.itemView.setOnClickListener {
            listener.onClick(it, songs[position])
        }
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FavoritesViewHolder {
        val itemBinding = FavoritesTypeBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        setOnItemClickListener(listener)
        return FavoritesViewHolder(itemBinding)
    }

    override fun getItemCount(): Int {
        return songs.size
    }

    interface OnItemClickListener {
        fun onClick(view: View, data: Song)
    }

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.listener = listener
    }

    fun updateData(list: ArrayList<Song>) {
        songs = list
        notifyDataSetChanged()
    }

    fun filterList(filterlist: ArrayList<Song>) {
        songs = filterlist
        notifyDataSetChanged()
    }

    class FavoritesViewHolder(itemView: FavoritesTypeBinding) : RecyclerView.ViewHolder(itemView.root) {
        val name: TextView = itemView.favoriteName
        val image: ImageView = itemView.favoriteImageView
    }
}