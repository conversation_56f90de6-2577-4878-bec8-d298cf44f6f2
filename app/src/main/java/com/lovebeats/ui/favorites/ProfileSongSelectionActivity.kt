package com.lovebeats.ui.favorites

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.LinearLayoutManager
import com.lovebeats.analytics.AnalyticsTrackingService
import com.lovebeats.analytics.AttributionTrackingService
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.analytics.ONBOARDING_COMPLETE
import com.lovebeats.databinding.ProfileSongLayoutBinding
import com.lovebeats.extensions.launchModeWithNoBackStack
import com.lovebeats.firebase.cloudFunctions.ProfileSongApi
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.Song
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.home.browseProfiles.BrowseProfilesActivity
import com.lovebeats.ui.onboarding.ActorFavoriteActivity
import com.lovebeats.utils.Constants
import com.lovebeats.utils.Constants.songId
import com.lovebeats.utils.Constants.songImageUrl
import com.lovebeats.utils.Constants.songSubTitle
import com.lovebeats.utils.Constants.songTitle
import com.lovebeats.utils.Constants.songUrl
import com.lovebeats.utils.ScreenRouter
import java.util.Locale

class ProfileSongSelectionActivity : AppCompatActivity() {
    private lateinit var binding: ProfileSongLayoutBinding
    private var songsData: MutableSet<Song> = mutableSetOf()
    private lateinit var profileSongListRecyclerAdapter: ProfileSongListRecyclerAdapter
    val firebaseUid: String = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "") ?: ""
    companion object {

        const val TAG = ScreenRouter.PROFILE_SONG_SELECTION_ACTIVITY

        const val EXTRA_PROFILE_SONG_ONBOARDING = "EXTRA_PROFILE_SONG_ONBOARDING"
        fun newIntent(context: Context): Intent {
            return Intent(context, ProfileSongSelectionActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ProfileSongLayoutBinding.inflate(layoutInflater)
        setContentView(binding.root)

        init()

        ScreenRouter.setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, ProfileSongSelectionActivity::class.java.simpleName)
    }

    private fun init() {

        startLoading()

        setInitialData()

        binding.accountHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener,
            android.widget.SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(text: String?): Boolean {
                if (!text.isNullOrEmpty()) {
                    getData(text)
                }
                return false
            }

            override fun onQueryTextChange(msg: String): Boolean {
                if (msg.length > 6) {
                    filter(msg)
                }
                return false
            }
        })
    }

    private fun startLoading() {
        binding.progressBar.visibility = View.VISIBLE
    }

    private fun hideLoading() {
        binding.progressBar.visibility = View.GONE
    }

    private fun setInitialData() {
        ProfileSongApi.getProfileSongs(this, firebaseUid, "") {response ->
            response?.let { songsData.addAll(it) }
            hideLoading()
            binding.songsViewRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
            profileSongListRecyclerAdapter = ProfileSongListRecyclerAdapter(ArrayList(songsData), this)
            binding.songsViewRecyclerView.adapter = profileSongListRecyclerAdapter
            profileSongListRecyclerAdapter.setOnItemClickListener(object : ProfileSongListRecyclerAdapter.OnItemClickListener {
                override fun onClick(view: View, data: Song) {
                    updateSongToFirebase(data)
                }
            })
        }
    }

    private fun getData(searchText: String) {
        songsData = mutableSetOf()
        startLoading()
        ProfileSongApi.getProfileSongs(this, firebaseUid, searchText) {response ->
            response?.let { songsData.addAll(it) }
            hideLoading()
            profileSongListRecyclerAdapter.updateData(ArrayList(songsData))
        }
    }

    private fun updateSongToFirebase(song: Song) {
        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        val songDataMap: HashMap<String, String?> = HashMap()
        songDataMap[songId] = song.id
        songDataMap[songTitle] = song.title
        songDataMap[songUrl] = song.audioUrl
        songDataMap[songSubTitle] = song.subTitle
        songDataMap[songImageUrl] = song.imageUrl
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.profileSong, songDataMap)

        val fromOnboarding = intent.getBooleanExtra(EXTRA_PROFILE_SONG_ONBOARDING, false)
        if (fromOnboarding) {
            startActorFavoriteActivity()
            setOnboardingCompleteFlag()
        } else {
            val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
            this.startActivity(intent)
        }
    }

    private fun filter(text: String) {
        val filteredlist: MutableSet<Song> = mutableSetOf()
        for (item in songsData) {
            if (item.title?.lowercase(Locale.getDefault())?.contains(text.lowercase(Locale.getDefault())) == true) {
                filteredlist.add(item)
            }
        }
        if (filteredlist.isNotEmpty()) {
            profileSongListRecyclerAdapter.filterList(ArrayList(filteredlist))
        }
    }

    private fun startActorFavoriteActivity() {
        val intent = Intent(this, ActorFavoriteActivity::class.java)
        startActivity(intent)
    }

    private fun setOnboardingCompleteFlag() {
        AccountPreferences.getInstance(this).setValue(Constants.isOnboardingComplete, true)
        val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
        firebaseDatabaseUtil.saveUserInfoToFirebase(Constants.isOnboardingComplete, true)

        // Log onboarding completion with attribution
        AttributionTrackingService.logOnboardingComplete(this)
    }
}
