package com.lovebeats.ui.favorites

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.LinearLayoutManager
import com.lovebeats.databinding.ActivityFavoritesBinding
import com.lovebeats.firebase.cloudFunctions.Favorites
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.Favorite
import com.lovebeats.models.FavoritesType
import com.lovebeats.ui.home.settings.EditProfileModalActivity
import com.lovebeats.utils.ScreenRouter
import java.util.Locale


class FavoritesActivity : AppCompatActivity() {

    private lateinit var binding: ActivityFavoritesBinding

    private val favoritesData: ArrayList<Favorite> = ArrayList()
    private lateinit var favoritesListRecyclerAdapter: FavoritesListRecyclerAdapter
    private var favoriteType: FavoritesType? = null

    companion object {

        const val EXTRA_FAVORITE = "EXTRA_FAVORITE"
        fun newIntent(context: Context): Intent {
            return Intent(context, FavoritesActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFavoritesBinding.inflate(layoutInflater)
        setContentView(binding.root)

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, FavoritesActivity::class.java.simpleName)
    }

    private fun init() {

        startLoading()

        favoriteType = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent.getSerializableExtra(EXTRA_FAVORITE, FavoritesType::class.java)
        } else {
            intent.getSerializableExtra(EXTRA_FAVORITE) as? FavoritesType
        }

        favoriteType?.let {
            binding.titleTextView.text = "Update ${it.name.replaceFirstChar(Char::titlecase)}"
            getData(it)
        }

        binding.accountHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener,
            android.widget.SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(p0: String?): Boolean {
                return false
            }

            override fun onQueryTextChange(msg: String): Boolean {
                filter(msg)
                return false
            }
        })
    }

    private fun startLoading() {
        binding.progressBar.visibility = View.VISIBLE
    }

    private fun hideLoading() {
        binding.progressBar.visibility = View.GONE
    }

    private fun getData(favoriteType: FavoritesType) {

        Favorites.getFavoritesData(this, favoriteType.name) {response ->

            response.let { favoritesData.addAll(it) }

            if (favoritesData.isEmpty()) {
                binding.searchView.visibility = View.GONE
            } else {
                binding.searchView.visibility = View.VISIBLE
            }

            hideLoading()

            binding.favoritesViewRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
            favoritesListRecyclerAdapter = FavoritesListRecyclerAdapter(favoritesData, this)
            binding.favoritesViewRecyclerView.adapter = favoritesListRecyclerAdapter
            favoritesListRecyclerAdapter.setOnItemClickListener(object : FavoritesListRecyclerAdapter.OnItemClickListener {
                override fun onClick(view: View, data: Favorite) {
                    updateFavoriteToFirebase(data)
                }
            })
        }
    }

    private fun updateFavoriteToFirebase(favorite: Favorite) {
        // Key is favorite ID, Value is favorite name in array list and at 0 index
        val favoriteDataMap: HashMap<String, List<String>> = HashMap()
        favoriteDataMap[favorite.id] = listOf(favorite.name, favorite.imageUrl)
        favoriteType?.let { type ->

            startLoading()

            val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
            firebaseDatabaseReference.saveUserFavoriteInfoToFirebase(type.name, favoriteDataMap)

            Handler(Looper.getMainLooper()).postDelayed({
                hideLoading()
                val intent = Intent(this, EditProfileModalActivity::class.java)
                startActivity(intent)
            }, 2000)
        }
    }

    private fun filter(text: String) {
        val filteredlist: ArrayList<Favorite> = ArrayList()
        for (item in favoritesData) {
            if (item.name.lowercase(Locale.getDefault()).contains(text.lowercase(Locale.getDefault()))) {
                filteredlist.add(item)
            }
        }
        if (filteredlist.isEmpty()) {
            binding.requestFavoriteLayout.visibility = View.VISIBLE
            favoritesListRecyclerAdapter.filterList(arrayListOf())
            binding.buttonEnableLocation.setOnClickListener {
                val text = binding.favoriteEditText.text.toString()
                val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
                if (text.isNotEmpty()) {
                    firebaseDatabaseUtil.saveRequestedFavorite(text)
                }
            }
        } else {
            binding.requestFavoriteLayout.visibility = View.GONE
            favoritesListRecyclerAdapter.filterList(filteredlist)
        }
    }
}
