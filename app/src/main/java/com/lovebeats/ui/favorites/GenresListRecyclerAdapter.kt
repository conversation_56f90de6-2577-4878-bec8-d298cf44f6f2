package com.lovebeats.ui.favorites

import android.content.Context
import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.R
import com.lovebeats.databinding.ItemGenresBinding

class GenresListRecyclerAdapter(private var genres: List<String>, val context: Context, private val selectedGenres: MutableSet<String>) : RecyclerView.Adapter<GenresListRecyclerAdapter.FavoritesViewHolder>() {

    private lateinit var listener: OnItemClickListener
    override fun onBindViewHolder(holder: FavoritesViewHolder, position: Int) {
        holder.genreButton.text = genres[position]
        if (selectedGenres.contains(genres[position])) {
            buttonSelected(holder)
        } else {
            buttonNotSelected(holder)
        }
        holder.genreButton.setOnClickListener {
            updateList(holder, genres[position])
            listener.onClick(it, selectedGenres)
        }
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FavoritesViewHolder {
        val itemBinding = ItemGenresBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        setOnItemClickListener(listener)
        return FavoritesViewHolder(itemBinding)
    }

    override fun getItemCount(): Int {
        return genres.size
    }

    private fun buttonSelected(holder: FavoritesViewHolder) {
        holder.genreButton.backgroundTintList = ColorStateList.valueOf(context.getColor(R.color.color_primary))
        holder.genreButton.setTextColor(context.getColor(R.color.white))
    }

    private fun buttonNotSelected(holder: FavoritesViewHolder) {
        holder.genreButton.backgroundTintList = ColorStateList.valueOf(context.getColor(com.google.android.material.R.color.mtrl_btn_transparent_bg_color))
        holder.genreButton.setTextColor(context.getColor(R.color.grey2))
    }

    private fun updateList(holder: FavoritesViewHolder, data: String) {
        if (selectedGenres.size < 5) {
            if (selectedGenres.contains(data)) {
                selectedGenres.remove(data)
                buttonNotSelected(holder)
            } else {
                selectedGenres.add(data)
                buttonSelected(holder)
            }
        } else if (selectedGenres.size == 5) {
            if (selectedGenres.contains(data)) {
                selectedGenres.remove(data)
                buttonNotSelected(holder)
            }
        }

        if (selectedGenres.size <= 2) {
            listener.enableDisableButton(false)
        } else if (selectedGenres.size in 3..5) {
            listener.enableDisableButton(true)
        }
    }

    interface OnItemClickListener {
        fun onClick(view: View, data: Set<String>)
        fun enableDisableButton(enable: Boolean)
    }

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.listener = listener
    }
    class FavoritesViewHolder(itemView: ItemGenresBinding) : RecyclerView.ViewHolder(itemView.root) {
        val genreButton: TextView = itemView.genreButton

    }
}