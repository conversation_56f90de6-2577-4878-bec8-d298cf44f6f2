package com.lovebeats.ui.favorites

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.request.RequestOptions
import com.lovebeats.R
import com.lovebeats.databinding.FavoritesTypeBinding
import com.lovebeats.glide.GlideApp
import com.lovebeats.models.Favorite

class FavoritesListRecyclerAdapter(private var favorites: ArrayList<Favorite>, val context: Context) : RecyclerView.Adapter<FavoritesListRecyclerAdapter.FavoritesViewHolder>() {

    private lateinit var listener: OnItemClickListener

    override fun onBindViewHolder(holder: FavoritesViewHolder, position: Int) {
        holder.name.text = favorites[position].name
        GlideApp.with(context)
            .load(favorites[position].imageUrl)
            .placeholder(R.drawable.profile_user)
            .error(R.drawable.profile_user)
            .apply(RequestOptions.circleCropTransform())
            .into(holder.image)

        holder.itemView.setOnClickListener {
            listener.onClick(it, favorites[position])
        }
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FavoritesViewHolder {
        val itemBinding = FavoritesTypeBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        setOnItemClickListener(listener)
        return FavoritesViewHolder(itemBinding)
    }

    override fun getItemCount(): Int {
        return favorites.size
    }

    interface OnItemClickListener {
        fun onClick(view: View, data: Favorite)
    }

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.listener = listener
    }

    fun filterList(filterlist: ArrayList<Favorite>) {
        favorites = filterlist
        notifyDataSetChanged()
    }

    class FavoritesViewHolder(itemView: FavoritesTypeBinding) : RecyclerView.ViewHolder(itemView.root) {
        val name: TextView = itemView.favoriteName
        val image: ImageView = itemView.favoriteImageView
    }
}