package com.lovebeats.ui.favorites

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.request.RequestOptions
import com.lovebeats.R
import com.lovebeats.databinding.ItemFavoriteProfileBinding
import com.lovebeats.glide.GlideApp
import com.lovebeats.models.Favorite

class FavoritesProfileListRecyclerAdapter(private var favorites: ArrayList<Favorite>, val context: Context) : RecyclerView.Adapter<FavoritesProfileListRecyclerAdapter.FavoritesViewHolder>() {
    override fun onBindViewHolder(holder: FavoritesViewHolder, position: Int) {
        holder.name.text = favorites[position].name
        GlideApp.with(context)
            .load(favorites[position].imageUrl)
            .placeholder(R.drawable.profile_user)
            .error(R.drawable.profile_user)
            .apply(RequestOptions.circleCropTransform())
            .into(holder.image)
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FavoritesViewHolder {
        val itemBinding = ItemFavoriteProfileBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return FavoritesViewHolder(itemBinding)
    }

    override fun getItemCount(): Int {
        return favorites.size
    }

    class FavoritesViewHolder(itemView: ItemFavoriteProfileBinding) : RecyclerView.ViewHolder(itemView.root) {
        val name: TextView = itemView.favoriteName
        val image: ImageView = itemView.favoriteImageView
    }
}