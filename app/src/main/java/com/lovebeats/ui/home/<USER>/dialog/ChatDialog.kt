package com.lovebeats.ui.home.chat.dialog

import com.stfalcon.chatkit.commons.models.IDialog

class ChatDialog(private val chatId: String,
                 private val dialogName: String,
                 private val users: ArrayList<ChatUser>?,
                 private var lastMessage: ChatMessage,
                 private val dialogPhotoUrl: String,
                 var messageReadStatus: Boolean?,
                 val matchStatus: Boolean?,
                 val matchedTimeStamp: Long,
                 val gender: String?,
                 val conversationChatId: String?,
                 val dmRequest: Boolean = false,
                 val dmRejected: Boolean? = null,
                 val dmRequestBy: String? = null) : IDialog<ChatMessage> {

    override fun getId(): String {
        return chatId
    }

    override fun getDialogPhoto(): String {
        return dialogPhotoUrl
    }

    override fun getDialogName(): String {
        return dialogName
    }

    override fun getUsers(): ArrayList<ChatUser>? {

        return users
    }

    override fun getLastMessage(): ChatMessage {
        return lastMessage
    }

    override fun setLastMessage(lastMessage: ChatMessage) {
        this.lastMessage = lastMessage
    }

    override fun getUnreadCount(): Int {
        return 0
    }

    fun setMessageUnreadStatus(messageUnreadStatus: Boolean) {
        messageReadStatus = messageUnreadStatus
    }
}