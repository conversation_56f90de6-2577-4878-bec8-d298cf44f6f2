package com.lovebeats.ui.home.settings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageButton
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentTransaction
import com.lovebeats.R
import com.lovebeats.firebase.cloudFunctions.UserPreferencesUpdatedFunc.makeUserPreferencesUpdatedAPICall
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.InterestedInGender
import com.lovebeats.models.UserObject
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.Constants

class SettingsSexPreferenceFragment : Fragment() {

    private lateinit var preferenceMenButton: Button
    private lateinit var preferenceWoMenButton: Button
    private lateinit var preferenceBothButton: Button
    private lateinit var preferenceTopBackButton: ImageButton

    private lateinit var preferencesFragment: PreferencesFragment

    private var interestedIn: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        preferencesFragment = PreferencesFragment()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_settings_sex_preference, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        init(view)
    }

    private fun init(layout: View) {

        val interestedInFromPrefs = AccountPreferences.getInstance(activity).getStringValue(
            Constants.interestedIn, "")

        preferenceMenButton = layout.findViewById(R.id.button_preference_man)
        preferenceWoMenButton = layout.findViewById(R.id.button_preference_woman)
        preferenceBothButton = layout.findViewById(R.id.button_preference_both)
        preferenceTopBackButton = layout.findViewById(R.id.notifications_header_left_image_view)

        when (interestedInFromPrefs) {
            InterestedInGender.man.toString() -> {
                preferenceMenButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                preferenceWoMenButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
                preferenceBothButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
            }
            InterestedInGender.woman.toString() -> {
                preferenceMenButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
                preferenceBothButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
                preferenceWoMenButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
            }
            InterestedInGender.everyone.toString() -> {
                preferenceMenButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
                preferenceWoMenButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
                preferenceBothButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
            }
        }

        preferenceMenButton.setOnClickListener {
            interestedIn = InterestedInGender.man.toString()
            savePreferences()
            onRadioMenButtonPressed()
        }

        preferenceWoMenButton.setOnClickListener {
            interestedIn = InterestedInGender.woman.toString()
            savePreferences()
            onRadioWoManButtonPressed()
        }

        preferenceBothButton.setOnClickListener {
            interestedIn = InterestedInGender.everyone.toString()
            savePreferences()
            onRadioBothButtonPressed()
        }

        preferenceTopBackButton.setOnClickListener {

            fragmentManager?.beginTransaction()?.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE)?.replace(R.id.main_container, preferencesFragment)?.commit()
        }
    }

    private fun onRadioMenButtonPressed() {
        preferenceMenButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        preferenceWoMenButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        preferenceBothButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        interestedIn = InterestedInGender.man.toString()
    }

    private fun onRadioWoManButtonPressed() {
        preferenceMenButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        preferenceBothButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        preferenceWoMenButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        interestedIn = InterestedInGender.woman.toString()
    }

    private fun onRadioBothButtonPressed() {
        preferenceMenButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        preferenceWoMenButton.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        preferenceBothButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        interestedIn = InterestedInGender.everyone.toString()
    }

    private fun savePreferences() {

        AccountPreferences.getInstance(activity).setValue(Constants.interestedIn, interestedIn)

        val firebaseDatabaseReference = activity?.let { FirebaseDatabaseUtil(it) }
        firebaseDatabaseReference?.saveUserInfoToFirebase(Constants.interestedIn, interestedIn)

        activity?.let {
            setDefaultHeights(it)
        }

        UserObject.userPreferencesChangedLiveData.postValue(true)

        val mainUserId = AccountPreferences.getInstance(activity).getStringValue(Constants.firebaseUserId, "")
        makeUserPreferencesUpdatedAPICall(activity, mainUserId)
    }

    private fun setDefaultHeights(context: FragmentActivity) {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(context)

        when (interestedIn) {
            InterestedInGender.man.toString() -> {
                //setting default match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)

                AccountPreferences.getInstance(context).setValue(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                AccountPreferences.getInstance(context).setValue(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)
            }
            InterestedInGender.woman.toString() -> {
                //setting default match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)

                AccountPreferences.getInstance(context).setValue(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                AccountPreferences.getInstance(context).setValue(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)
            }
            else -> {
                //setting default match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)

                AccountPreferences.getInstance(context).setValue(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                AccountPreferences.getInstance(context).setValue(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)
            }
        }
    }
}
