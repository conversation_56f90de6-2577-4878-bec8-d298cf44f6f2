package com.lovebeats.ui.home.chat.dialog

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.request.RequestOptions
import com.google.firebase.database.ChildEventListener
import com.google.firebase.database.DataSnapshot
import com.google.firebase.database.DatabaseReference
import com.lovebeats.R
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.firebase.database.FirebaseRetrieveAllMatchesListenerInterface
import com.lovebeats.firebase.database.FirebaseRetrieveChildListener
import com.lovebeats.firebase.database.FirebaseRetrieveSingleUserListenerInterfaceFirestore
import com.lovebeats.glide.GlideApp
import com.lovebeats.models.User
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.SpacesImagesViewModel
import com.lovebeats.ui.blanks.BlankMatchesFragment
import com.lovebeats.ui.home.browseProfiles.OtherUserProfileModal
import com.lovebeats.ui.home.chat.message.MessageListActivity
import com.lovebeats.utils.AppUtils.Companion.appLocale
import com.lovebeats.utils.Constants
import com.lovebeats.utils.Utils
import com.lovebeats.utils.Utils.Companion.getDateFromTimestamp
import com.lovebeats.utils.Utils.Companion.isThisWeek
import com.stfalcon.chatkit.commons.ImageLoader
import com.stfalcon.chatkit.dialogs.DialogsList
import com.stfalcon.chatkit.dialogs.DialogsListAdapter
import com.stfalcon.chatkit.dialogs.DialogsListAdapter.OnDialogClickListener
import com.stfalcon.chatkit.utils.DateFormatter
import timber.log.Timber
import java.util.Calendar
import java.util.Date
import java.util.Objects
import kotlin.apply
import kotlin.text.compareTo

class ChatDialogListFragment : Fragment(), OnDialogClickListener<ChatDialog> {

    private var dialogsList: DialogsList? = null
    private var imageLoader: ImageLoader? = null
    private var dialogsAdapter: DialogsListAdapter<ChatDialog>? = null
    private var firebaseDatabaseUtil: FirebaseDatabaseUtil? = null
    private var chatUsersList: java.util.ArrayList<ChatUser>? = null
    private var chatDialogs: java.util.ArrayList<ChatDialog>? = null
    private var chatDialog: ChatDialog? = null
    private var conversationId: String? = null
    private var userProfilePhotoUrl = ""
    private var chatDialogProgressBar: ProgressBar? = null
    private var matchedUsersNumber = 0
    private var dialogsNumber = 0
    private var mainUserFirebaseId: String? = null

    private lateinit var matchesTab: TextView
    private lateinit var requestsTab: TextView
    private lateinit var tabIndicator: View

    private lateinit var blankChatLayout: ConstraintLayout
    private lateinit var chatBlankTitle: TextView
    private lateinit var chatBlankDesc: TextView

    private var isRequestTabSelected = false


    private var firebaseDatabaseReference: DatabaseReference? = null
    private var firebaseChildEventListener: ChildEventListener? = null

    private lateinit var spacesImagesViewModel: SpacesImagesViewModel

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {

        return inflater.inflate(R.layout.fragment_chat_dialog_list, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        spacesImagesViewModel = ViewModelProvider(this).get(SpacesImagesViewModel::class.java)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialogsList = view.findViewById(R.id.dialogsList)
        chatDialogProgressBar = view.findViewById(R.id.chat_dialog_progress_bar)
        matchesTab = view.findViewById(R.id.matches_tab)
        requestsTab = view.findViewById(R.id.requests_tab)
        tabIndicator = view.findViewById(R.id.tab_indicator)
        blankChatLayout = view.findViewById(R.id.blank_chat_layout)
        chatBlankTitle = view.findViewById(R.id.chat_blank_title)
        chatBlankDesc = view.findViewById(R.id.chat_blank_desc)

        firebaseDatabaseUtil = activity?.let { FirebaseDatabaseUtil(it) }

        initAdapter()

        dialogs

        mainUserFirebaseId = AccountPreferences.getInstance(context).getStringValue(Constants.firebaseUserId, "")
    }

    private fun initAdapter() {

        imageLoader = ImageLoader { imageView: ImageView?, url: String?, _: Any? ->

            activity?.let {

                if (imageView != null) {

                    GlideApp.with(it)
                        .load(url)
                        .apply(RequestOptions.circleCropTransform())
                        .into(imageView)
                }
            }
        }

        dialogsAdapter = DialogsListAdapter(R.layout.chat_item_custom_dialog, CustomDialogViewHolder::class.java, imageLoader)
        dialogsAdapter?.setOnDialogClickListener(this)
        dialogsAdapter?.setDatesFormatter { date: Date? ->
            date?.let {
                when {
                    DateFormatter.isToday(it) -> {
                        return@setDatesFormatter getString(R.string.date_header_today)
                    }
                    DateFormatter.isYesterday(it) -> {
                        return@setDatesFormatter getString(R.string.date_header_yesterday)
                    }
                    isThisWeek(it) -> {
                        return@setDatesFormatter DateFormatter.format(it, "E")
                    }
                    else -> {
                        return@setDatesFormatter DateFormatter.format(it, "d/MM")
                    }
                }
            }
        }
        dialogsList?.setAdapter(dialogsAdapter)


        matchesTab.setOnClickListener {
            if (!isRequestTabSelected) return@setOnClickListener
            isRequestTabSelected = false
            updateTabUI()
            showDialogsByMatchRequest(false)
        }

        requestsTab.setOnClickListener {
            if (isRequestTabSelected) return@setOnClickListener
            isRequestTabSelected = true
            updateTabUI()
            showDialogsByMatchRequest(true)
        }


        setChatsChildListener()

        setTabFont()
    }

    override fun onDialogClick(dialog: ChatDialog) {

        val user0Map = HashMap<String, String?>()
        val user1Map = HashMap<String, String>()
        user0Map[Constants.name] = AccountPreferences.getInstance(context).getStringValue(Constants.name, "")
        user0Map[Constants.avatar] = AccountPreferences.getInstance(context).getStringValue(Constants.userProfilePhotoUrl, "")
        user0Map[Constants.firebaseUserId] = mainUserFirebaseId

        var dialogUserName = dialog.dialogName.replace(" (Guest)", "")
        dialogUserName = dialogUserName.replace("/ MEMBER", "")
        dialogUserName = dialogUserName.substring(0, 1).toUpperCase(appLocale) + dialogUserName.substring(1, dialogUserName.length).toLowerCase(appLocale)

        user1Map[Constants.name] = dialogUserName
        user1Map[Constants.avatar] = dialog.dialogPhoto
        user1Map[Constants.firebaseUserId] = dialog.id

        conversationId = dialog.lastMessage.id

        if (activity != null) {
            if (dialog.dmRequest) {
                openOtherUserProfile(dialog)
            } else {
                val intent = Intent(activity, MessageListActivity::class.java)
                intent.putExtra(Constants.user0, user0Map)
                intent.putExtra(Constants.user1, user1Map)
                intent.putExtra(Constants.conversationId, conversationId)
                activity?.startActivity(intent)
            }
        }
    }

    private fun openOtherUserProfile(dialog: ChatDialog) {
        if (activity != null) {
            val intent = Intent(activity, OtherUserProfileModal::class.java)
            intent.putExtra(Constants.firebaseUserId, dialog.id)
            intent.putExtra(DIALOG_PHOTO_URL, dialog.dialogPhoto)
            intent.putExtra(DM_REQUEST, dialog.dmRequest)
            intent.putExtra(CHAT_ID, dialog.conversationChatId)
            intent.putExtra(EXTRA_LAST_MESSAGE, dialog.lastMessage.text)
            startActivity(intent)
        }
    }

    private val dialogs: Unit
        get() {

            chatDialogs = ArrayList()

            firebaseDatabaseUtil?.readAllMatchedUsersInfoFromFirebase(object : FirebaseRetrieveAllMatchesListenerInterface {
                override fun onStart() {
                }

                override fun onSuccess(dataSnapshot: DataSnapshot) {

                    showChatDialogProgressBar()

                    if (!dataSnapshot.exists()) {

                        setBlankMatchesView()
                    } else {

                        val usersMap = dataSnapshot.value as? java.util.HashMap<String, Any>?

                        if (usersMap != null) {

                            matchedUsersNumber = usersMap.size

                            if (usersMap.isEmpty()) {

                                setBlankMatchesView()
                            } else {

                                getDialog(usersMap, dataSnapshot)
                            }
                        }
                    }
                }

                override fun onFailure() {

                    Timber.d("failures in dialogs: ")
                }
            })
        }

    private fun getDialog(usersMap: java.util.HashMap<String, Any>?, matchedUsersDataSnapshot: DataSnapshot) {
        try {

            if (usersMap != null) {

                for ((key, value) in usersMap) {

                    val chatIdMap = value as? java.util.HashMap<String, Any>
                    var chatId = CHAT_ID
                    var messageUnreadStatus: Boolean? = false
                    var dmRequest: Boolean? = false

                    if (matchedUsersDataSnapshot.hasChild("$key/chatId")) {

                        chatId = Objects.requireNonNull(chatIdMap?.get(Constants.chatId)).toString()

                        messageUnreadStatus = if (matchedUsersDataSnapshot.hasChild(key + "/" + Constants.unread)) {

                            chatIdMap?.get(Constants.unread) as? Boolean?
                        } else {

                            true
                        }

                        dmRequest = if (matchedUsersDataSnapshot.hasChild(key + "/" + Constants.dmRequest)) {
                            chatIdMap?.get(Constants.dmRequest) as? Boolean?
                        } else {
                            false
                        }
                    }
                    var chatIdTimeStamp = Calendar.getInstance().timeInMillis
                    if (matchedUsersDataSnapshot.hasChild("$key/timestamp")) {
                        chatIdTimeStamp = Objects.requireNonNull(chatIdMap?.get(Constants.timestamp)).toString().toLong()
                    }

                    var lastMessage = SEND_A_SONG
                    if (matchedUsersDataSnapshot.hasChild("$key/message")) {
                        lastMessage = Objects.requireNonNull(chatIdMap?.get(Constants.lastMessage)).toString()
                    }

                    var dmRequestBy: String? = null
                    if (matchedUsersDataSnapshot.hasChild(key + "/" + Constants.dmRequestBy)) {
                        dmRequestBy = chatIdMap?.get(Constants.dmRequestBy) as? String
                    }

                    var dmRequestRejected: Boolean? = null
                    if (matchedUsersDataSnapshot.hasChild(key + "/" + Constants.dmRequestRejected)) {
                        dmRequestRejected = chatIdMap?.get(Constants.dmRequestRejected) as? Boolean
                    }

                    getUserFromFirebase(key, chatId, chatIdTimeStamp, lastMessage, messageUnreadStatus, dmRequest?: false, dmRequestRejected, dmRequestBy)
                }
            }
        } catch (ex: Exception) {

            Timber.e("exception in dialogs:  $ex")
        }
    }

    private fun getUserFromFirebase(firebaseId: String, chatId: String, matchedTimeStamp: Long, lastMessage: String, messageUnreadStatus: Boolean?, dnRequest: Boolean, dmRejected: Boolean?, dmRequestBy: String?) {

        chatUsersList = ArrayList()

        firebaseDatabaseUtil?.readSingleUserInfoFromFirebaseFirestore(object :
            FirebaseRetrieveSingleUserListenerInterfaceFirestore {
            override fun onSuccess(user: User) {
                if (user.isUserReported != true && Utils.isValidUser(user)) {
                    spacesImagesViewModel.getImageUrl("$firebaseId/${Constants.photoFileName1}") { url, _ ->
                        activity?.let {
                            it.runOnUiThread {
                                onFirebaseCompletion(url, firebaseId, chatId, user, lastMessage, matchedTimeStamp, messageUnreadStatus, false, dnRequest, dmRejected, dmRequestBy)
                            }
                        }
                    }
                } else {
                    onFirebaseCompletion(null, null, null, null, null, 0L, null, null, false, null, null)
                }
            }

            override fun onFailure() {

                onFirebaseCompletion(null, null, null, null, null, 0L, null, null, false, null, null)

                Timber.d("getting  user failed: ")
            }
        }, firebaseId)
    }

    private fun onFirebaseCompletion(uri: String?, firebaseId: String?, chatId: String?, user: User?, lastMessage: String?, matchedTimeStamp: Long,
                                     unreadStatus: Boolean?, matchStatus: Boolean?, dmRequest: Boolean, dmRejected: Boolean?, dmRequestBy: String?) {

        dialogsNumber += 1

        if (firebaseId != null && user != null) {
            userProfilePhotoUrl = uri.toString()
            val chatUser = ChatUser(firebaseId, user.name.toString(), userProfilePhotoUrl)
            chatUsersList?.add(chatUser)

            val chatMessage = ChatMessage(chatId, chatUser, lastMessage, getDateFromTimestamp(matchedTimeStamp))

            var dialogTitle = user.name.toString()

            chatDialog = ChatDialog(firebaseId, dialogTitle, chatUsersList, chatMessage, userProfilePhotoUrl, unreadStatus, matchStatus, matchedTimeStamp, user.gender?.toString(), chatId, dmRequest, dmRejected, dmRequestBy)
            chatDialog?.let {
                chatDialogs?.add(it)
                setDialog(it)
            }
        }
    }

    private fun setBlankMatchesView() {

        if (isRequestTabSelected) {
            chatBlankTitle.text = getString(R.string.blank_matches_chats_requests_title)
            chatBlankDesc.text = getString(R.string.blank_matches_chats_requests)
        } else {
            chatBlankTitle.text = getString(R.string.blank_matches_chats_title)
            chatBlankDesc.text = getString(R.string.blank_matches_activity_when_you_match_with_text_view_text)
        }
        blankChatLayout.visibility = View.VISIBLE
    }

    private fun setDialogs(chatDialogList: List<ChatDialog>?) {

        blankChatLayout.visibility = View.GONE

        dialogsAdapter?.setItems(chatDialogList)

        if (chatDialogList?.isEmpty() == true) {

            setBlankMatchesView()
        }

        hideChatDialogProgressBar()

        setChatsChildListener()
    }

    private fun setDialog(chatDialog: ChatDialog) {
        blankChatLayout.visibility = View.GONE

        if (!chatDialog.dmRequest) {
            dialogsAdapter?.upsertItem(chatDialog)
        }

        dialogsAdapter?.sort { c1, c2 ->
            c2.matchedTimeStamp.compareTo(c1.matchedTimeStamp)
        }

        if (chatDialogs?.none { !it.dmRequest } == true) {
            setBlankMatchesView()
        }

        hideChatDialogProgressBar()
    }

    private fun showChatDialogProgressBar() {
        chatDialogProgressBar?.visibility = View.VISIBLE
        blankChatLayout.visibility = View.GONE
    }

    private fun hideChatDialogProgressBar() {

        if (chatDialogs?.isEmpty() == true) {
            setBlankMatchesView()
        }

        chatDialogProgressBar?.visibility = View.GONE
    }

    private fun setChatsChildListener() {

        firebaseDatabaseUtil?.readAllMatchedUsersInfoFromFirebaseChildListener(object : FirebaseRetrieveChildListener {
            override fun onChildChanged(dataSnapshot: DataSnapshot, p1: String?, reference: DatabaseReference, listener: ChildEventListener) {
                firebaseDatabaseReference = reference
                firebaseChildEventListener = listener
                checkForChangedValues(dataSnapshot)
            }

            override fun onChildAdded(dataSnapshot: DataSnapshot, p1: String?, reference: DatabaseReference, listener: ChildEventListener) {
                firebaseDatabaseReference = reference
                firebaseChildEventListener = listener
            }

            override fun onChildRemoved(dataSnapshot: DataSnapshot, reference: DatabaseReference, listener: ChildEventListener) {
                firebaseDatabaseReference = reference
                firebaseChildEventListener = listener
                removeDialogFromList(dataSnapshot)
            }

            override fun onChildMoved(dataSnapshot: DataSnapshot, p1: String?, reference: DatabaseReference, listener: ChildEventListener) {
                firebaseDatabaseReference = reference
                firebaseChildEventListener = listener
            }

            override fun onFailure() {
                Timber.d("Listening to chat dialogs failed")
            }
        })
    }

    private fun checkForChangedValues(dataSnapshot: DataSnapshot) {
        try {
            val chatDialogKey = dataSnapshot.key
            val matchesMap = dataSnapshot.value as? java.util.HashMap<String, Any>?

            if (matchesMap != null && matchesMap.isNotEmpty()) {

                val chatId = Objects.requireNonNull(matchesMap[Constants.chatId]).toString()
                val matchLatestMessage = Objects.requireNonNull(matchesMap[Constants.message]).toString()
                val lastMessageTimeStamp = matchesMap[Constants.timestamp] as? Long

                chatDialogs?.let { dialogs ->

                    for (i in dialogs.indices) {

                        val chatUserKey = chatDialogs?.get(i)?.id

                        if (chatUserKey == chatDialogKey) {

                            val chatUserName = chatDialogs?.get(i)?.dialogName

                            val chatUserAvatar = chatDialogs?.get(i)?.dialogPhoto

                            chatDialogs?.get(i)?.setMessageUnreadStatus(true)

                            val chatUser = ChatUser(chatDialogKey, chatUserName, chatUserAvatar)

                            val chatMessage = ChatMessage(chatId, chatUser, matchLatestMessage, lastMessageTimeStamp?.let { time -> getDateFromTimestamp(time) })

                            dialogsAdapter?.updateDialogWithMessage(chatDialogKey, chatMessage)

                            break
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e("Exception in check for changed values:  $e")
        }
    }

    private fun removeDialogFromList(dataSnapshot: DataSnapshot) {

        val chatDialogKey = dataSnapshot.key

        dialogsAdapter?.deleteById(chatDialogKey)

        if (chatDialogs?.isEmpty() == true) {

            setBlankMatchesView()
        }
    }

    private fun removeDialogById(unmatchedUserId: String?) {

        dialogsAdapter?.deleteById(unmatchedUserId)
    }

    override fun onDestroyView() {
        removeFirebaseListener()
        super.onDestroyView()
    }

    override fun onStop() {
        removeFirebaseListener()
        super.onStop()
    }

    private fun removeFirebaseListener() {
        firebaseChildEventListener?.let {
            firebaseDatabaseReference?.removeEventListener(it)
        }
    }

    companion object {
        @JvmField
        var SEND_A_SONG = "Make a Move"

        @JvmField
        var EXTRA_LAST_MESSAGE = "EXTRA_LAST_MESSAGE"

        @JvmField
        var CHAT_ID = "CHAT_ID"

        var DM_REQUEST = "DM_REQUEST"

        var DIALOG_PHOTO_URL = "DIALOG_PHOTO_URL"

    }

    private fun showDialogsByMatchRequest(isRequest: Boolean) {
        val filteredDialogs = chatDialogs?.filter {
            it.dmRequest == isRequest &&
                    it.dmRequestBy != null &&
                    it.dmRejected != true &&
                    mainUserFirebaseId != it.dmRequestBy

        } ?: emptyList()

        setDialogs(filteredDialogs)
    }

    private fun updateTabUI() {
        val selectedColor = ContextCompat.getColor(requireContext(), R.color.black)
        val unselectedColor = ContextCompat.getColor(requireContext(), R.color.grey1)

        matchesTab.setTextColor(if (isRequestTabSelected) unselectedColor else selectedColor)
        requestsTab.setTextColor(if (isRequestTabSelected) selectedColor else unselectedColor)

        setTabFont()

        // Animate underline
        tabIndicator.animate()
            .translationX(if (isRequestTabSelected) requestsTab.x else matchesTab.x)
            .setDuration(200)
            .start()
    }

    private fun setTabFont() {
        if (isRequestTabSelected) {
            requestsTab.setTextColor(resources.getColor(R.color.grey1))
            matchesTab.setTextColor(resources.getColor(R.color.grey4))
        } else {
            matchesTab.setTextColor(resources.getColor(R.color.grey1))
            requestsTab.setTextColor(resources.getColor(R.color.grey4))
        }
    }
}