package com.lovebeats.ui.home.chat.dialog

import android.graphics.Color
import android.graphics.Typeface
import android.view.View
import android.widget.TextView
import com.lovebeats.R
import com.stfalcon.chatkit.dialogs.DialogsListAdapter.DialogViewHolder

class CustomDialogViewHolder(itemView: View) : DialogViewHolder<ChatDialog>(itemView) {

    private val isNewMatchView: View = itemView.findViewById(R.id.matchStatus)
    private val lastMessageTextView: TextView = itemView.findViewById(com.stfalcon.chatkit.R.id.dialogLastMessage)
    private val dialogNameTextView: TextView = itemView.findViewById(com.stfalcon.chatkit.R.id.dialogName)

    override fun onBind(dialog: ChatDialog) {

        super.onBind(dialog)
        val isNewMatch = dialog.matchStatus
        val isMessageUnread = dialog.messageReadStatus

        dialogNameTextView.setTypeface(dialogNameTextView.typeface, Typeface.BOLD)
        dialogNameTextView.textSize = 16f

        if (isMessageUnread == true) {

            lastMessageTextView.setTypeface(lastMessageTextView.typeface, Typeface.BOLD)
        } else {
            lastMessageTextView.setTextColor(Color.parseColor("#646C70"))
        }
        if (isNewMatch == true) {

            isNewMatchView.visibility = View.VISIBLE
        } else {
            isNewMatchView.visibility = View.GONE
        }
    }

}