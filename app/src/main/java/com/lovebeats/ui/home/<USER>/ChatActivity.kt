package com.lovebeats.ui.home.chat

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import com.lovebeats.R
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.ui.home.BottomNavigationBarActivity
import com.lovebeats.ui.home.chat.dialog.ChatDialogListFragment
import com.lovebeats.ui.home.chat.message.MessageListActivity.Companion.UN_MATCHED_USER_ID
import com.lovebeats.utils.ScreenRouter

class ChatActivity : BottomNavigationBarActivity() {

    private lateinit var mLayoutContainer: FrameLayout

    override fun getBottomNavigationBarItem(): Int {

        return R.id.navigation_chat
    }

    companion object {

        fun newIntent(context: Context): Intent {
            return Intent(context, ChatActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        initLayout()

        isChatScreenActive = true

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun initLayout() {

        mLayoutContainer = findViewById(R.id.layout_container)

        val chatLayout = View.inflate(this, R.layout.activity_chat, null)
        mLayoutContainer.addView(chatLayout)

        val chatFragment = ChatDialogListFragment()

        val bundle = Bundle()

        when {
            intent.getStringExtra(UN_MATCHED_USER_ID) != null -> {
                val id = intent.getStringExtra(UN_MATCHED_USER_ID)
                val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)
                firebaseDatabaseUtil.deleteUnmatchedUserFromMainUserNode(id) {
                    supportFragmentManager.beginTransaction().replace(R.id.main_container, chatFragment).commit()
                }
            }
            else -> {
                chatFragment.arguments = bundle
                supportFragmentManager.beginTransaction().replace(R.id.main_container, chatFragment).commit()
            }
        }
    }
}
