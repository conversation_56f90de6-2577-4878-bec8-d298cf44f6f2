package com.lovebeats.ui.home.settings

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.SpannableString
import android.text.TextUtils
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.lovebeats.R
import com.lovebeats.databinding.ActivitySettingsIceBreakerTitleBinding
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils
import com.lovebeats.utils.Utils.Companion.afterTextChanged

class SettingsIceBreakerTitleActivity : AppCompatActivity() {

    private lateinit var chooseNewQuestionButton: Button
    private lateinit var fieldTitleTextView: TextView
    private lateinit var saveTextView: TextView
    private lateinit var buttonLargeActiveButton: Button
    private lateinit var cancelTextView: TextView
    private lateinit var iceBreakerEditText: EditText
    private lateinit var counterTextView: TextView

    private var icebreaker: String = ""
    private var answer: String = ""
    private var question: String = ""

    private lateinit var binding: ActivitySettingsIceBreakerTitleBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingsIceBreakerTitleBinding.inflate(layoutInflater)
        setContentView(binding.root)

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    fun init() {
        chooseNewQuestionButton = this.findViewById(R.id.choose_new_question_button)

        icebreaker = intent.getStringExtra("icebreaker") ?: ""
        answer = intent.getStringExtra("answer") ?: ""
        question = intent.getStringExtra("question") ?: ""

        chooseNewQuestionButton.setOnClickListener {
            val intent = Intent(this, SettingsIceBreakerListActivity::class.java)
            intent.putExtra("icebreaker", icebreaker)
            startActivity(intent)
        }

        val questionsMap = Utils.getIceBreakerQuestions()

        saveTextView = this.findViewById(R.id.save_text_view)
        cancelTextView = this.findViewById(R.id.cancel_text_view)

        // Configure Field Title component
        fieldTitleTextView = this.findViewById(R.id.field_title_text_view)
        val fieldTitleTextViewText = SpannableString(question)
        fieldTitleTextView.text = fieldTitleTextViewText.toString()

        counterTextView = this.findViewById(R.id.counter_text_view)

        // Configure 0/50 component
        val textViewTextViewText = SpannableString(this.getString(R.string.icebreaker2_activity_text_view_text_view_text))
        counterTextView.text = textViewTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            saveIceBreakerPreferences()
        }

        iceBreakerEditText = this.findViewById(R.id.icebreaker1_edit_text)
        iceBreakerEditText.requestFocus()

        iceBreakerEditText.hint = questionsMap[question]

        if (!TextUtils.isEmpty(answer)) {
            iceBreakerEditText.setText(answer)
        }

        val counterText = 140.minus(answer.length).toString() + "/140"

        counterTextView.text = counterText

        iceBreakerEditText.afterTextChanged {

            if (it.isNotEmpty()) {

                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                buttonLargeActiveButton.isEnabled = true
            } else {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
                buttonLargeActiveButton.isEnabled = false
            }

            val textCounter = 140.minus(it.length)
            counterTextView.text = "$textCounter/140"
        }

        saveTextView.setOnClickListener {
            saveIceBreakerPreferences()
        }

        cancelTextView.setOnClickListener {
            val intent = Intent(this, EditProfileModalActivity::class.java)
            startActivity(intent)
        }
    }

    private fun saveIceBreakerPreferences() {

        answer = iceBreakerEditText.text.toString()

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        if (Constants.iceBreaker1 == icebreaker) {
            AccountPreferences.getInstance(applicationContext).setValue(Constants.iceBreaker1, question)
            firebaseDatabaseReference.updateUserInfoMapToFirebase(Constants.iceBreaker1, question, answer)
        } else if (Constants.iceBreaker2 == icebreaker) {
            AccountPreferences.getInstance(applicationContext).setValue(Constants.iceBreaker2, question)
            firebaseDatabaseReference.updateUserInfoMapToFirebase(Constants.iceBreaker2, question, answer)
        }

        binding.settingsIcebreakerProgressBar.visibility = View.VISIBLE
        Handler(Looper.getMainLooper()).postDelayed({
            binding.settingsIcebreakerProgressBar.visibility = View.GONE
            val intent = Intent(this, EditProfileModalActivity::class.java)
            startActivity(intent)
        }, 1000)
    }
}
