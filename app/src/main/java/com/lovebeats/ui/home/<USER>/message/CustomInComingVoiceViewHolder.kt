package com.lovebeats.ui.home.chat.message

import android.view.View
import android.widget.ImageButton
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.lovebeats.R
import com.stfalcon.chatkit.messages.MessageHolders

class CustomInComingVoiceViewHolder(itemView: View?, payload: Any?) : MessageHolders.IncomingTextMessageViewHolder<Message?>(itemView, payload) {

    override fun onBind(message: Message?) {
        super.onBind(message)

        val chatPlayer = itemView.findViewById<ChatPlayer>(R.id.player)
        chatPlayer.show(itemView.context, message?.song)

        MessageListActivity.chatPlayer = chatPlayer

        val layout = chatPlayer.findViewById<ConstraintLayout>(R.id.chat_payer_item_layout)
        layout.setBackgroundColor(ContextCompat.getColor(itemView.context, R.color.grey5))

        val playButton = chatPlayer.findViewById<ImageButton>(R.id.play_button)
        playButton.setOnClickListener {
            if (chatPlayer.isPlaying()) {
                playButton.setImageDrawable(AppCompatResources.getDrawable(itemView.context, R.drawable.ic_play_black))
                chatPlayer.pause()
            } else {
                playButton.setImageDrawable(AppCompatResources.getDrawable(itemView.context, R.drawable.pause_icon))
                chatPlayer.play()
            }
        }
    }
}