package com.lovebeats.ui.home.chat.dialog

import com.stfalcon.chatkit.commons.models.IMessage
import com.stfalcon.chatkit.commons.models.MessageContentType
import java.util.Date

class ChatMessage @JvmOverloads constructor(private val id: String?, private val user: ChatUser?,
                                            private var text: String?,
                                            private var createdAt: Date?) : IMessage, MessageContentType.Image {

    private var image: Image? = null

    override fun getId(): String? {

        return id
    }

    override fun getText(): String? {

        return text
    }

    override fun getCreatedAt(): Date? {

        return createdAt
    }

    override fun getUser(): ChatUser? {

        return user
    }

    override fun getImageUrl(): String? {

        if (image != null && image is Image) {

            return (image as Image).url
        }

        return null
    }

    val status: String
        get() = "Sent"

    fun setText(text: String) {
        this.text = text
    }

    fun setCreatedAt(createdAt: Date) {
        this.createdAt = createdAt
    }

    fun setImage(image: Image?) {

        this.image = image
    }

    class Image(val url: String?)
}