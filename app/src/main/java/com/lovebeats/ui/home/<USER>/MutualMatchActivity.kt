package com.lovebeats.ui.home.browseProfiles

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.lovebeats.databinding.ActivityMutualMatchBinding
import com.lovebeats.extensions.launchModeWithSingleClearTop
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.firebase.database.FirebaseRetrieveSingleUserListenerInterfaceFirestore
import com.lovebeats.glide.ImageLoaderModule
import com.lovebeats.models.GenderType
import com.lovebeats.models.User
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.SpacesImagesViewModel
import com.lovebeats.ui.home.chat.dialog.ChatDialogListFragment
import com.lovebeats.ui.home.chat.dialog.ChatDialogListFragment.Companion.CHAT_ID
import com.lovebeats.ui.home.chat.dialog.ChatDialogListFragment.Companion.SEND_A_SONG
import com.lovebeats.ui.home.chat.message.MessageListActivity
import com.lovebeats.ui.paidVersion.likesYou.LikesYouActivity
import com.lovebeats.ui.paidVersion.likesYou.LikesYouAdapter
import com.lovebeats.utils.Constants
import com.lovebeats.utils.Constants.photoFileName1
import com.lovebeats.utils.ScreenRouter
import timber.log.Timber
import java.io.Serializable

class MutualMatchActivity : AppCompatActivity() {

    private lateinit var mContext: Context
    private lateinit var otherUserProfilePhotoUrl: String

    private var otherUserfirebaseId: String? = null
    private var chatId: String? = CHAT_ID

    private lateinit var spacesImagesViewModel: SpacesImagesViewModel

    private lateinit var binding: ActivityMutualMatchBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMutualMatchBinding.inflate(layoutInflater)
        setContentView(binding.root)

        spacesImagesViewModel = ViewModelProvider(this).get(SpacesImagesViewModel::class.java)

        if (intent.getStringExtra(Constants.firebaseUserId) != null) {

            otherUserfirebaseId = intent.getStringExtra(Constants.firebaseUserId)
        }

        if (intent.getStringExtra(Constants.conversationId) != null) {

            chatId = intent.getStringExtra(Constants.conversationId)
        }

        mContext = this

        getPhotoUrl()
    }

    fun init() {

        val fromOtherUserProfile = intent.getBooleanExtra(OtherUserProfileModal.EXTRA_MESSAGE_OTHER_PROFILE_MODAL, false)

        if (fromOtherUserProfile) {

            var likesYouUser: Serializable? = null

            if (intent.getSerializableExtra(LikesYouAdapter.EXTRA_LIKES_YOU_USER) != null) {

                likesYouUser = intent.getSerializableExtra(LikesYouAdapter.EXTRA_LIKES_YOU_USER)
            }

            binding.keepBrowsingButton.text = "Back to Likes"

            binding.keepBrowsingButton.setOnClickListener {

                val intent = Intent(mContext, LikesYouActivity::class.java).launchModeWithSingleClearTop()

                if (likesYouUser != null) {

                    intent.putExtra(OtherUserProfileModal.EXTRA_USER_FROM_OTHER_PROFILE_MODAL, likesYouUser)
                }

                startActivity(intent)
                finish()
            }
        } else {

            binding.keepBrowsingButton.setOnClickListener {

                val intent = Intent(mContext, BrowseProfilesActivity::class.java)
                startActivity(intent)
                finish()
            }
        }

        ImageLoaderModule.loadImageIntoImageViewWithSpacesPathCircleTransformAndLoading(this, spacesImagesViewModel, "${otherUserfirebaseId}/${photoFileName1}",binding.userProfileImageView)

        val firebaseDatabaseUtil = FirebaseDatabaseUtil(this)

        otherUserfirebaseId?.let {

            firebaseDatabaseUtil.readSingleUserInfoFromFirebaseFirestore(object :
                FirebaseRetrieveSingleUserListenerInterfaceFirestore {
                override fun onSuccess(user: User) {

                    when {
                        fromOtherUserProfile -> {

                            binding.likeTextView.text = "It's a match!"
                        }
                        user.gender == GenderType.man.toString() -> {

                            binding.likeTextView.text = "He likes you too!"
                        }
                        user.gender == GenderType.woman.toString() -> {

                            binding.likeTextView.text = "She likes you too!"
                        }
                        else -> {
                            binding.likeTextView.text = "It's a match!"
                        }
                    }

                    val user0Map = HashMap<String, String>()
                    val user1Map = HashMap<String, String>()

                    user0Map[Constants.name] = AccountPreferences.getInstance(mContext).getStringValue(
                        Constants.name, "")
                    user0Map[Constants.avatar] = AccountPreferences.getInstance(mContext).getStringValue(
                        Constants.userProfilePhotoUrl, "")
                    user0Map[Constants.firebaseUserId] = AccountPreferences.getInstance(mContext).getStringValue(
                        Constants.firebaseUserId, "")

                    user1Map[Constants.name] = user.name.toString()
                    user1Map[Constants.avatar] = otherUserProfilePhotoUrl
                    user1Map[Constants.firebaseUserId] = it
                    user1Map[Constants.gender] = user.gender.toString()

                    binding.sendMessageButton.setOnClickListener {
                        val intent = Intent(mContext, MessageListActivity::class.java)
                        intent.putExtra(Constants.user0, user0Map)
                        intent.putExtra(Constants.user1, user1Map)
                        intent.putExtra(Constants.conversationId, chatId)
                        intent.putExtra(ChatDialogListFragment.EXTRA_LAST_MESSAGE, SEND_A_SONG)
                        startActivity(intent)
                        finish()
                    }
                }

                override fun onFailure() {
                    Timber.d("retrieving single user data in mutual match failed")
                }
            }, it)
        }

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun getPhotoUrl() {
        spacesImagesViewModel.getImageUrl( "$otherUserfirebaseId/${photoFileName1}") { url, _ ->
            runOnUiThread {
                otherUserProfilePhotoUrl = url?: ""
                init()
            }
        }
    }
}
