package com.lovebeats.ui.home.chat.message

import android.view.View
import android.widget.ImageButton
import androidx.appcompat.content.res.AppCompatResources
import com.lovebeats.R
import com.stfalcon.chatkit.messages.MessageHolders

class CustomOutComingVoiceViewHolder(itemView: View?, payload: Any?) : MessageHolders.OutcomingTextMessageViewHolder<Message?>(itemView, payload) {
    override fun onBind(message: Message?) {
        super.onBind(message)

        val chatPlayer = itemView.findViewById<ChatPlayer>(R.id.player)
        chatPlayer.show(itemView.context, message?.song)

        MessageListActivity.chatPlayer = chatPlayer

        val playButton = chatPlayer.findViewById<ImageButton>(R.id.play_button)
        playButton.setImageDrawable(AppCompatResources.getDrawable(itemView.context, R.drawable.ic_play_white))
        playButton.setOnClickListener {
            if (chatPlayer.isPlaying()) {
                playButton.setImageDrawable(AppCompatResources.getDrawable(itemView.context, R.drawable.ic_play_white))
                chatPlayer.pause()
            } else {
                playButton.setImageDrawable(AppCompatResources.getDrawable(itemView.context, R.drawable.pause_icon))
                chatPlayer.play()
            }
        }
    }
}