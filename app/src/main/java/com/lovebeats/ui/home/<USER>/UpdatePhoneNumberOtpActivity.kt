package com.lovebeats.ui.home.settings

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.res.ResourcesCompat
import com.google.firebase.FirebaseException
import com.google.firebase.FirebaseTooManyRequestsException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthInvalidCredentialsException
import com.google.firebase.auth.PhoneAuthCredential
import com.google.firebase.auth.PhoneAuthProvider
import com.google.firebase.database.DatabaseReference
import com.google.firebase.database.FirebaseDatabase
import com.lovebeats.R
import com.lovebeats.databinding.ActivityUpdatePhoneNumberOtpBinding
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.onboarding.EmailActivity
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.FontSpan
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils
import com.lovebeats.utils.Utils.Companion.afterTextChanged
import timber.log.Timber
import java.util.concurrent.TimeUnit

class UpdatePhoneNumberOtpActivity : AppCompatActivity() {

    private lateinit var iDidnTgetAcodeTextView: TextView
    private lateinit var enter6DigitCodeTextView: TextView
    private lateinit var sentToTextView: TextView
    private lateinit var buttonLargeActiveButton: Button

    private lateinit var otpEditText1: EditText

    private lateinit var mCallbacks: PhoneAuthProvider.OnVerificationStateChangedCallbacks
    private val keyVerifyInProgress: String = "key_verify_in_progress"
    private var mVerificationInProgress: Boolean = false
    var resendToken: PhoneAuthProvider.ForceResendingToken? = null

    //private lateinit var firebaseUserId: String
    private lateinit var phoneNumber: String
    private var otpVerificationCode: String? = null
    private var otpCode: String = ""

    private lateinit var database: DatabaseReference

    private lateinit var binding: ActivityUpdatePhoneNumberOtpBinding

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, UpdatePhoneNumberOtpActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityUpdatePhoneNumberOtpBinding.inflate(layoutInflater)
        setContentView(binding.root)
        this.init()

        binding.preferencesHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        phoneNumber = "+91" + AccountPreferences.getInstance(applicationContext).getStringValue(
            Constants.phoneNumber, "").toString()

        initAuthCallback()

        getOtp(phoneNumber = phoneNumber)

        // Configure I didn’t get a code component
        iDidnTgetAcodeTextView = this.findViewById(R.id.i_didn_tget_acode_text_view)
        val iDidnTgetAcodeTextViewText = SpannableString(this.getString(R.string.code_verification_retry_text))
        iDidnTgetAcodeTextView.text = iDidnTgetAcodeTextViewText

        // Configure Enter 6-Digit Code component
        enter6DigitCodeTextView = this.findViewById(R.id.enter6_digit_code_text_view)
        val enter6DigitCodeTextViewText = SpannableString(this.getString(R.string.code_verification_title))
        enter6DigitCodeTextView.text = enter6DigitCodeTextViewText

        // Configure Sent to: component
        sentToTextView = this.findViewById(R.id.sent_to_text_view)
        val sentToTextViewText = SpannableString(this.getString(R.string.code_verification_sent_to_text) + phoneNumber)
        sentToTextViewText.setSpan(FontSpan(ResourcesCompat.getFont(this, R.font.inter_bold)), 0, 8, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        sentToTextView.text = sentToTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.isEnabled = false

        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        //configure otp edit texts
        otpEditText1 = this.findViewById(R.id.edit_text_edit_text)

        otpEditText1.afterTextChanged {
            makeCodeVerificationCall()
        }

        //Configure firebase
        database = FirebaseDatabase.getInstance().reference


        iDidnTgetAcodeTextView.setOnClickListener { view ->
            this.resendVerificationCode(phoneNumber, resendToken)
        }
    }

    private fun isOtpCodeValid(): Boolean {
        return otpCode.isNotEmpty() && otpCode.length == 6
    }

    private fun makeCodeVerificationCall() {

        otpCode = otpEditText1.text.toString()

        if (otpVerificationCode != null && isOtpCodeValid()) {

            buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
            buttonLargeActiveButton.isEnabled = true

            val credential = PhoneAuthProvider.getCredential(otpVerificationCode.toString(), otpCode)
            signInWithPhoneAuthCredential(credential)
        } else {
            buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
            buttonLargeActiveButton.isEnabled = false
        }
    }

    fun onButtonLargeActivePressed() {

        makeCodeVerificationCall()
    }

    private fun startEmailActivity() {
        this.startActivity(EmailActivity.newIntent(this))
    }

    private fun initAuthCallback() {
        mCallbacks = object : PhoneAuthProvider.OnVerificationStateChangedCallbacks() {

            override fun onVerificationCompleted(credential: PhoneAuthCredential) {

                signInWithPhoneAuthCredential(credential)
            }

            override fun onVerificationFailed(e: FirebaseException) {
                // This callback is invoked if an invalid request for verification is made,
                // for instance if the the phone number format is invalid.
                Timber.d("onVerificationFailed: $e")

                //Fire an event to developers that otp code request for this phone number failed. This block executes
                //when a user submits his phone number to get an otp code but failed to get that otp code
                //fire an event with the error message

                when (e) {
                    is FirebaseAuthInvalidCredentialsException -> {
                        // Invalid request
                        Timber.d("Invalid phone number.")
                        Utils.showAlert("The phone number you entered is invalid. Please check and try again", this@UpdatePhoneNumberOtpActivity)
                    }
                    is FirebaseTooManyRequestsException -> {
                        // The SMS quota for the project has been exceeded
                        Timber.e("Quota exceeded: $e")
                        Utils.showAlert("Unable to process the request. Please try again", this@UpdatePhoneNumberOtpActivity)
                    }
                    else -> {
                        Timber.e("Unable to process the request: $e")
                        Utils.showAlert("Unable to process the request. Please try again", this@UpdatePhoneNumberOtpActivity)
                    }
                }
            }

            override fun onCodeSent(verificationId: String,
                                    token: PhoneAuthProvider.ForceResendingToken) {
                // The SMS verification code has been sent to the provided phone number, we
                // now need to ask the user to enter the code and then construct a credential
                // by combining the code with a verification ID.
                // Save verification ID and resending token so we can use them later
                otpVerificationCode = verificationId
                resendToken = token
            }

            override fun onCodeAutoRetrievalTimeOut(verificationId: String) {
                // called when the timeout duration has passed without triggering onVerificationCompleted
                super.onCodeAutoRetrievalTimeOut(verificationId)
                //show the otp ui here
                Utils.showAlert("Unable to process the request. Please try again", this@UpdatePhoneNumberOtpActivity)
            }
        }
    }

    private fun getOtp(phoneNumber: String) {

        PhoneAuthProvider.getInstance().verifyPhoneNumber(
                phoneNumber, // Phone number to verify
                45,             // Timeout duration
                TimeUnit.SECONDS,   // Unit of timeout
                this,           // Activity (for callback binding)
                mCallbacks)
    }

    private fun signInWithPhoneAuthCredential(credential: PhoneAuthCredential) {
        FirebaseAuth.getInstance().signInWithCredential(credential)
                .addOnCompleteListener(this) { task ->
                    if (task.isSuccessful) {
                        // Sign in success, update UI with the signed-in user's information
                        savePhoneNumberToFirebase()
                    } else {
                        // Sign in failed, display a message and update the UI

                        if (task.exception is FirebaseAuthInvalidCredentialsException) {
                            // The verification code entered was invalid
                            Utils.showAlert("The code you entered is invalid. Please check and try again", this)
                        } else if (!AppUtils.isNetworkConnected(application)) {

                            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
                        } else {
                            Timber.e("Unable to process the request: ${task.exception}")
                            Utils.showAlert("Unable to process the request. Please try again", this)
                        }

                    }
                }
    }

    private fun resendVerificationCode(phoneNumber: String,
                                       token: PhoneAuthProvider.ForceResendingToken?) {
        PhoneAuthProvider.getInstance().verifyPhoneNumber(
                phoneNumber, // Phone number to verify
                60, // Timeout duration
                TimeUnit.SECONDS, // Unit of timeout
                this, // Activity (for callback binding)
                mCallbacks, // OnVerificationStateChangedCallbacks
                token)             // ForceResendingToken from callbacks
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean(keyVerifyInProgress, mVerificationInProgress)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        mVerificationInProgress = savedInstanceState.getBoolean(keyVerifyInProgress)
    }

    private fun savePhoneNumberToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.phoneNumber, phoneNumber)

        gotToAccountActivity()
    }

    private fun gotToAccountActivity() {
        val dialogBuilder = AlertDialog.Builder(this)

        dialogBuilder.setMessage("")
                ?.setCancelable(false)
                ?.setPositiveButton("OK") { dialog, id ->
                    val intent = Intent(this, SettingsActivity::class.java)
                    intent.putExtra("AccountSettingsUpdateEmailOrPhoneActivity", true)
                    startActivity(intent)
                }

        val alert = dialogBuilder.create()
        alert.setTitle("Your phone number has been successfully updated.")
        alert.show()
    }
}
