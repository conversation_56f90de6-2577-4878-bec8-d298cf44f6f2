package com.lovebeats.ui.home.settings

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import com.lovebeats.R
import com.lovebeats.ui.home.BottomNavigationBarActivity
import com.lovebeats.ui.paidVersion.changeLocation.TeleportFragment
import com.lovebeats.utils.Constants
import com.lovebeats.utils.Constants.PREFERENCES_FRAGMENT_HOME_PROFILE_ACTIVITY
import com.lovebeats.utils.Constants.PREFERENCES_FRAGMENT_TELEPORT_FRAGMENT
import com.lovebeats.utils.ScreenRouter

class SettingsActivity : BottomNavigationBarActivity() {

    private lateinit var mLayoutContainer: FrameLayout

    override fun getBottomNavigationBarItem(): Int {

        return R.id.navigation_settings
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        initLayout()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun initLayout() {
        mLayoutContainer = findViewById(R.id.layout_container)

        val settingsLayout = View.inflate(this, R.layout.activity_settings, null)
        mLayoutContainer.addView(settingsLayout)

        when {
            intent.getBooleanExtra(PREFERENCES_FRAGMENT_HOME_PROFILE_ACTIVITY, false) -> {

                val preferencesFragment = PreferencesFragment()
                val arguments = Bundle()
                arguments.putString(Constants.PREFERENCES_FRAGMENT_ARG1, PREFERENCES_FRAGMENT_HOME_PROFILE_ACTIVITY)
                preferencesFragment.arguments = arguments
                supportFragmentManager.beginTransaction().add(R.id.main_container, preferencesFragment).commit()

            }
            intent.getBooleanExtra("EditProfileModalActivity", false) -> {

                val editProfileFragment = EditProfileFragment()

                supportFragmentManager
                        .beginTransaction()
                        .add(R.id.main_container, editProfileFragment)
                        .commit()
            }
            intent.getBooleanExtra("AccountSettingsUpdateEmailOrPhoneActivity", false) -> {

                val settingsAccountFragment = SettingsAccountFragment()

                supportFragmentManager
                        .beginTransaction()
                        .add(R.id.main_container, settingsAccountFragment)
                        .commit()
            }
            intent.getBooleanExtra(PREFERENCES_FRAGMENT_TELEPORT_FRAGMENT, false) -> {

                val teleportFragment = TeleportFragment()
                supportFragmentManager
                    .beginTransaction()
                    .add(R.id.main_container, teleportFragment)
                    .commit()
            }
            else -> {

                val settingsFragment = SettingsFragment()
                supportFragmentManager.beginTransaction().replace(R.id.main_container, settingsFragment).commit()
            }
        }
    }

    companion object {

        fun newIntent(context: Context): Intent {
            return Intent(context, SettingsActivity::class.java)
        }

    }
}
