package com.lovebeats.ui.home.chat.message

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.LinearLayoutManager
import com.lovebeats.R
import com.lovebeats.databinding.ProfileSongLayoutBinding
import com.lovebeats.firebase.cloudFunctions.ProfileSongApi
import com.lovebeats.models.Song
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.favorites.ProfileSongListRecyclerAdapter
import com.lovebeats.ui.home.chat.message.MessageListActivity.Companion.CHAT_SONG_URL
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import java.util.Locale

class ChatSongSelectionActivity : AppCompatActivity() {
    private lateinit var binding: ProfileSongLayoutBinding
    private var songsData: MutableSet<Song> = mutableSetOf()
    private lateinit var profileSongListRecyclerAdapter: ProfileSongListRecyclerAdapter
    val firebaseUid: String = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "") ?: ""
    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, ChatSongSelectionActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ProfileSongLayoutBinding.inflate(layoutInflater)
        setContentView(binding.root)

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        startLoading()

        setInitialData()

        binding.accountHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        binding.cancelTextView.setOnClickListener {
            onBackPressed()
        }

        binding.headerTitle.text = getString(R.string.select_chat_song)
        binding.titleTextView.text = getString(R.string.send_song)
        binding.cancelTextView.visibility = View.VISIBLE

        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener,
            android.widget.SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(text: String?): Boolean {
                if (!text.isNullOrEmpty()) {
                    getData(text)
                }
                return false
            }

            override fun onQueryTextChange(msg: String): Boolean {
                if (msg.length > 6) {
                    filter(msg)
                }
                return false
            }
        })
    }

    private fun startLoading() {
        binding.progressBar.visibility = View.VISIBLE
    }

    private fun hideLoading() {
        binding.progressBar.visibility = View.GONE
    }

    private fun setInitialData() {
        ProfileSongApi.getProfileSongs(this, firebaseUid, "") {response ->
            response?.let { songsData.addAll(it) }
            hideLoading()
            binding.songsViewRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
            profileSongListRecyclerAdapter = ProfileSongListRecyclerAdapter(ArrayList(songsData), this)
            binding.songsViewRecyclerView.adapter = profileSongListRecyclerAdapter
            profileSongListRecyclerAdapter.setOnItemClickListener(object : ProfileSongListRecyclerAdapter.OnItemClickListener {
                override fun onClick(view: View, data: Song) {
                    updateSongToFirebaseChatNode(data)
                }
            })
        }
    }

    private fun getData(searchText: String) {
        songsData = mutableSetOf()
        startLoading()
        ProfileSongApi.getProfileSongs(this, firebaseUid, searchText) {response ->
            response?.let { songsData.addAll(it) }
            hideLoading()
            profileSongListRecyclerAdapter.updateData(ArrayList(songsData))
        }
    }

    private fun updateSongToFirebaseChatNode(song: Song) {
        val intent = Intent()
        intent.putExtra(CHAT_SONG_URL, song.audioUrl)
        setResult(MessageListActivity.CHAT_SONG_RESULT_CODE, intent)
        finish()
    }

    private fun filter(text: String) {
        val filteredlist: MutableSet<Song> = mutableSetOf()
        for (item in songsData) {
            if (item.title?.lowercase(Locale.getDefault())?.contains(text.lowercase(Locale.getDefault())) == true) {
                filteredlist.add(item)
            }
        }
        if (filteredlist.isNotEmpty()) {
            profileSongListRecyclerAdapter.filterList(ArrayList(filteredlist))
        }
    }
}
