package com.lovebeats.ui.home.chat.message

import android.view.View
import com.lovebeats.models.UserObject
import com.stfalcon.chatkit.messages.MessageHolders

class CustomOutComingMessageViewHolder(itemView: View?, payload: Any?) : MessageHolders.OutcomingTextMessageViewHolder<Message?>(itemView, payload) {

    override fun onBind(message: Message?) {
        super.onBind(message)
        if (UserObject.isLoveBeatPlusUser == true) {
            time.text = message?.messageStatus?: ""
        } else {
            time.visibility = View.GONE
        }
    }
}