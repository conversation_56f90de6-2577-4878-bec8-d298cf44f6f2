package com.lovebeats.ui.home.browseProfiles

import android.animation.Animator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.*
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.view.View
import android.view.WindowManager
import android.widget.*
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieAnimationView
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.google.firebase.database.FirebaseDatabase
import com.lovebeats.BuildConfig
import com.lovebeats.LoveBeatApplication
import com.lovebeats.R
import com.lovebeats.analytics.AnalyticsTrackingService
import com.lovebeats.analytics.DISLIKE_USER
import com.lovebeats.analytics.LIKE_USER
import com.lovebeats.analytics.MUTUAL_MATCH
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.customViews.AppReview
import com.lovebeats.customViews.PlayerView
import com.lovebeats.customViews.ToolTipView
import com.lovebeats.extensions.getDoubleValue
import com.lovebeats.extensions.getIntValue
import com.lovebeats.extensions.launchModeWithSingleTop
import com.lovebeats.firebase.cloudFunctions.Favorites
import com.lovebeats.firebase.cloudFunctions.GetUsersResponse
import com.lovebeats.firebase.cloudFunctions.NearByUsers
import com.lovebeats.firebase.cloudFunctions.NumLikesRestriction
import com.lovebeats.firebase.cloudFunctions.NumLikesRestriction.deviceDateBasedOnServer
import com.lovebeats.firebase.cloudFunctions.NumLikesRestriction.deviceLikeCounter
import com.lovebeats.firebase.cloudFunctions.NumLikesRestriction.likesThreshold
import com.lovebeats.firebase.cloudFunctions.NumLikesRestriction.tempLikeCounterInMemory
import com.lovebeats.firebase.cloudFunctions.ProfileSongApi
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.firebase.database.FirebaseRetrieveLikedByUsersListener
import com.lovebeats.firebase.database.FirebaseRetrieveSingleUserListenerInterfaceFirestore
import com.lovebeats.glide.ImageLoaderModule
import com.lovebeats.models.*
import com.lovebeats.services.BackgroundLocationIntentService
import com.lovebeats.services.ForegroundBackgroundListener
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.subscriptions.viewModels.BillingViewModel
import com.lovebeats.subscriptions.views.SubscriptionActivity
import com.lovebeats.ui.SpacesImagesViewModel
import com.lovebeats.ui.favorites.FavoritesProfileListRecyclerAdapter
import com.lovebeats.ui.favorites.GenresProfileAdapter
import com.lovebeats.ui.favorites.MovieListActivity
import com.lovebeats.ui.favorites.MusicListActivity
import com.lovebeats.ui.home.BottomNavigationBarActivity
import com.lovebeats.ui.home.SendMessageDialogFragment
import com.lovebeats.ui.home.settings.SettingsActivity
import com.lovebeats.ui.onboarding.Icebreaker1Activity
import com.lovebeats.utils.*
import com.lovebeats.utils.Constants.conversations
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*


class BrowseProfilesActivity : BottomNavigationBarActivity() {

    private lateinit var mLayoutContainer: FrameLayout

    private lateinit var professionTextViewTitle: TextView
    private lateinit var professionTextView: TextView
    private lateinit var userHeightTextView: TextView
    private lateinit var distanceTextView: TextView
    private lateinit var userAgeTextView: TextView
    private lateinit var educationTextViewTitle: TextView
    private lateinit var educationTextView: TextView
    private lateinit var hometownTextViewTitle: TextView
    private lateinit var hometownTextView: TextView
    private lateinit var iceBreakerQuestion1TextView: TextView
    private lateinit var iceBreakerAnswer1TextView: TextView
    private lateinit var iceBreakerQuestion2TextView: TextView
    private lateinit var iceBreakerAnswer2TextView: TextView
    private lateinit var userNameTextView: TextView
    private lateinit var buttonGhostButton: Button
    private lateinit var firstPhotoImageView: ImageView
    private lateinit var secondPhotoImageView: ImageView
    private lateinit var thirdPhotoImageView: ImageView
    private lateinit var firstIcebreakerBackgroundImageView: ImageView
    private lateinit var secondIcebreakerBackgroundImageView: ImageView
    private lateinit var fourthPhotoImageView: ImageView
    private lateinit var fifthPhotoImageView: ImageView
    private lateinit var sixthPhotoImageView: ImageView
    private lateinit var settingsTopImageView: ImageView

    private lateinit var favoritesRecyclerView: RecyclerView
    private lateinit var genresRecyclerView: RecyclerView

    private lateinit var progressBar: ProgressBar
    private lateinit var topHeelsIcon: ImageView
    private lateinit var bottomHeelsIcon: ImageView
    private lateinit var bottomHeelsTextViewPreview: TextView
    private lateinit var bottomHeelsSectionLayout: ConstraintLayout
    private lateinit var progressTextView: TextView
    private lateinit var locationIconImageView: ImageView
    private lateinit var ageHeaderTextView: TextView
    private lateinit var userLocationTextView: TextView

    private lateinit var iceBreaker1Layout: ConstraintLayout
    private lateinit var iceBreaker2Layout: ConstraintLayout

    private lateinit var activityBrowseProfilesConstraintLayout: ConstraintLayout

    private lateinit var scrollBar: ScrollView

    private lateinit var likeButton: Button
    private lateinit var disLikeButton: Button

    private lateinit var lottieAnimationView: LottieAnimationView

    private lateinit var browseWithCityIcon: TextView
    private lateinit var browseWithCityTextView: TextView

    private lateinit var favoritesTextView: TextView
    private lateinit var genresTextView: TextView

    private lateinit var layoutEnableLocation: View
    private lateinit var layoutOutOfPeople: View
    private lateinit var userErrorLayout: View
    private lateinit var getUsersApiErrorLayout: View

    private lateinit var mContext: Context

    private var mainUserId = ""
    private var previousMatchUserId = ""

    private var mainUserInfo: User? = null

    private lateinit var mainUserGender: String

    var usersMembersMap: LinkedHashMap<String?, NearByUser?> = LinkedHashMap()
    var usersMembersKeysArrayList: ArrayList<String?> = ArrayList()
    private var usersMembersListCounter = 0

    private var firebaseDatabaseUtil: FirebaseDatabaseUtil? = null

    private val enableLocationInSettingsRequestCode = 2000

    private var userCurrentCity = ""

    var firstPhotoRef = ""
    var secondPhotoRef = ""
    var thirdPhotoRef = ""
    var fourthPhotoRef = ""
    var fifthPhotoRef = ""
    var sixthPhotoRef = ""

    private lateinit var spacesImagesViewModel: SpacesImagesViewModel

    private var isLoadingProfiles = false

    private lateinit var appLifecycleObserver: ForegroundBackgroundListener
    var appWentToBackground = false
    var reviewShown = false
    var freeTrailShown = false

    var shouldFetchUsers = true

    private lateinit var player: PlayerView

    private var isActivityShowing = false

    private lateinit var sendDMButton: Button

    companion object {

        fun newIntent(context: Context): Intent {

            return Intent(context, BrowseProfilesActivity::class.java)
        }
    }

    override fun getBottomNavigationBarItem(): Int {

        return R.id.navigation_home
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (!BuildConfig.DEBUG) {
            window.setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE)
        }

        spacesImagesViewModel = ViewModelProvider(this).get(SpacesImagesViewModel::class.java)
        checkForSubscriptionsStatusAndUpdateDB()

        init()

        if (!UserObject.isLocationSentToServer) {
            setLocation()
        }

        showMembers()

        appLifecycleObserver = ForegroundBackgroundListener(
            onAppGoesToBackground = {
                appWentToBackground = true
            },
            onAppEntersForeground = {
                if (isBrowseProfilesScreenActive &&
                    appWentToBackground && !isLoadingProfiles
                    && usersMembersMap.isEmpty()) {
                    Timber.d("Fetching users again after app brought up from background")
                    readMainUserInfo()
                }
                appWentToBackground = false
            })

        appLifecycleObserver.attach()

        val tutorialShown = AccountPreferences.getInstance(this).getBooleanValue(Constants.tutorialShown, false)
        if (!tutorialShown) {
            AccountPreferences.getInstance(this).setValue(Constants.tutorialShown, true)

            Handler(Looper.getMainLooper()).postDelayed({
                ToolTipView().showTour(this)
            }, 1200)
        }

        reviewShown = AccountPreferences.getInstance(this).getBooleanValue(Constants.reviewShown, false)
        freeTrailShown = AccountPreferences.getInstance(this).getBooleanValue(Constants.freeTrailShown, false)
    }

    private fun init() {

        Timber.d("Browse profiles activity init")

        mContext = this

        firebaseDatabaseUtil = FirebaseDatabaseUtil(mContext)

        mLayoutContainer = findViewById(R.id.layout_container)

        val browseProfilesLayout = View.inflate(this, R.layout.activity_browse_profiles, null)
        mLayoutContainer.addView(browseProfilesLayout)

        settingsTopImageView = findViewById(R.id.settings_top_image_view)

        settingsTopImageView.setOnClickListener {
            startPreferencesFragment()
        }

        scrollBar = findViewById(R.id.view_scroll_view)

        // Configure Product Designer component
        professionTextViewTitle = findViewById(R.id.profession_text_view)
        professionTextView = findViewById(R.id.user_profession_text_view)

        favoritesTextView = findViewById(R.id.favorites_text_view)
        genresTextView = findViewById(R.id.genres_text_view)

        // Configure Age component
        userHeightTextView = findViewById(R.id.user_height1_text_view)

        // Configure Education component
        educationTextViewTitle = findViewById(R.id.education_text_view)
        educationTextView = findViewById(R.id.user_education_text_view)

        locationIconImageView = findViewById(R.id.location_image_view)

        // Configure Hometown component
        hometownTextViewTitle = findViewById(R.id.hometown_text_view)
        hometownTextView = findViewById(R.id.user_home_town_text_view)

        // Configure How do you describe component
        iceBreakerQuestion1TextView = findViewById(R.id.ice_breaker_1_question_text_view)

        // Configure I’m definitely fun a component
        iceBreakerAnswer1TextView = findViewById(R.id.ice_breaker_1_answer_text_view)

        // Configure How do you describe component
        iceBreakerQuestion2TextView = findViewById(R.id.ice_breaker_2_question_text_view)

        // Configure I’m definitely fun a component
        iceBreakerAnswer2TextView = findViewById(R.id.ice_breaker_2_answer_text_view)

        // Configure Jon component
        userNameTextView = findViewById(R.id.user_name_text_view)

        ageHeaderTextView = findViewById(R.id.age_text_view)

        likeButton = findViewById(R.id.like_button)

        disLikeButton = findViewById(R.id.dislike_button)

        sendDMButton = findViewById(R.id.dm_button)

        userAgeTextView = findViewById(R.id.user_age1_text_view)

        firstPhotoImageView = findViewById(R.id.user_first_photo_image_view)

        secondPhotoImageView = findViewById(R.id.user_second_photo_image_view)

        thirdPhotoImageView = findViewById(R.id.user_third_photo_image_view)

        fourthPhotoImageView = findViewById(R.id.user_fourth_photo_image_view)

        fifthPhotoImageView = findViewById(R.id.user_fifth_photo_image_view)

        sixthPhotoImageView = findViewById(R.id.user_sixth_photo_image_view)

        favoritesRecyclerView = findViewById(R.id.favorites_view_recycler_view)

        genresRecyclerView = findViewById(R.id.genres_view_recycler_view)

        iceBreaker1Layout = findViewById(R.id.profilecard_blank_photo_copy_constraint_layout)

        iceBreaker2Layout = findViewById(R.id.profilecard_blank_photo_copy3_constraint_layout)

        activityBrowseProfilesConstraintLayout = findViewById(R.id.activity_browse_profiles)

        firstIcebreakerBackgroundImageView = findViewById(R.id.rectangle_image_view)
        secondIcebreakerBackgroundImageView = findViewById(R.id.rectangle_two_image_view)

        layoutEnableLocation = findViewById(R.id.layout_enable_location)
        layoutOutOfPeople = findViewById(R.id.layout_out_of_people)
        userErrorLayout = findViewById(R.id.user_error)
        getUsersApiErrorLayout = findViewById(R.id.get_users_api_error_container)
        browseWithCityIcon = findViewById(R.id.browse_city_with_icon)
        browseWithCityTextView = findViewById(R.id.browse_city_text)

        setImageViewHeightBasedOnDeviceWidth()

        distanceTextView = findViewById(R.id.distance_text_view)

        userLocationTextView = findViewById(R.id.user_location_text_view)

        topHeelsIcon = findViewById(R.id.top_heels_icon)
        bottomHeelsIcon = findViewById(R.id.boots_bottom_image_view)
        bottomHeelsTextViewPreview = findViewById(R.id.user_match_boot_preview_text_view)
        bottomHeelsSectionLayout = findViewById(R.id.group10_constraint_layout)

        progressBar = findViewById(R.id.progressBar)
        progressTextView = findViewById(R.id.progressTextView)

        player = findViewById(R.id.player)

        showLoading()

        lottieAnimationView = findViewById(R.id.user_like_dislike_animation_view)

        mainUserGender = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")
        mainUserId = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "")

        // Configure button/ghost component
        buttonGhostButton = findViewById(R.id.report_user_button)
        buttonGhostButton.setOnClickListener { view ->
            this.onButtonGhostPressed()
        }

        likeButton.setOnClickListener { view ->
            val movieGenreList = AccountPreferences.getInstance(this).getStringSetValue(Constants.movieGenres, setOf())
            val musicGenreList = AccountPreferences.getInstance(this).getStringSetValue(Constants.musicGenres, setOf())
            val iceBreakerQuestion1 = AccountPreferences.getInstance(this).getStringValue(Constants.iceBreaker1, "")
            val iceBreakerQuestion2 = AccountPreferences.getInstance(this).getStringValue(Constants.iceBreaker2, "")

            if (movieGenreList.isEmpty()) {
                AlertDialogView.showAlertDialog(context = this,
                    title = getString(R.string.complete_profile),
                    message = getString(R.string.complete_profile_desc),
                    buttonPositiveText = getString(R.string.continue_text),
                    buttonNegativeText = getString(R.string.cancel)) { dialog, which ->
                    if (which == DialogInterface.BUTTON_POSITIVE) {
                        startActivity(MovieListActivity.newIntent(this))
                    } else {
                        dialog.cancel()
                    }
                }
            } else if (musicGenreList.isEmpty()) {
                AlertDialogView.showAlertDialog(context = this,
                    title = getString(R.string.complete_profile),
                    message = getString(R.string.complete_profile_desc),
                    buttonPositiveText = getString(R.string.continue_text),
                    buttonNegativeText = getString(R.string.cancel)) { dialog, which ->
                    if (which == DialogInterface.BUTTON_POSITIVE) {
                        startActivity(MusicListActivity.newIntent(this))
                    } else {
                        dialog.cancel()
                    }
                }
            } else if (iceBreakerQuestion1.isNullOrEmpty() && iceBreakerQuestion2.isNullOrEmpty()) {
                AlertDialogView.showAlertDialog(context = this,
                    title = getString(R.string.complete_profile),
                    message = getString(R.string.complete_profile_desc),
                    buttonPositiveText = getString(R.string.continue_text),
                    buttonNegativeText = getString(R.string.cancel)) { dialog, which ->
                    if (which == DialogInterface.BUTTON_POSITIVE) {
                        startActivity(Icebreaker1Activity.newIntent(this))
                    } else {
                        dialog.cancel()
                    }
                }
            } else {
              onLikeButtonClicked()
           }
        }

        disLikeButton.setOnClickListener { view ->
            onDisLikeButtonClicked()
        }

        hideProfession()
        hideHomeTown()
        hideSchool()

        if (deviceDateBasedOnServer != null) {
            readMainUserInfo()
        }else {
             NumLikesRestriction.setDeviceLikeCounterAndDate(this) {
                readMainUserInfo()
            }
        }

        UserObject.userPreferencesChangedLiveData.observe(this) { changed ->
            Timber.d("User preferences changed")
            if (changed && !isLoadingProfiles) {
                UserObject.userPreferencesUpdatedAPICalled = false
                hideOutOfPeopleLayout()
                readMainUserInfo()
                UserObject.userPreferencesChangedLiveData.postValue(false)
            }
        }

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun startPreferencesFragment() {
        val intent = SettingsActivity.newIntent(this)
        intent.putExtra(Constants.PREFERENCES_FRAGMENT_HOME_PROFILE_ACTIVITY, true)
        startActivity(intent)
    }

    private fun startTeleportFragment() {
        val intent = SettingsActivity.newIntent(this)
        intent.putExtra(Constants.PREFERENCES_FRAGMENT_TELEPORT_FRAGMENT, true)
        startActivity(intent)
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun hideLoading() {

        isLoadingProfiles = false

        progressBar.visibility = View.GONE
        progressTextView.visibility = View.GONE

        likeButton.visibility = View.VISIBLE
        disLikeButton.visibility = View.VISIBLE

        likeButton.isEnabled = true
        disLikeButton.isEnabled = true
        scrollBar.setOnTouchListener { _, _ -> false}

        firstPhotoImageView.visibility = View.VISIBLE
        userNameTextView.visibility = View.VISIBLE
        userAgeTextView.visibility = View.VISIBLE
        topHeelsIcon.visibility = View.VISIBLE
        userHeightTextView.visibility = View.VISIBLE
        distanceTextView.visibility = View.VISIBLE
        userLocationTextView.visibility = View.VISIBLE
        locationIconImageView.visibility = View.VISIBLE
        ageHeaderTextView.visibility = View.VISIBLE
        professionTextView.visibility = View.VISIBLE
        professionTextViewTitle.visibility = View.VISIBLE

        favoritesRecyclerView.visibility = View.VISIBLE
        genresRecyclerView.visibility = View.VISIBLE
        favoritesTextView.visibility = View.VISIBLE
        genresTextView.visibility = View.VISIBLE
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun showLoading() {

        scrollBar.scrollTo(0, 0)

        favoritesRecyclerView.visibility = View.GONE
        genresRecyclerView.visibility = View.GONE
        favoritesTextView.visibility = View.INVISIBLE
        genresTextView.visibility = View.INVISIBLE

        player.releasePlayer()
        player.visibility = View.GONE

        hideOutOfPeopleLayout()
        hideGetNearbyUsersErrorLayout()

        isLoadingProfiles = true

        progressBar.visibility = View.VISIBLE
        progressBar.isIndeterminate = true
        progressTextView.visibility = View.VISIBLE

        firstPhotoImageView.background = ColorDrawable(resources.getColor(R.color.grey4))
        firstPhotoImageView.visibility = View.INVISIBLE
        userNameTextView.visibility = View.INVISIBLE
        userAgeTextView.visibility = View.INVISIBLE
        topHeelsIcon.visibility = View.INVISIBLE
        userHeightTextView.visibility = View.INVISIBLE
        distanceTextView.visibility = View.INVISIBLE
        userLocationTextView.visibility = View.INVISIBLE
        locationIconImageView.visibility = View.INVISIBLE
        ageHeaderTextView.visibility = View.INVISIBLE
        professionTextView.visibility = View.INVISIBLE
        professionTextViewTitle.visibility = View.INVISIBLE

        likeButton.isEnabled = false
        disLikeButton.isEnabled = false
        scrollBar.setOnTouchListener { _, _ -> true }
    }

    private fun retrieveProfilePhotoAndSaveToPrefs() {
        spacesImagesViewModel.getImageUrl("$mainUserId/${Constants.photoFileName1}") { url, _ ->
            url?.let {
                AccountPreferences.getInstance(this).setValue(Constants.userProfilePhotoUrl, it)
            }
        }
    }

    private fun onButtonGhostPressed() {
        val appLogic = AppLogic(mContext, mainUserId, previousMatchUserId)
        appLogic.reportUser(shouldShowUnmatchButton = false)
    }

    private fun readMainUserInfo() {
        hideLocationLockedLayout()
        if (UserObject.shouldFetchUserFromServer()) {
            firebaseDatabaseUtil?.readMainUserInfoFromFirebaseFirestore(object :
                FirebaseRetrieveSingleUserListenerInterfaceFirestore {
                override fun onSuccess(user: User) {
                    mainUserInfo = user
                    userCurrentCity = mainUserInfo?.city?.toString() ?: ""
                    mainUserInfo?.let { UserObject.setUserDataStore(it, mainUserId)}
                    readUserLocationAndFetchNearbyUsers()
                }
                override fun onFailure() {
                    Timber.e("failed reading main user info in browse profiles")
                }
            })
        }else {
            mainUserInfo = UserObject.user
            userCurrentCity = mainUserInfo?.city?.toString() ?: ""
            readUserLocationAndFetchNearbyUsers()
        }
        retrieveProfilePhotoAndSaveToPrefs()
    }

    private fun readUserLocationAndFetchNearbyUsers() {

        Timber.d("Read user location and fetch nearby users")

        resetMemoryCachedProfiles()

        showLoading()

        Handler(Looper.getMainLooper()).postDelayed({

            hideBrowsingInCityView()

            var lat = AccountPreferences.getInstance(mContext).getDoubleValue(Constants.latitude, 0.0)
            var long = AccountPreferences.getInstance(mContext).getDoubleValue(Constants.longitude, 0.0)

            UserObject.selectedUnlockedLocation?.let {
                lat = it.latitude ?: 0.0
                long = it.longitude ?: 0.0
                showBrowsingInCityView()
                hideLocationLockedLayout()
            }

//            lat = 17.***************
//            long = 78.**************

            if (lat == 0.0 || long == 0.0) {

                setLocation()

                val localBroadcastManager = LocalBroadcastManager.getInstance(mContext)
                localBroadcastManager.registerReceiver(locationBroadcastReceiver, IntentFilter(
                    BackgroundLocationIntentService.LOCATION_FETCH_STATUS_ACTION))
            } else {

                readNearbyUsers(lat, long)
            }
        }, 200)
    }

    private fun readNearbyUsers(searchLat: Double, searchLong: Double) {
        val distanceFromPrefs = AccountPreferences.getInstance(applicationContext).getIntValue(
            Constants.distance, Constants.defaultDistance)

        if (mainUserId.isEmpty()) {
            mainUserId = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "")
        }

        Timber.d("Getting users for distance: $distanceFromPrefs")

        NearByUsers.getNearByUsers(this, mainUserId, searchLat, searchLong, distanceFromPrefs) { nearbyUsersList, apiStatus ->
            if (apiStatus == GetUsersResponse.SUCCESS) {
                if (!nearbyUsersList.isNullOrEmpty()) {
                    Timber.d("Received users size: ${nearbyUsersList.size}")
                    val sortedNearByUsersList = nearbyUsersList.sortedBy { it.distance }

                    for (nearByUser in sortedNearByUsersList) {
                        val nearByUserUid = nearByUser.uid
                        if (!usersMembersMap.containsKey(nearByUserUid)) {
                            usersMembersMap[nearByUserUid] = nearByUser
                            usersMembersKeysArrayList.add(nearByUserUid)
                        } else {
                            Timber.d("Not a valid user: Key already exists")
                        }
                    }

                    hideLoading()
                    hideOutOfPeopleLayout()

                    showNextProfile(USERLIKINGSTATUS.NOTDECIDED, usersMembersKeysArrayList)
                } else {
                    shouldFetchUsers = false
                    showOutOfPeopleLayout()
                }
            } else {
                Timber.e("Get users API Failed, Showing error layout")
                showGetNearbyUsersErrorLayout()
            }
        }
    }

    private fun removeFirstUserItem(userKey: String?) {
        usersMembersKeysArrayList.removeAt(0)
        usersMembersMap.remove(userKey)
    }

    private fun onLikeButtonClicked() {
        tempLikeCounterInMemory++
//        if (!reviewShown && tempLikeCounterInMemory == 4) {
//            AppReview().showEnjoyingLoveBeatDialog(this)
//            AccountPreferences.getInstance(this).setValue(Constants.reviewShown, true)
//        }
        if(UserObject.isLoveBeatPlusUser == false) { // check if user is free user
            if (!freeTrailShown && deviceLikeCounter == 10) {
                startSubscriptionActivity()
                AccountPreferences.getInstance(this).setValue(Constants.freeTrailShown, true)
            }
            if (deviceLikeCounter >= likesThreshold) {
                // Should show subscription screen
                val subIntent = SubscriptionActivity.newIntent(this)
                subIntent.putExtra(
                    SubscriptionActivity.SUBSCRIPTION_ORIGIN,
                    SubscriptionActivity.OUT_OF_LIKES
                )
                startActivity(subIntent.launchModeWithSingleTop())
            }else {
                likeUser()
                deviceLikeCounter++
                NumLikesRestriction.updateLikeCounterOnDeviceAndServer(this)
            }
        }else {
            likeUser()
        }
    }

    private fun likeUser() {
        usersMembersListCounter.plus(1)
        showNextProfile(USERLIKINGSTATUS.LIKE, usersMembersKeysArrayList)
        AnalyticsTrackingService.logEvent(this, LIKE_USER)
        MixPanelAnalyticsTrackingService.logEvent(this, LIKE_USER)
    }

    private fun onDisLikeButtonClicked() {
        usersMembersListCounter.plus(1)
        showNextProfile(USERLIKINGSTATUS.DISLIKE, usersMembersKeysArrayList)
        AnalyticsTrackingService.logEvent(this, DISLIKE_USER)
        MixPanelAnalyticsTrackingService.logEvent(this, DISLIKE_USER)
    }

    private fun showNextProfile(previousUserLikeStatus: USERLIKINGSTATUS, usersKeysArrayList: ArrayList<String?>) {
        if (previousUserLikeStatus != USERLIKINGSTATUS.NOTDECIDED) {
            if (usersMembersKeysArrayList.isNotEmpty()) {
                removeFirstUserItem(previousMatchUserId)
            } else {
                if (!shouldFetchUsers) {
                    showOutOfPeopleLayout()
                }
            }
        } else if (previousUserLikeStatus == USERLIKINGSTATUS.NOTDECIDED) {
            if (usersMembersKeysArrayList.isEmpty()) {
                if (!shouldFetchUsers) {
                    showOutOfPeopleLayout()
                }
            }
        }
        lottieAnimationView.removeAllAnimatorListeners()
        lottieAnimationView.invalidate()

        clearViewData()

        if (usersKeysArrayList.isEmpty()) {

            if ("" != previousMatchUserId) {

                when (previousUserLikeStatus) {
                    USERLIKINGSTATUS.LIKE -> {

                        firebaseDatabaseUtil?.likeUser(mainUserId, previousMatchUserId)

                        checkForMutualMatch(mainUserId, previousMatchUserId, true)

                        previousMatchUserId = ""
                    }
                    USERLIKINGSTATUS.DISLIKE -> {

                        firebaseDatabaseUtil?.disLikeUser(mainUserId, previousMatchUserId)
                        previousMatchUserId = ""

                        showOutOfPeopleLayout()
                    }
                    else -> {

                        previousMatchUserId = ""
                        showOutOfPeopleLayout()
                    }
                }
            } else {
                showOutOfPeopleLayout()
            }
        } else {

            var userKey: String? = null
            var user: NearByUser? = null

            userKey = usersKeysArrayList[usersMembersListCounter]
            user = usersMembersMap[userKey]

            firstPhotoRef = "$userKey/${Constants.photoFileName1}"
            secondPhotoRef = "$userKey/${Constants.photoFileName2}"
            thirdPhotoRef = "$userKey/${Constants.photoFileName3}"
            fourthPhotoRef = "$userKey/${Constants.photoFileName4}"
            fifthPhotoRef = "$userKey/${Constants.photoFileName5}"
            sixthPhotoRef = "$userKey/${Constants.photoFileName6}"

            if (previousUserLikeStatus == USERLIKINGSTATUS.LIKE) {

                lottieAnimationView.elevation = 10f
                lottieAnimationView.setAnimation(R.raw.like)
                lottieAnimationView.playAnimation()

                lottieAnimationView.addAnimatorListener(object : Animator.AnimatorListener {
                    override fun onAnimationRepeat(animation: Animator) {
                        Timber.d("animation repeated")
                    }

                    override fun onAnimationCancel(animation: Animator) {
                        Timber.d("animation cancelled")
                        showUserData(user, userKey)
                    }

                    override fun onAnimationStart(animation: Animator) {
                        //Timber.d("animation started like")
                    }

                    override fun onAnimationEnd(animation: Animator) {
                        //Timber.d("animation ended")
                        showUserData(user, userKey)
                    }
                })

                if (!TextUtils.isEmpty(previousMatchUserId)) {

                    firebaseDatabaseUtil?.likeUser(mainUserId, previousMatchUserId)

                    //There is a match between these two users, send the match details to firebase
                    //there is one more place we are doing the same above, if we update here, update there as well
                    checkForMutualMatch(mainUserId, previousMatchUserId, false)

                    previousMatchUserId = ""
                }
            } else if (previousUserLikeStatus == USERLIKINGSTATUS.DISLIKE) {

                lottieAnimationView.elevation = 10f
                lottieAnimationView.setAnimation(R.raw.dislike)
                lottieAnimationView.playAnimation()

                lottieAnimationView.addAnimatorListener(object : Animator.AnimatorListener {
                    override fun onAnimationRepeat(animation: Animator) {
                        Timber.d("animation repeated")
                    }

                    override fun onAnimationCancel(animation: Animator) {
                        Timber.d("animation cancelled")
                        showUserData(user, userKey)
                    }

                    override fun onAnimationStart(animation: Animator) {
                        Timber.d("animation started dislike")
                    }

                    override fun onAnimationEnd(animation: Animator) {
                        Timber.d("animation ended")
                        showUserData(user, userKey)
                    }
                })


                if (!TextUtils.isEmpty(previousMatchUserId)) {

                    firebaseDatabaseUtil?.disLikeUser(mainUserId, previousMatchUserId)

                    previousMatchUserId = ""

                }
            } else if (previousUserLikeStatus == USERLIKINGSTATUS.NOTDECIDED) {

                showUserData(user, userKey)
            }
        }
    }

    private fun showUserData(user: NearByUser?, userKey: String?) {

        try {

            setFavorites(user)

            setProfileSong(user)

            var iceBreaker1Questions: Any = ""

            var iceBreaker1Question = ""
            var iceBreaker1Answer = ""
            var iceBreaker2Question = ""
            var iceBreaker2Answer = ""

            if (user?.iceBreaker1 != null) {
                if (user.iceBreaker1 is Map<*, *>) {

                    iceBreaker1Questions = user.iceBreaker1

                    for (key in iceBreaker1Questions.keys) {
                        iceBreaker1Question = key.toString()
                    }

                    iceBreaker1Answer = iceBreaker1Questions[iceBreaker1Question].toString()
                }
            }

            var iceBreaker2Questions: Any = ""
            if (user?.iceBreaker2 != null) {
                if (user.iceBreaker2 is Map<*, *>) {
                    iceBreaker2Questions = user.iceBreaker2

                    for (key2 in iceBreaker2Questions.keys) {
                        iceBreaker2Question = key2.toString()
                    }

                    iceBreaker2Answer = iceBreaker2Questions[iceBreaker2Question].toString()
                }
            }

            if (iceBreaker1Question.isNotEmpty() && iceBreaker1Answer.isNotEmpty()) {

                iceBreaker1Layout.visibility = View.VISIBLE
                iceBreakerQuestion1TextView.text = iceBreaker1Question
                iceBreakerAnswer1TextView.text = iceBreaker1Answer
            } else {

                iceBreaker1Layout.visibility = View.GONE
            }

            if (iceBreaker2Question.isNotEmpty() && iceBreaker2Answer.isNotEmpty()) {

                iceBreaker2Layout.visibility = View.VISIBLE
                iceBreakerQuestion2TextView.text = iceBreaker2Question
                iceBreakerAnswer2TextView.text = iceBreaker2Answer
            } else {

                iceBreaker2Layout.visibility = View.GONE
            }


            setPhotoIntoImageViewWithIceBreaker(this, firstPhotoRef, firstPhotoImageView, firstIcebreakerBackgroundImageView)

            setPhotoIntoImageViewWithIceBreaker(this, secondPhotoRef, secondPhotoImageView, secondIcebreakerBackgroundImageView)

            setPhotoIntoImageViewWithIceBreaker(this, thirdPhotoRef, thirdPhotoImageView, null)

            setPhotoIntoImageViewWithIceBreaker(this, fourthPhotoRef, fourthPhotoImageView, null)

            setPhotoIntoImageViewWithIceBreaker(this, fifthPhotoRef, fifthPhotoImageView, null)

            setPhotoIntoImageViewWithIceBreaker(this, sixthPhotoRef, sixthPhotoImageView, null)

            val sdf = SimpleDateFormat("MM/dd/yyyy", Locale.US)
            val date = sdf.parse(user?.dob.toString())

            var school = ""

            val userGender = user?.gender ?: ""

            if (mainUserGender == userGender) {

                hideHeelsIcon()
            } else if (mainUserGender == GenderType.woman.toString() && userGender == GenderType.man.toString()) {

                // get mainuser height in inches
                // get other user height in inches
                // pass these two as parameters to HeelsLogic class
                // get back top-badge-id, bottom-badge-id and text copy from HeelsLogic class
                // assign them to these below views
                val mainUserHeightInInches = AccountPreferences.getInstance(mContext).getDoubleValue(
                    Constants.height, 0.0).toString().getDoubleValue()?.getIntValue()
                val otherUserHeightInInches = user?.height.toString().getDoubleValue()?.getIntValue()

                if (otherUserHeightInInches != null &&
                        mainUserHeightInInches != null) {

                    HeelsLogic.getBadgeAndText(otherUserHeightInInches, mainUserHeightInInches, user?.name.toString().trim()) { topHeelsIconId, bottomHeelsIconId, bottomHeelsText, shouldShowComponent ->

                        if (shouldShowComponent) {

                            showHeelsIcon()

                            topHeelsIcon.setImageResource(topHeelsIconId)
                            bottomHeelsIcon.setImageResource(bottomHeelsIconId)
                            bottomHeelsTextViewPreview.text = bottomHeelsText
                        } else {

                            hideHeelsIcon()
                        }
                    }
                } else {

                    hideHeelsIcon()
                }
            } else {

                hideHeelsIcon()
            }

            userNameTextView.text = user?.name.toString()
            val userHeightInInches = Utils.heightInFeetFromInchesWithQuotes(user?.height.toString().toDouble())

            userHeightTextView.text =  userHeightInInches
            userAgeTextView.text = Utils.getAge(date).toString()


            if (!user?.school.isNullOrEmpty()) {
                school = user?.school.toString()
            }

            if (!user?.school2.isNullOrEmpty()) {
                school = school + "\n" + user?.school2.toString()
            }

            if (user?.profession.isNullOrEmpty()) {
                hideProfession()
            } else {
                showProfession()
                professionTextView.text = user?.profession.toString()
            }

            if (user?.school.isNullOrEmpty()) {
                hideSchool()
            } else {
                showSchool()
                educationTextView.text = school
            }

            if (user?.homeTown.isNullOrEmpty()) {
                hideHomeTown()
            } else {
                showHomeTown()
                hometownTextView.text = user?.homeTown.toString()
            }

            val userCity = user?.city
            var location = ""
            if (userCity != null) {
                location = "$userCity"
            }

            val distance = user?.distance

            if (distance != null) {
                if (distance == 0 || distance == 1) {
                    if (location != "") {
                        userLocationTextView.text = location
                        distanceTextView.text = "<1 km away"
                    }else {
                        distanceTextView.text = "<1 km away"
                    }
                } else {
                    if (location != "") {
                        userLocationTextView.text = location
                        distanceTextView.text = "$distance kms away"
                    }else {
                        distanceTextView.text = "$distance kms away"
                    }
                }
            }

            previousMatchUserId = userKey.toString()

            user?.uid?.let { id ->
                sendDMButton.setOnClickListener {
                    if (UserObject.isLoveBeatPlusUser == true) {
                        val dialog = SendMessageDialogFragment.newInstance(id, firstPhotoRef, user.name.toString(), spacesImagesViewModel) {
                            onLikeButtonClicked()
                        }
                        dialog.show(supportFragmentManager, "SendMessageDialog")
                    } else {
                        startSubscriptionActivity()
                    }
                }
            }

        } catch (e: Exception) {

            Timber.e("exception in showing user profile: $e")
        }
    }

    private fun setProfileSong(user: NearByUser?) {
        if (isActivityShowing) {
            val map = user?.profileSong as? Map<String, String?>
            if (map != null) {
                val song = ProfileSongApi.parseProfileSongFromFireStore(map)
                player.visibility = View.VISIBLE
                player.show(this,
                    Song(song?.id,
                        song?.imageUrl,
                        song?.audioUrl,
                        song?.title,
                        song?.subTitle,
                        song?.spotifyUrl))
            } else {
                player.visibility = View.GONE
            }
        }
    }

    private fun setFavorites(user: NearByUser?) {
        try {
            favoritesRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
            val favoritesData:Map<String, Map<String, List<String>>>? = user?.favorites as? Map<String, Map<String, List<String>>>
            favoritesData?.let {data ->
                val list = Favorites.parseFavoritesFromFireStoreObject(data)
                val favoritesListRecyclerAdapter = FavoritesProfileListRecyclerAdapter(list, this)
                favoritesRecyclerView.adapter = favoritesListRecyclerAdapter
            }
            val movieList:List<String> = user?.movieGenres as? ArrayList<String>?: emptyList()
            val musicList:List<String> = user?.musicGenres as? ArrayList<String>?: emptyList()
            val totalList: List<String> = movieList + musicList
            val layoutManager = FlexboxLayoutManager(this)
            layoutManager.flexDirection = FlexDirection.ROW
            layoutManager.justifyContent = JustifyContent.FLEX_START
            genresRecyclerView.layoutManager = layoutManager
            val musicListRecyclerAdapter = GenresProfileAdapter(totalList, this)
            genresRecyclerView.adapter = musicListRecyclerAdapter
        } catch (ex: Exception) {
            Timber.e("Error in set favorites: $ex")
        }
    }

    private fun checkForMutualMatch(mainUserFirebaseId: String, otherUserFirebaseId: String, isLastProfile: Boolean) {

        firebaseDatabaseUtil?.getLikedByUsers(object : FirebaseRetrieveLikedByUsersListener {
            override fun onSuccess(isMutualMatch: Boolean) {
                if (isMutualMatch) {

                    AnalyticsTrackingService.logEvent(this@BrowseProfilesActivity, MUTUAL_MATCH)
                    MixPanelAnalyticsTrackingService.logEvent(this@BrowseProfilesActivity, MUTUAL_MATCH)

                    val firebaseDatabaseReference = FirebaseDatabase.getInstance().getReference(conversations).push()
                    val chatId = firebaseDatabaseReference.key

                    firebaseDatabaseUtil?.matchUsers(mainUserFirebaseId, otherUserFirebaseId, chatId)

                    val intent = Intent(mContext, MutualMatchActivity::class.java)
                    intent.putExtra(Constants.firebaseUserId, otherUserFirebaseId)
                    intent.putExtra(Constants.conversationId, chatId)
                    startActivity(intent)

                } else if (isLastProfile) {
                    showOutOfPeopleLayout()
                }
            }

            override fun onFailure() {
                if (isLastProfile) {
                    showOutOfPeopleLayout()
                }
            }
        }, mainUserFirebaseId, otherUserFirebaseId)
    }

    private fun resetMemoryCachedProfiles() {
        usersMembersMap = LinkedHashMap()
        usersMembersKeysArrayList = ArrayList()
        usersMembersListCounter = 0
        previousMatchUserId = ""
    }

    private fun clearViewData() {

        scrollBar.scrollTo(0, 0)

        userNameTextView.text = ""
        userAgeTextView.text = ""
        userHeightTextView.text = ""
        distanceTextView.text = ""
        userLocationTextView.text = ""
        professionTextView.text = ""
        educationTextView.text = ""
        hometownTextView.text = ""
        iceBreakerQuestion1TextView.text = ""
        iceBreakerAnswer1TextView.text = ""
        iceBreakerQuestion2TextView.text = ""
        iceBreakerAnswer2TextView.text = ""

        firstPhotoImageView.setImageResource(R.color.white)
        secondPhotoImageView.setImageResource(R.color.white)
        firstIcebreakerBackgroundImageView.setImageResource(R.color.white)
        secondIcebreakerBackgroundImageView.setImageResource(R.color.white)
        topHeelsIcon.setImageResource(R.color.white)

        thirdPhotoImageView.setImageResource(R.color.white)
        hidePhotoImageView(thirdPhotoImageView)
        fourthPhotoImageView.setImageResource(R.color.white)
        hidePhotoImageView(fourthPhotoImageView)
        fifthPhotoImageView.setImageResource(R.color.white)
        hidePhotoImageView(fifthPhotoImageView)
        sixthPhotoImageView.setImageResource(R.color.white)
        hidePhotoImageView(sixthPhotoImageView)

        player.releasePlayer()
    }

    enum class USERLIKINGSTATUS {
        LIKE,
        DISLIKE,
        NOTDECIDED
    }

    private fun hideProfession() {
        professionTextViewTitle.visibility = View.GONE
        professionTextView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showProfession() {
        professionTextViewTitle.visibility = View.VISIBLE
        professionTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun hideSchool() {
        educationTextView.visibility = View.GONE
        educationTextViewTitle.visibility = View.GONE

        invalidateLayout()
    }

    private fun showSchool() {
        educationTextViewTitle.visibility = View.VISIBLE
        educationTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun hideHomeTown() {
        hometownTextViewTitle.visibility = View.GONE
        hometownTextView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showHomeTown() {
        hometownTextViewTitle.visibility = View.VISIBLE
        hometownTextView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun hidePhotoImageView(photoView: ImageView) {

        photoView.visibility = View.GONE

        invalidateLayout()
    }

    private fun showPhotoImageView(photoView: ImageView) {

        photoView.visibility = View.VISIBLE

        invalidateLayout()
    }

    private fun invalidateLayout() {

        activityBrowseProfilesConstraintLayout.invalidate()
    }

    private fun setImageViewHeightBasedOnDeviceWidth() {
        val height = AppUtils.getHeightForImages()

        val firstImageLayoutParams = firstPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        firstImageLayoutParams?.height = height
        firstImageLayoutParams?.let { params ->
            firstPhotoImageView.layoutParams = params
        }

        val secondImageLayoutParams = secondPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        secondImageLayoutParams?.height = height
        secondImageLayoutParams?.let { params ->
            secondPhotoImageView.layoutParams = params
        }

        val thirdImageLayoutParams = thirdPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        thirdImageLayoutParams?.height = height
        thirdImageLayoutParams?.let { params ->
            thirdPhotoImageView.layoutParams = params
        }

        val fourthImageLayoutParams = fourthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        fourthImageLayoutParams?.height = height
        fourthImageLayoutParams?.let { params ->
            fourthPhotoImageView.layoutParams = params
        }

        val fifthImageLayoutParams = fifthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        fifthImageLayoutParams?.height = height
        fifthImageLayoutParams?.let { params ->
            fifthPhotoImageView.layoutParams = params
        }

        val sixthImageLayoutParams = sixthPhotoImageView.layoutParams as? ConstraintLayout.LayoutParams
        sixthImageLayoutParams?.height = height
        sixthImageLayoutParams?.let { params ->
            sixthPhotoImageView.layoutParams = params
        }
    }

    private fun setPhotoIntoImageViewWithIceBreaker(activity: Activity,
                                                    spacesPath: String,
                                                    actualImageView: ImageView,
                                                    icebreakerImageView: ImageView?) {

        ImageLoaderModule.loadImageIntoImageViewWithLoadingAndCallback(applicationContext, activity, spacesImagesViewModel, spacesPath, actualImageView, onSuccess = {
            showPhotoImageView(actualImageView)
        }, onFailure = {
            hidePhotoImageView(actualImageView)
        })

        if (icebreakerImageView != null) {
            ImageLoaderModule.loadImageIntoImageViewForIceBreakersCallback(applicationContext, activity, spacesImagesViewModel, spacesPath, icebreakerImageView, onSuccess = {
                showPhotoImageView(icebreakerImageView)
            }, onFailure = {
                hidePhotoImageView(icebreakerImageView)
            })
        }
    }
    private fun showMembers() {
        showNextProfile(USERLIKINGSTATUS.NOTDECIDED, usersMembersKeysArrayList)
    }

    private fun setOutOfPeopleLayoutListeners() {
        val imageView = layoutOutOfPeople.findViewById<ImageView>(R.id.icon_empty_magnifying_glass_image_view)
        val outOfPeopleTextView = layoutOutOfPeople.findViewById<TextView>(R.id.you_re_out_of_people_text_view)
        val expandSearchTextView = layoutOutOfPeople.findViewById<TextView>(R.id.try_expanding_your_ptext_view)
        val buttonOpenPrefs = layoutOutOfPeople.findViewById<Button>(R.id.button_open_preferences)

        imageView.setImageResource(R.drawable.ic_out_of_members)
        outOfPeopleTextView.text = getString(R.string.blank_out_of_people_activity_you_re_out_of_members_text_view_text)
        expandSearchTextView.text = getString(R.string.blank_out_of_people_for_guest)
        buttonOpenPrefs.setOnClickListener {
            startPreferencesFragment()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == enableLocationInSettingsRequestCode) {
            setLocation()
        }
    }

    private fun showHeelsIcon() {
        topHeelsIcon.visibility = View.VISIBLE
        bottomHeelsSectionLayout.visibility = View.VISIBLE
    }

    private fun hideHeelsIcon() {
        topHeelsIcon.visibility = View.INVISIBLE
        bottomHeelsSectionLayout.visibility = View.GONE
    }

    private fun setLocation() {

        val managePermissions = ManagePermissions(this, BackgroundLocationIntentService.locationPermissionsList, BackgroundLocationIntentService.locationPermissionRequestCode)

        if (managePermissions.isPermissionGranted()) {

            saveLocationDetails()

            hideEnableLocationLayout()
        } else {
            if (managePermissions.isPermissionDenied()) {

                handleDeniedLocationPermission()
            } else {

                managePermissions.checkPermissions()

                hideEnableLocationLayout()
            }
        }
    }

    private fun handleDeniedLocationPermission() {

        showEnableLocationLayout()
    }

    private fun saveLocationDetails() {

        val intent = Intent(this, BackgroundLocationIntentService::class.java)
        BackgroundLocationIntentService.enqueueWork(this, intent)
    }

    var locationBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context?, intent: Intent?) {

            if (intent != null) {
                val status = intent.getBooleanExtra(BackgroundLocationIntentService.LOCATION_FETCH_RESULT, false)
                if (status) {
                    hideUserErrorView()
                    readMainUserInfo()
                } else {
                    showUserErrorView()
                }
            } else {
                hideUserErrorView()
                readMainUserInfo()
            }
        }
    }

    private fun showEnableLocationLayout() {
        hideLoading()
        layoutEnableLocation.visibility = View.VISIBLE
        scrollBar.visibility = View.GONE
        val settingsButton = layoutEnableLocation.findViewById<Button>(R.id.button_open_settings)
        settingsButton?.setOnClickListener {
            startActivityForResult(Intent().apply {
                action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                data = Uri.fromParts("package", packageName, null)
            }, enableLocationInSettingsRequestCode)
        }
    }

    fun showOutOfPeopleLayout() {
        if (shouldFetchUsers && usersMembersKeysArrayList.isEmpty()) {
            showLoading()
            readUserLocationAndFetchNearbyUsers()
        }else {
            hideLoading()
            layoutOutOfPeople.visibility = View.VISIBLE
            scrollBar.visibility = View.GONE
            setOutOfPeopleLayoutListeners()
        }
    }

    private fun showUserErrorView() {
        hideLoading()
        userErrorLayout.visibility = View.VISIBLE
    }

    private fun hideEnableLocationLayout() {
        layoutEnableLocation.visibility = View.GONE
        scrollBar.visibility = View.VISIBLE
    }

    private fun hideUserErrorView() {
        userErrorLayout.visibility = View.GONE
    }

    private fun hideLocationLockedLayout() {
        scrollBar.visibility = View.VISIBLE
    }

    private fun hideOutOfPeopleLayout() {
        layoutOutOfPeople.visibility = View.GONE
        scrollBar.visibility = View.VISIBLE
    }

    private fun showBrowsingInCityView() {
        browseWithCityIcon.visibility = View.VISIBLE
        browseWithCityIcon.paint.isUnderlineText = true
        browseWithCityIcon.text = UserObject.selectedUnlockedLocation?.city
        browseWithCityTextView.visibility = View.VISIBLE
        browseWithCityIcon.setOnClickListener {
            startTeleportFragment()
        }
    }

    private fun hideBrowsingInCityView() {
        browseWithCityIcon.visibility = View.GONE
        browseWithCityTextView.visibility = View.GONE
    }

    private fun showGetNearbyUsersErrorLayout() {
        getUsersApiErrorLayout.visibility = View.VISIBLE
        val buttonTryAgain = getUsersApiErrorLayout.findViewById<Button>(R.id.button_try_again)

        buttonTryAgain.isEnabled = true
        likeButton.visibility = View.GONE
        disLikeButton.visibility = View.GONE

        buttonTryAgain?.setOnClickListener {
            buttonTryAgain.isEnabled = false
            readUserLocationAndFetchNearbyUsers()
        }
        Timber.e("getNearByUsers API error. Showing error screen to user")
    }

    private fun hideGetNearbyUsersErrorLayout() {
        getUsersApiErrorLayout.visibility = View.GONE
        likeButton.visibility = View.VISIBLE
        disLikeButton.visibility = View.VISIBLE
    }


    private fun checkForSubscriptionsStatusAndUpdateDB() {
        try {
            val billingClientLifecycle = (application as LoveBeatApplication).billingClientLifecycle
            val billingViewModel: BillingViewModel = ViewModelProvider(this).get(BillingViewModel::class.java)
            billingClientLifecycle.purchases.observe(this) { purchaseList ->
                if (!purchaseList.isNullOrEmpty()) {
                    billingViewModel.checkPurchasesAndAcknowledge(purchaseList)
                }
            }
        } catch (exception: java.lang.Exception) {
            Timber.d("Exception in check subscriptions: $exception")
        }
    }

    private fun startSubscriptionActivity() {
        val subIntent = SubscriptionActivity.newIntent(this)
        val influencerId = AccountPreferences.getInstance(applicationContext).getStringValue(
            Constants.influencerId, "")
        if (influencerId.isNotEmpty()) {
            subIntent.putExtra(SubscriptionActivity.INFLUENCER_FREE_TRAIL, true)
        }
        startActivity(subIntent.launchModeWithSingleTop())
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        if (usersMembersMap.isEmpty() && !isLoadingProfiles) {
            readMainUserInfo()
        }
        isBrowseProfilesScreenActive = true
    }


    override fun onDestroy() {
        LocalBroadcastManager.getInstance(this).unregisterReceiver(locationBroadcastReceiver)
        super.onDestroy()
        appLifecycleObserver.detach()
    }

    override fun onResume() {
        super.onResume()
        isActivityShowing = true
    }
    public override fun onPause() {
        super.onPause()
        isActivityShowing = false
        player.releasePlayer()
    }

    public override fun onStop() {
        super.onStop()
        isActivityShowing = false
        player.releasePlayer()
    }
}
