package com.lovebeats.ui.home.settings

import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.R
import com.lovebeats.ui.adapter.SettingsHomeSupernovaActivityViewRecyclerViewAdapter
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Activities that contain this fragment must implement the
 * [SettingsSettingsFragment.OnFragmentInteractionListener] interface
 * to handle interaction events.
 * Use the [SettingsSettingsFragment.newInstance] factory method to
 * create an instance of this fragment.
 *
 */
class SettingsSettingsFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private var param1: String? = null
    private var param2: String? = null

    private lateinit var viewRecyclerView: RecyclerView
    private val homeSettingsData: ArrayList<String> = ArrayList()

    private lateinit var headerLeftBackImageView: ImageView

    private lateinit var preferencesSettingsFragment: Fragment
    private lateinit var notificationsSettingsFragment: Fragment
    private lateinit var accountSettingsFragment: Fragment
    private lateinit var settingsFragment: Fragment

    private lateinit var appVersionTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
        }

        preferencesSettingsFragment = PreferencesFragment()
        notificationsSettingsFragment = SettingsNotificationFragment()
        accountSettingsFragment = SettingsAccountFragment()
        settingsFragment = SettingsFragment()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_settings_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        init(view)
    }

    interface OnFragmentInteractionListener {
        // TODO: Update argument type and name
        fun onFragmentInteraction(uri: Uri)
    }

    companion object {
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @param param1 Parameter 1.
         * @param param2 Parameter 2.
         * @return A new instance of fragment SettingsSettingsFragment.
         */
        // TODO: Rename and change types and number of parameters
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
                SettingsSettingsFragment().apply {
                    arguments = Bundle().apply {
                        putString(ARG_PARAM1, param1)
                        putString(ARG_PARAM2, param2)
                    }
                }
    }

    private fun init(layout:View) {

        viewRecyclerView = layout.findViewById(R.id.settings_view_recycler_view)
        headerLeftBackImageView = layout.findViewById(R.id.settings_header_left_image_view)
        appVersionTextView = layout.findViewById(R.id.app_version)

        if (homeSettingsData.isEmpty()) {
            addHomeSettingsData()
        }

        // Configure View component
        viewRecyclerView.layoutManager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        viewRecyclerView.adapter = SettingsHomeSupernovaActivityViewRecyclerViewAdapter(activity, homeSettingsData, { settingsItem: String -> settingsItemClicked(settingsItem) }, 1)

        headerLeftBackImageView.setOnClickListener {
            parentFragmentManager.beginTransaction().setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE).replace(R.id.main_container, settingsFragment).commit()
        }

        appVersionTextView.text = "Version " + AppUtils.getAppVersion()
    }

    private fun addHomeSettingsData() {
        homeSettingsData.add(resources.getString(R.string.settings_settings_item_1))
        homeSettingsData.add(resources.getString(R.string.settings_settings_item_2))
        homeSettingsData.add(resources.getString(R.string.settings_settings_item_3))
    }

    private fun settingsItemClicked(item: String) {

        when (item) {
            resources.getString(R.string.settings_settings_item_1) -> {
                val arguments = Bundle()
                arguments.putString(Constants.PREFERENCES_FRAGMENT_ARG1, Constants.PREFERENCES_FRAGMENT_SETTINGS_SETTINGS_FRAGMENT)
                preferencesSettingsFragment.arguments = arguments
                parentFragmentManager.beginTransaction().setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN).replace(R.id.main_container, preferencesSettingsFragment).commit()
            }
            resources.getString(R.string.settings_settings_item_2) ->
                parentFragmentManager.beginTransaction().setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN).replace(R.id.main_container, notificationsSettingsFragment).commit()
            resources.getString(R.string.settings_settings_item_3) ->
                parentFragmentManager.beginTransaction().setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN).replace(R.id.main_container, accountSettingsFragment).commit()
        }
    }
}
