package com.lovebeats.ui.home.chat.message

import com.lovebeats.ui.home.chat.dialog.ChatUser
import com.stfalcon.chatkit.commons.models.IMessage
import com.stfalcon.chatkit.commons.models.MessageContentType
import java.util.Date

class Message @JvmOverloads constructor(private val id: String,
                                        private val user: ChatUser,
                                        private var text: String?,
                                        private var createdAt: Date?,
                                        var messageStatus: String = "") : IMessage, MessageContentType {

    var song: Track? = null

    override fun getId(): String {
        return id
    }

    override fun getText(): String? {

        return text
    }

    override fun getCreatedAt(): Date? {

        return createdAt
    }

    override fun getUser(): ChatUser {
        return user
    }

    fun setText(text: String) {
        this.text = text
    }

    fun setCreatedAt(createdAt: Date) {
        this.createdAt = createdAt
    }

    fun setImage(image: Image?) {}
    class Image(url: String?)


    class Track(val url: String)
}