package com.lovebeats.ui.home.settings

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.lovebeats.R
import com.lovebeats.databinding.ActivityUpdatePhoneNumberBinding
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils.Companion.validate
class UpdatePhoneNumberActivity : AppCompatActivity() {

    private lateinit var binding: ActivityUpdatePhoneNumberBinding

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, UpdatePhoneNumberActivity::class.java)
        }
    }

    private lateinit var weLlTextYouAcodTextView: TextView
    private lateinit var textViewTextView: TextView
    private lateinit var buttonLargeActiveButton: Button
    private lateinit var phoneNumberEditText: EditText


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityUpdatePhoneNumberBinding.inflate(layoutInflater)
        setContentView(binding.root)
        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.isEnabled = false

        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        phoneNumberEditText = this.findViewById(R.id.phone_number_edit_text)

        phoneNumberEditText.validate("Please enter a valid 10 digit phone number.") { s ->
            isValidPhone(s)
        }

        binding.preferencesHeaderLeftImageView.setOnClickListener {
            onBackClicked()
        }
    }

    fun onButtonLargeActivePressed() {

        val phoneNumber = phoneNumberEditText.text

        val removeSpecialChars = Regex("[^\\d]")
        val unMaskedPhone = removeSpecialChars.replace(phoneNumber, "")

        if (unMaskedPhone.length == 10) {
            buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        }

        val accountPreferences = AccountPreferences.getInstance(applicationContext)
        accountPreferences.setValue(Constants.phoneNumber, unMaskedPhone)
        startCodeVerificationActivity()
    }

    private fun startCodeVerificationActivity() {

        this.startActivity(UpdatePhoneNumberOtpActivity.newIntent(this))
    }

    private fun isValidPhone(input: String): Boolean {
        val removeSpecialChars = Regex("[^\\d]")
        val unMaskedPhone = removeSpecialChars.replace(input, "")

        return if (unMaskedPhone.length == 10) {
            buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
            buttonLargeActiveButton.isEnabled = true
            true
        } else {
            buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
            buttonLargeActiveButton.isEnabled = false
            false
        }
    }

    private fun onBackClicked() {
        val intent = Intent(this, SettingsActivity::class.java)
        intent.putExtra("AccountSettingsUpdateEmailOrPhoneActivity", true)
        startActivity(intent)
    }
}
