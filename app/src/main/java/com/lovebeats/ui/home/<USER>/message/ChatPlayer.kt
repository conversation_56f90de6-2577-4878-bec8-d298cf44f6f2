package com.lovebeats.ui.home.chat.message

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageButton
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import com.lovebeats.R
import timber.log.Timber

class ChatPlayer(context: Context, attrs: AttributeSet) : ConstraintLayout(context, attrs) {

    private val playbackStateListener: Player.Listener = playbackStateListener()
    private var player: ExoPlayer? = null

    private var playWhenReady = true
    private var currentItem = 0
    private var playbackPosition = 0L
    private var playerView: androidx.media3.ui.PlayerView
    private var playButton: ImageButton

    init {
        val view: View = LayoutInflater.from(context).inflate(R.layout.view_chat_player, this, true)
        playerView = view.findViewById(R.id.chat_player_view)
        playButton = playerView.findViewById(R.id.play_button)
    }

    fun show(context: Context, track: Message.Track?) {
        if (player == null) {
            track?.url?.let {
                initializePlayer(context, it)
            }
            pause()
        }
    }

    private fun initializePlayer(context: Context, track: String) {
        player = ExoPlayer.Builder(context)
            .build()
            .also { exoPlayer ->
                playerView.player = exoPlayer
                val mediaItem = MediaItem.fromUri(track)
                exoPlayer.setMediaItem(mediaItem)
                exoPlayer.repeatMode = Player.REPEAT_MODE_OFF
                exoPlayer.playWhenReady = false
                exoPlayer.seekTo(currentItem, playbackPosition)
                exoPlayer.prepare()
            }
    }

    fun play() {
        player?.play()
    }

    fun pause() {
        player?.pause()
    }

    fun isPlaying(): Boolean {
        return player?.isPlaying?: false
    }

    fun releasePlayer() {
        player?.let { exoPlayer ->
            playbackPosition = exoPlayer.currentPosition
            currentItem = exoPlayer.currentMediaItemIndex
            playWhenReady = exoPlayer.playWhenReady
            exoPlayer.removeListener(playbackStateListener)
            exoPlayer.release()
        }
        player = null
    }
}

private fun playbackStateListener() = object : Player.Listener {
    override fun onPlaybackStateChanged(playbackState: Int) {
        val stateString: String = when (playbackState) {
            ExoPlayer.STATE_IDLE -> "ExoPlayer.STATE_IDLE      -"
            ExoPlayer.STATE_BUFFERING -> "ExoPlayer.STATE_BUFFERING -"
            ExoPlayer.STATE_READY -> "ExoPlayer.STATE_READY     -"
            ExoPlayer.STATE_ENDED -> "ExoPlayer.STATE_ENDED     -"
            else -> "UNKNOWN_STATE             -"
        }
        Timber.d("PlayerView ChatPlayer", "changed state to $stateString")
    }
}