package com.lovebeats.ui.adapter

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.R
import com.lovebeats.databinding.SettingsNotificationsRowBinding
import com.lovebeats.databinding.SettingsRowBinding
import com.lovebeats.models.UserNotificationPreferences
import com.lovebeats.utils.ManagePermissions
class SettingsHomeSupernovaActivityViewRecyclerViewAdapter() : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private lateinit var homeSettingsItems: List<String>
    private lateinit var notificationSettingsItems: ArrayList<UserNotificationPreferences>
    private var viewType: Int = 1
    private lateinit var clickListener: Any
    private var mContext: Context? = null

    constructor(context: Context?, userNotificationItemsList: ArrayList<UserNotificationPreferences>, notificationClickListener: (UserNotificationPreferences) -> Unit, notificationViewType: Int) : this() {
        this.notificationSettingsItems = userNotificationItemsList
        this.clickListener = notificationClickListener
        this.viewType = notificationViewType
        this.mContext = context
    }

    constructor(context: Context?, homeSettingsItems: List<String>, clickListener: (String) -> Unit, viewType: Int) : this() {
        this.homeSettingsItems = homeSettingsItems
        this.clickListener = clickListener
        this.viewType = viewType

        mContext = context
    }

    companion object {
        const val SETTINGS_ROW_NORMAL_LAYOUT = 1
        const val SETTINGS_ROW_NOTIFICATIONS_LAYOUT = 2
        const val SETTINGS_ROW_ACCOUNT_LAYOUT = 3
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {

        when (viewType) {
            SETTINGS_ROW_NORMAL_LAYOUT -> {
                val itemBinding = SettingsRowBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                return ScreenDataViewHolder(itemBinding, mContext)
            }
            SETTINGS_ROW_NOTIFICATIONS_LAYOUT -> {
                val itemBinding = SettingsNotificationsRowBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                return ScreenNotificationsDataViewHolder(itemBinding)
            }
            SETTINGS_ROW_ACCOUNT_LAYOUT -> {
                val itemBinding = SettingsRowBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                return ScreenAccountDataViewHolder(itemBinding)
            }
        }

        throw RuntimeException("Unsupported view type")
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {

        when (viewType) {
            SETTINGS_ROW_NORMAL_LAYOUT -> {
                (holder as ScreenDataViewHolder).bind(homeSettingsItems[position], clickListener as (String) -> Unit)
            }
            SETTINGS_ROW_NOTIFICATIONS_LAYOUT -> {
                (holder as ScreenNotificationsDataViewHolder).bind(mContext, notificationSettingsItems[position], clickListener as (UserNotificationPreferences) -> UserNotificationPreferences)
            }
            SETTINGS_ROW_ACCOUNT_LAYOUT -> {
                (holder as ScreenAccountDataViewHolder).bind(position, homeSettingsItems[position], clickListener as (String) -> Unit)
            }
        }
    }

    override fun getItemCount(): Int {

        if (viewType == 2) {
            return notificationSettingsItems.size
        }
        return homeSettingsItems.size
    }

    override fun getItemViewType(position: Int): Int {

        return viewType
    }

    class ScreenDataViewHolder(val binding: SettingsRowBinding, val context: Context?) : RecyclerView.ViewHolder(binding.root) {

        fun bind(settingsItem: String, clickListener: (String) -> Unit) {

            binding.yourFavoriteHobbyTextView.setTextColor(Color.parseColor("#646C70"))

            val typeface = context?.let {
                ResourcesCompat.getFont(it, R.font.inter)
            }
            binding.yourFavoriteHobbyTextView.typeface = typeface
            binding.yourFavoriteHobbyTextView.text = settingsItem
            itemView.setOnClickListener { clickListener(settingsItem) }
        }
    }

    class ScreenNotificationsDataViewHolder(val binding: SettingsNotificationsRowBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(context: Context?, userNotificationPreference: UserNotificationPreferences, clickListener: (UserNotificationPreferences) -> UserNotificationPreferences) {

            binding.settingItemTextView.text = userNotificationPreference.notificationItemTitle
            binding.notificationsItemSwitch.isChecked = userNotificationPreference.notificationItemCheckedStatus ?: true

            binding.notificationsItemSwitch.setOnClickListener {
               if (context != null && !ManagePermissions.areNotificationsEnabled(context)) {
                   binding.notificationsItemSwitch.isChecked = false
               }else {
                   userNotificationPreference.notificationItemCheckedStatus = binding.notificationsItemSwitch.isChecked
               }
                clickListener(userNotificationPreference)
            }
        }
    }

    class ScreenAccountDataViewHolder(val binding: SettingsRowBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(rowPosition: Int, settingsItem: String, clickListener: (String) -> Unit) {

            binding.yourFavoriteHobbyTextView.text = settingsItem
            itemView.setOnClickListener { clickListener(settingsItem) }

            if ((rowPosition == 2) or (rowPosition == 3)) {

                binding.streRightImageView.visibility = View.INVISIBLE
            }
        }
    }
}
