package com.lovebeats.ui.onboarding.genderFlow

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.lovebeats.R
import com.lovebeats.analytics.AnalyticsTrackingService
import com.lovebeats.analytics.GAY
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.analytics.STRAIGHT
import com.lovebeats.analytics.USER_ORIENTATION_PROPERTY
import com.lovebeats.databinding.ActivityInterestedInBinding
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.InterestedInGender
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.onboarding.YourHeightActivity
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter

class InterestedInActivity : AppCompatActivity() {

    private lateinit var binding: ActivityInterestedInBinding

    companion object {

        const val TAG = ScreenRouter.INTERESTED_IN_ACTIVITY

        fun newIntent(context: Context): Intent {
            return Intent(context, InterestedInActivity::class.java)
        }
    }

    private var interestedIn: String = ""
    private var interestedInButtonSelected = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInterestedInBinding.inflate(layoutInflater)
        setContentView(binding.root)

        ScreenRouter.setLastSeenScreen(TAG, this)

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        binding.notificationsHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        binding.buttonPreferenceMen.setOnClickListener {
            this.onManButtonPressed()
        }

        binding.buttonPreferenceWomen.setOnClickListener { view ->
            this.onWomanButtonPressed()
        }

        binding.buttonPreferenceEveryone.setOnClickListener { view ->
            this.onEveryoneButtonPressed()
        }

        binding.buttonNext.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }
    }

    private fun onManButtonPressed() {
        binding.buttonPreferenceMen.setBackgroundResource(R.drawable.bottom_button_active_state_ripple_light)
        binding.buttonPreferenceWomen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        binding.buttonPreferenceEveryone.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        interestedIn = InterestedInGender.man.toString()
        interestedInButtonSelected = true
        enableOrDisableButton()
    }

    private fun onWomanButtonPressed() {
        binding.buttonPreferenceWomen.setBackgroundResource(R.drawable.bottom_button_active_state_ripple_light)
        binding.buttonPreferenceMen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        binding.buttonPreferenceEveryone.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        interestedIn = InterestedInGender.woman.toString()
        interestedInButtonSelected = true
        enableOrDisableButton()
    }

    private fun onEveryoneButtonPressed() {
        binding.buttonPreferenceEveryone.setBackgroundResource(R.drawable.bottom_button_active_state_ripple_light)
        binding.buttonPreferenceMen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        binding.buttonPreferenceWomen.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        interestedIn = InterestedInGender.everyone.toString()
        interestedInButtonSelected = true
        enableOrDisableButton()
    }

    private fun enableOrDisableButton() {
        if (interestedInButtonSelected) {
            binding.buttonNext.isEnabled = true
            binding.buttonNext.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        }
    }

    fun onButtonLargeActivePressed() {
        if (!AppUtils.isNetworkConnected(application)) {
            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            saveInterestedInPrefs()
            startYourHeightActivity()
        }
    }

    private fun saveInterestedInPrefs() {
        AccountPreferences.getInstance(this).setValue(Constants.interestedIn, interestedIn)
        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.interestedIn, interestedIn)

        val gender: String = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")
        if (gender.isNotEmpty()) {
            if (gender == interestedIn) {
                AnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, GAY)
                MixPanelAnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, GAY)
            }else {
                AnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, STRAIGHT)
                MixPanelAnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, STRAIGHT)
            }
        }
    }

    private fun startYourHeightActivity() {
        this.startActivity(YourHeightActivity.newIntent(this))
    }
}