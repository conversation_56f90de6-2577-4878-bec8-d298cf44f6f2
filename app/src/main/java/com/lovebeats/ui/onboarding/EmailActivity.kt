package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.SpannableString
import android.view.MenuItem
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import com.lovebeats.R
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.onboarding.genderFlow.IdentifyYourselfActivity
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.ScreenRouter.Companion.setLastSeenScreen
import com.lovebeats.utils.Utils


class EmailActivity : AppCompatActivity() {

    companion object {

        const val TAG = ScreenRouter.EMAIL_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, EmailActivity::class.java)
        }
    }

    private lateinit var buttonLargeActiveButton: Button
    private lateinit var thisEnsuresThatYoTextView: TextView
    private lateinit var emailEditText: EditText
    private lateinit var skipTextView: TextView
    private lateinit var toolbar: Toolbar

    private var email: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.email_activity)
        this.init()

        setLastSeenScreen(TAG, this)

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        skipTextView = this.findViewById(R.id.skip_text_view)

        // Configure This ensures that yo component
        thisEnsuresThatYoTextView = this.findViewById(R.id.this_ensures_that_yo_text_view)
        val thisEnsuresThatYoTextViewText = SpannableString(this.getString(R.string.email_screen_desc))
        thisEnsuresThatYoTextView.text = thisEnsuresThatYoTextViewText

        emailEditText = this.findViewById(R.id.email_edit_text)

        emailEditText.requestFocus()

        setupToolbar()

//        emailEditText.afterTextChanged {
//            if (it.length >= 1) {
//                buttonLargeActiveButton.setBackgroundResource(R.drawable.phone_number_activity_button_large_active_button_selector)
//                buttonLargeActiveButton.isEnabled = true
//            } else {
//                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
//                buttonLargeActiveButton.isEnabled = false
//            }
//        }

    }

    fun setupToolbar() {

        toolbar = findViewById(R.id.toolbar)

        skipTextView = this.findViewById(R.id.skip_text_view)

        setSupportActionBar(toolbar)

        val actionBar = supportActionBar
        val drawable: Drawable? = ContextCompat.getDrawable(this, R.drawable.ic_arrow_left)

        actionBar?.setHomeAsUpIndicator(drawable)

        // Display the app icon in action bar/toolbar
        actionBar?.setDisplayShowTitleEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)

        skipTextView.setOnClickListener { view -> this.onSkipButtonPressed() }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            email = emailEditText.text.toString()

            val isValidEmail = Utils.isValidEmail(email)

            val accountPreferences = AccountPreferences.getInstance(applicationContext)
            accountPreferences.setValue(Constants.email, email)

            if (isValidEmail) {
                saveEmailToFirebase()
                startSexPreferenceActivity()
            } else {
                emailEditText.error = "Please enter a valid email address."
            }
        }
    }

    private fun onSkipButtonPressed() {
        startSexPreferenceActivity()
    }

    private fun saveEmailToFirebase() {
        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.email, email)
    }

    private fun startSexPreferenceActivity() {
        this.startActivity(IdentifyYourselfActivity.newIntent(this))
    }
}
