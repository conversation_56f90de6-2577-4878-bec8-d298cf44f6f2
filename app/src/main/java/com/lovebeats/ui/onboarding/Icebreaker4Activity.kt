package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.SpannableString
import android.view.MenuItem
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import com.lovebeats.R
import com.lovebeats.analytics.AnalyticsTrackingService
import com.lovebeats.analytics.ICE_BREAKERS_COMPLETE
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.onboarding.Icebreaker1Activity.Companion.ICE_BREAKER_1_ANSWER
import com.lovebeats.ui.onboarding.Icebreaker1Activity.Companion.ICE_BREAKER_1_QUESTION
import com.lovebeats.ui.onboarding.Icebreaker1Activity.Companion.ICE_BREAKER_2_QUESTION
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils
import com.lovebeats.utils.Utils.Companion.afterTextChanged


class Icebreaker4Activity : AppCompatActivity() {

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, Icebreaker4Activity::class.java)
        }
    }

    private lateinit var fieldTitleTextView: TextView
    private lateinit var textViewTextView: TextView
    private lateinit var buttonLargeActiveButton: Button
    private lateinit var toolbar: Toolbar
    private lateinit var skipTextView: TextView
    private lateinit var iceBreakerEditText: EditText

    private var icebreaker2Answer: String = ""
    private var icebreaker2Question: String? = null

    private var icebreaker1Question: String? = ""
    private var icebreaker1Answer: String? = ""

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.icebreaker4_activity)
        this.init()
    }

    private fun init() {

        icebreaker1Question = intent?.getStringExtra(ICE_BREAKER_1_QUESTION)
        icebreaker1Answer = intent?.getStringExtra(ICE_BREAKER_1_ANSWER)

        icebreaker2Question = intent.getStringExtra(ICE_BREAKER_2_QUESTION)

        if (icebreaker2Question == null) {

            this.startActivity(Icebreaker3Activity.newIntent(this))
            finish()
            return
        }

        val questionsMap = Utils.getIceBreakerQuestions()

        // Configure Field Title component
        fieldTitleTextView = this.findViewById(R.id.field_title_text_view)
        val fieldTitleTextViewText = SpannableString(icebreaker2Question)
        fieldTitleTextView.text = fieldTitleTextViewText

        // Configure 0/50 component
        textViewTextView = this.findViewById(R.id.height_text_view)
        val textViewTextViewText = SpannableString(this.getString(R.string.icebreaker4_activity_text_view_text_view_text))
        textViewTextView.text = textViewTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        // Configure Navigation Bar #1 component
        toolbar = this.findViewById(R.id.toolbar)

        iceBreakerEditText = this.findViewById(R.id.icebreaker2_edit_text)
        iceBreakerEditText.requestFocus()

        iceBreakerEditText.hint = questionsMap[icebreaker2Question.toString()]

        iceBreakerEditText.afterTextChanged {

            if (it.isNotEmpty()) {

                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                buttonLargeActiveButton.isEnabled = true
            } else {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
                buttonLargeActiveButton.isEnabled = false
            }

            val textCounter = 140.minus(it.length)
            textViewTextView.text = "$textCounter/140"
        }


        setupToolbar()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    fun setupToolbar() {

        toolbar = findViewById(R.id.toolbar)

        skipTextView = this.findViewById(R.id.skip_text_view)
        skipTextView.text = getString(R.string.cancel)

        setSupportActionBar(toolbar)

        val actionBar = supportActionBar
        val drawable: Drawable? = ContextCompat.getDrawable(this, R.drawable.ic_arrow_left)

        actionBar?.setHomeAsUpIndicator(drawable)

        // Display the app icon in action bar/toolbar
        actionBar?.setDisplayShowTitleEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)

        skipTextView.setOnClickListener {

            Icebreaker1Activity.abandonIceBreakers(this)
        }
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            icebreaker2Answer = iceBreakerEditText.text.toString()

            if (Utils.isValidString(icebreaker2Answer)) {

                startHeightScan()
            }
        }
    }

    private fun startHeightScan() {

        saveIceBreakersToFirebase()

        startActivity(ProfessionActivity.newIntent(this))
    }

    private fun saveIceBreakersToFirebase() {

        if (icebreaker1Question != null && icebreaker1Answer != null && icebreaker2Question != null) {

            val firebaseDatabaseReference = FirebaseDatabaseUtil(this)

            firebaseDatabaseReference.clearAndSaveChildUserInfoToFirebase(Constants.iceBreaker1, icebreaker1Question.toString(), icebreaker1Answer.toString())
            firebaseDatabaseReference.clearAndSaveChildUserInfoToFirebase(Constants.iceBreaker2, icebreaker2Question.toString(), icebreaker2Answer)

            val accountPreferences = AccountPreferences.getInstance(this)
            accountPreferences.setValue(Constants.iceBreaker1, icebreaker1Question)
            accountPreferences.setValue(Constants.iceBreaker2, icebreaker2Question)

            val isIceBreakersComplete = AccountPreferences.getInstance(this).getBooleanValue(
                Constants.iceBreakersComplete, false)
            if (!isIceBreakersComplete) {
                accountPreferences.setValue(Constants.iceBreakersComplete, true)
                AnalyticsTrackingService.logEvent(this, ICE_BREAKERS_COMPLETE)
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }
}
