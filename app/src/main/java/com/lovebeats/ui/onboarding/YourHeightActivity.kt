package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.lovebeats.R
import com.lovebeats.firebase.cloudFunctions.NearByUsers
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.InterestedInGender
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.AlertDialogView
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.FontSpan
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.ScreenRouter.Companion.setLastSeenScreen
import com.lovebeats.utils.Utils
import io.apptik.widget.MultiSlider


class YourHeightActivity : AppCompatActivity() {

    companion object {

        const val TAG = ScreenRouter.YOUR_HEIGHT_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, YourHeightActivity::class.java)
        }
    }

    private lateinit var heightTextView: TextView
    private lateinit var sliderSingleCopySeekBar: MultiSlider
    private lateinit var beAsAccurateAsPoTextView: TextView
    private lateinit var buttonLargeActiveButton: Button

    private var height: Double = Constants.defaultHeightInInch
    private lateinit var gender: String
    private lateinit var interestedIn: String

    private lateinit var toolbar: Toolbar
    private lateinit var skipTextView: TextView

    override fun onStart() {
        super.onStart()

        makeNearbyUsersAPICall()
    }

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.your_height_activity)
        this.init()

        setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        setupToolbar()

        // Configure slider/single copy component
        sliderSingleCopySeekBar = this.findViewById(R.id.slider_single_copy_seek_bar)

        gender = AccountPreferences.getInstance(this).getStringValue(Constants.gender, "")
        interestedIn = AccountPreferences.getInstance(this).getStringValue(Constants.interestedIn, "")

        heightTextView = this.findViewById(R.id.height_text_view)

        height = Constants.defaultHeightInInch
        setHeightTextViewText(Constants.defaultHeightInFeet)
        sliderSingleCopySeekBar.getThumb(0).value = 52

        sliderSingleCopySeekBar.setOnThumbValueChangeListener(object : MultiSlider.SimpleChangeListener() {
            override fun onValueChanged(multiSlider: MultiSlider?, thumb: MultiSlider.Thumb?, thumbIndex: Int, value: Int) {
                height = value.times(Constants.heightInInchMultiplier).plus(Constants.startingHeightInInch)
                val heightString = Utils.heightInFeetFromInches(height)
                setHeightTextViewText(heightString)
            }
        })

        // Configure Be as accurate as po component
        beAsAccurateAsPoTextView = this.findViewById(R.id.be_as_accurate_as_po_text_view)
        val beAsAccurateAsPoTextViewText = SpannableString(this.getString(R.string.height_confirmation_desc))
        beAsAccurateAsPoTextView.text = beAsAccurateAsPoTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }
    }

    fun setHeightTextViewText(heightString: String) {

        val formattedHeight: String = if (heightString.length == 7) {
            heightString.substring(0, 1) + " " + heightString.substring(1, 3) + " " + heightString.substring(3, 5) + " " + heightString.substring(heightString.length - 2, heightString.length)
        } else {
            heightString.substring(0, 1) + " " + heightString.substring(1, 3) + " " + heightString.substring(3, 4) + " " + heightString.substring(heightString.length - 2, heightString.length)
        }

        val spannedHeightString = SpannableString(formattedHeight)
        spannedHeightString.setSpan(AbsoluteSizeSpan(64), 1, 4, Spannable.SPAN_INCLUSIVE_INCLUSIVE)
        spannedHeightString.setSpan(AbsoluteSizeSpan(64), formattedHeight.length - 2, formattedHeight.length, Spannable.SPAN_INCLUSIVE_INCLUSIVE)

        heightTextView.setText(spannedHeightString, TextView.BufferType.SPANNABLE)
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {

            showConfirmationDialog()
        }
    }

    private fun showConfirmationDialog() {

        AlertDialogView.showAlertDialog(context = this,
                title = getString(R.string.height_confirmation_title, Utils.heightInFeetFromInchesWithQuotes(height)),
                message = getString(R.string.height_confirmation_desc),
                buttonPositiveText = getString(R.string.height_confirmation_positive_button),
                buttonNegativeText = getString(R.string.height_confirmation_negative_button)) { dialog, which ->

            if (which == DialogInterface.BUTTON_POSITIVE) {

                AccountPreferences.getInstance(this).setValue(Constants.height, height)
                saveHeightToFirebase()

                startImagesActivity()
            } else {

                dialog.cancel()
            }
        }
    }

    private fun startImagesActivity() {
        if (!AppUtils.isNetworkConnected(application)) {
            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            this.startActivity(PhotoUploadActivity.newIntent(this))
        }
    }

    fun setupToolbar() {

        toolbar = findViewById(R.id.toolbar)

        skipTextView = this.findViewById(R.id.skip_text_view)
        skipTextView.visibility = View.GONE

        setSupportActionBar(toolbar)

        val actionBar = supportActionBar
        val drawable: Drawable? = ContextCompat.getDrawable(this, R.drawable.ic_arrow_left)

        actionBar?.setHomeAsUpIndicator(drawable)
        actionBar?.setDisplayShowTitleEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }

    private fun saveHeightToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)

        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.height, height)

        setDefaultHeights()
    }

    private fun setDefaultHeights() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)

        when (interestedIn) {
            InterestedInGender.man.toString() -> {
                //setting default match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)

                AccountPreferences.getInstance(this).setValue(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                AccountPreferences.getInstance(this).setValue(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)
            }
            InterestedInGender.woman.toString() -> {
                //setting default match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)

                AccountPreferences.getInstance(this).setValue(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                AccountPreferences.getInstance(this).setValue(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)
            }
            else -> {
                //setting default match heights
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                firebaseDatabaseReference.saveUserInfoToFirebase(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)

                AccountPreferences.getInstance(this).setValue(Constants.minMatchHeight, Constants.defaultMinHeightInInch)
                AccountPreferences.getInstance(this).setValue(Constants.maxMatchHeight, Constants.defaultMaxHeightInInch)
            }
        }
    }

    private fun makeNearbyUsersAPICall() {
        Thread {
            val mainUserId = AccountPreferences.getInstance(this).getStringValue(Constants.firebaseUserId, "")
            val distanceFromPrefs = AccountPreferences.getInstance(this).getIntValue(Constants.distance, Constants.defaultDistance)
            val lat = AccountPreferences.getInstance(this).getDoubleValue(Constants.latitude, 0.0)
            val long = AccountPreferences.getInstance(this).getDoubleValue(Constants.longitude, 0.0)
            NearByUsers.getNearByUsers(this, mainUserId, lat, long, distanceFromPrefs) { _, _ ->}
        }.start()
    }
}
