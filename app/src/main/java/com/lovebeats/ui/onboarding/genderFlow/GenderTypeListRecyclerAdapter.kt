package com.lovebeats.ui.onboarding.genderFlow

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.databinding.GenderTypeViewHolderBinding
import com.lovebeats.models.OtherGenderType

class GenderTypeListRecyclerAdapter(private val genders: A<PERSON>yList<OtherGenderType>, val context: Context) : RecyclerView.Adapter<GenderTypeListRecyclerAdapter.GenderViewHolder>() {

    private lateinit var listener: OnItemClickListener

    override fun onBindViewHolder(holder: <PERSON>ViewHolder, position: Int) {
        holder.genderType.text = genders[position].displayText
        holder.itemView.setOnClickListener {
            listener.onClick(it, genders[position])
        }
    }
    override fun onCreateViewHolder(parent: <PERSON>G<PERSON>, viewType: Int): GenderViewHolder {
        val itemBinding = GenderTypeViewHolderBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        setOnItemClickListener(listener)
        return GenderViewHolder(itemBinding)
    }

    override fun getItemCount(): Int {
        return genders.size
    }

    interface OnItemClickListener {
        fun onClick(view: View, data: OtherGenderType)
    }

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.listener = listener
    }

    class GenderViewHolder(itemView: GenderTypeViewHolderBinding) : RecyclerView.ViewHolder(itemView.root) {
        val genderType: TextView = itemView.genderType
    }
}