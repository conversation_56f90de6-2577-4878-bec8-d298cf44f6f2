package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.SpannableString
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import com.lovebeats.R
import com.lovebeats.analytics.AnalyticsTrackingService
import com.lovebeats.analytics.GAY
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.analytics.STRAIGHT
import com.lovebeats.analytics.USER_GENDER_PROPERTY
import com.lovebeats.analytics.USER_ORIENTATION_PROPERTY
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.GenderType
import com.lovebeats.models.InterestedInGender
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.ScreenRouter.Companion.setLastSeenScreen

// NOT USED
class SexPreferenceActivity : AppCompatActivity() {

    companion object {

        const val TAG = ScreenRouter.SEX_PREFERENCE_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, SexPreferenceActivity::class.java)
        }
    }

    private lateinit var buttonPreferenceMan1: Button
    private lateinit var buttonPreferenceLookingForMan: Button
    private lateinit var buttonPreferenceWoman1: Button
    private lateinit var buttonPreferenceLookingForWoman: Button
    private lateinit var youAreAtextView: TextView
    private lateinit var buttonLargeActiveButton: Button

    private var gender: String = ""
    private var interestedIn: String = ""

    private var genderButtonSelected = false
    private var interestedInButtonSelected = false

    private lateinit var toolbar: Toolbar
    private lateinit var skipTextView: TextView

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.sex_preference_activity)
        this.init()

        setLastSeenScreen(TAG, this)
    }

    private fun init() {

        setupToolbar()

        // Configure radio_button_unselected component
        buttonPreferenceMan1 = this.findViewById(R.id.button_preference_man_1)

        buttonPreferenceMan1.setOnClickListener { view ->
            this.onRadioMan1ButtonPressed()
        }

        // Configure You are a… component
        youAreAtextView = this.findViewById(R.id.you_are_atext_view)
        val youAreAtextViewText = SpannableString(this.getString(R.string.sex_preference_activity_title))
        youAreAtextView.text = youAreAtextViewText


        buttonPreferenceWoman1 = this.findViewById(R.id.button_preference_woman_1)
        buttonPreferenceWoman1.setOnClickListener { view ->
            this.onRadioWoMan1ButtonPressed()
        }

        buttonPreferenceLookingForWoman = this.findViewById(R.id.button_looking_for_woman)
        buttonPreferenceLookingForWoman.setOnClickListener { view ->
            this.onRadioLookingForWoManButtonPressed()
        }

        buttonPreferenceLookingForMan = this.findViewById(R.id.button_looking_for_man)
        buttonPreferenceLookingForMan.setOnClickListener { view ->
            this.onRadioLookingForManButtonPressed()
        }

        buttonPreferenceMan1.setOnClickListener { view ->
            this.onRadioMan1ButtonPressed()
        }


        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun onRadioMan1ButtonPressed() {
        buttonPreferenceMan1.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        buttonPreferenceWoman1.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        gender = GenderType.man.toString()
        genderButtonSelected = true
        enableOrDisableButton()
    }

    private fun onRadioWoMan1ButtonPressed() {
        buttonPreferenceWoman1.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        buttonPreferenceMan1.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        gender = GenderType.woman.toString()
        genderButtonSelected = true
        enableOrDisableButton()
    }

    private fun onRadioLookingForManButtonPressed() {
        buttonPreferenceLookingForMan.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        buttonPreferenceLookingForWoman.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        interestedIn = InterestedInGender.man.toString()
        interestedInButtonSelected = true
        enableOrDisableButton()
    }

    private fun onRadioLookingForWoManButtonPressed() {
        buttonPreferenceLookingForWoman.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        buttonPreferenceLookingForMan.setBackgroundResource(R.drawable.sex_preference_activity_radio_button_unselected_button_selector)
        interestedIn = InterestedInGender.woman.toString()
        interestedInButtonSelected = true
        enableOrDisableButton()
    }

    private fun enableOrDisableButton() {

        if (interestedInButtonSelected && genderButtonSelected) {

            buttonLargeActiveButton.isEnabled = true
            buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
        }
    }

    fun setupToolbar() {

        toolbar = findViewById(R.id.toolbar)

        skipTextView = this.findViewById(R.id.skip_text_view)
        skipTextView.visibility = View.GONE

        setSupportActionBar(toolbar)

        val actionBar = supportActionBar
        val drawable: Drawable? = ContextCompat.getDrawable(this, R.drawable.ic_arrow_left)

        actionBar?.setHomeAsUpIndicator(drawable)
        actionBar?.setDisplayShowTitleEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {

            AccountPreferences.getInstance(this).setValue(Constants.interestedIn, interestedIn)
            AccountPreferences.getInstance(this).setValue(Constants.gender, gender)

            saveGenderPrefsToFirebase()
            startYourHeightActivity()
        }
    }

    private fun startYourHeightActivity() {

        this.startActivity(YourHeightActivity.newIntent(this))
    }

    private fun saveGenderPrefsToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.gender, gender)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.interestedIn, interestedIn)

        AnalyticsTrackingService.setUserProperty(this, USER_GENDER_PROPERTY, gender)
        MixPanelAnalyticsTrackingService.setUserProperty(this, USER_GENDER_PROPERTY, gender)

        if (gender == interestedIn) {
            AnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, GAY)
            MixPanelAnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, GAY)
        }else {
            AnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, STRAIGHT)
            MixPanelAnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, STRAIGHT)
        }
    }
}
