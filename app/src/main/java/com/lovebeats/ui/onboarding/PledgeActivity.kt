package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.View
import android.widget.Button
import android.widget.ImageButton
import android.widget.TextView
import com.lovebeats.R
import com.lovebeats.extensions.launchModeWithNoBackStack
import com.lovebeats.extensions.launchModeWithSingleTop
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.subscriptions.views.SubscriptionActivity
import com.lovebeats.ui.home.browseProfiles.BrowseProfilesActivity
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter

class PledgeActivity : AppCompatActivity() {

    companion object {

        const val TAG = ScreenRouter.PLEDGE_ACTIVITY
        const val FROM_SETTINGS = "FROM_SETTINGS"
        fun newIntent(context: Context): Intent {
            return Intent(context, PledgeActivity::class.java)
        }
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_pledge)

        val agreeButton = findViewById<Button>(R.id.pledge_agree_button)

        agreeButton.setOnClickListener {
            startBrowseProfilesScreen()
        }

        val pledgeTextView = findViewById<TextView>(R.id.pledge_desc)
        pledgeTextView.movementMethod = LinkMovementMethod.getInstance()

        val fromSettings = intent.getBooleanExtra(FROM_SETTINGS, false)
        if (fromSettings) {
            agreeButton.setOnClickListener {
                onBackPressed()
            }
        }

        val back = findViewById<ImageButton>(R.id.account_header_left_image_view)
        back.setOnClickListener {
            onBackPressed()
        }

        ScreenRouter.setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, PledgeActivity::class.java.simpleName)
    }

    private fun startSubscriptionScreen() {
        val subIntent = SubscriptionActivity.newIntent(this)
        val influencerId = AccountPreferences.getInstance(applicationContext).getStringValue(
            Constants.influencerId, "")
        if (influencerId.isNotEmpty()) {
            subIntent.putExtra(SubscriptionActivity.INFLUENCER_FREE_TRAIL, true)
        }
        startActivity(subIntent.launchModeWithSingleTop())
    }

    private fun startBrowseProfilesScreen() {
        val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
        this.startActivity(intent)
    }
}