package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.SpannableString
import android.view.MenuItem
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import com.lovebeats.R
import com.lovebeats.extensions.launchModeWithNoBackStack
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.home.browseProfiles.BrowseProfilesActivity
import com.lovebeats.ui.onboarding.Icebreaker1Activity.Companion.ICE_BREAKER_1_ANSWER
import com.lovebeats.ui.onboarding.Icebreaker1Activity.Companion.ICE_BREAKER_1_QUESTION
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils
import com.lovebeats.utils.Utils.Companion.afterTextChanged

class Icebreaker2Activity : AppCompatActivity() {

    private lateinit var fieldTitleTextView: TextView
    private lateinit var buttonLargeActiveButton: Button
    private lateinit var toolbar: Toolbar
    private lateinit var skipTextView: TextView
    private lateinit var iceBreakerEditText: EditText
    private lateinit var counterTextView: TextView

    private var icebreaker: String = ""
    private var question: String? = null

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, Icebreaker2Activity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.icebreaker2_activity)
        this.init()
    }

    private fun init() {

        question = intent.getStringExtra(ICE_BREAKER_1_QUESTION)

        if (question == null) {
            this.startActivity(Icebreaker1Activity.newIntent(this))
            finish()
            return
        }

        val questionsMap = Utils.getIceBreakerQuestions()

        // Configure Field Title component
        fieldTitleTextView = this.findViewById(R.id.field_title_text_view)
        val fieldTitleTextViewText = SpannableString(question)
        fieldTitleTextView.text = fieldTitleTextViewText.toString()

        counterTextView = this.findViewById(R.id.counter_text_view)

        // Configure 0/50 component
        val textViewTextViewText = SpannableString(this.getString(R.string.icebreaker2_activity_text_view_text_view_text))
        counterTextView.text = textViewTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }


        // Configure Navigation Bar #1 component
        toolbar = this.findViewById(R.id.toolbar)

        iceBreakerEditText = this.findViewById(R.id.icebreaker1_edit_text)
        iceBreakerEditText.requestFocus()

        iceBreakerEditText.hint = questionsMap[question.toString()]

        iceBreakerEditText.afterTextChanged {

            if (it.isNotEmpty()) {

                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                buttonLargeActiveButton.isEnabled = true
            } else {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
                buttonLargeActiveButton.isEnabled = false
            }

            val textCounter = 140.minus(it.length)
            counterTextView.text = "$textCounter/140"
        }

        setupToolbar()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            icebreaker = iceBreakerEditText.text.toString()

            if (Utils.isValidString(icebreaker)) {
                startIcebreaker3Activity()
            }
        }
    }

    private fun startIcebreaker3Activity() {

        val intent = Intent(this, Icebreaker3Activity::class.java)
        intent.putExtra(ICE_BREAKER_1_QUESTION, question)
        intent.putExtra(ICE_BREAKER_1_ANSWER, icebreaker)
        startActivity(intent)
    }

    fun setupToolbar() {

        toolbar = findViewById(R.id.toolbar)

        skipTextView = this.findViewById(R.id.skip_text_view)
        skipTextView.text = getString(R.string.cancel)

        setSupportActionBar(toolbar)

        val actionBar = supportActionBar
        val drawable: Drawable? = ContextCompat.getDrawable(this, R.drawable.ic_arrow_left)

        actionBar?.setHomeAsUpIndicator(drawable)
        actionBar?.setDisplayShowTitleEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)

        skipTextView.setOnClickListener {

            Icebreaker1Activity.abandonIceBreakers(this)
        }
    }

    private fun onSkipButtonPressed() {

        val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
        this.startActivity(intent)
    }

    private fun saveIceBreakerToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)

        if (question != null) {

            firebaseDatabaseReference.clearAndSaveChildUserInfoToFirebase(Constants.iceBreaker1, question.toString(), icebreaker)

            val accountPreferences = AccountPreferences.getInstance(this)
            accountPreferences.setValue(Constants.iceBreaker1, question)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {

            android.R.id.home -> {

                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }
}
