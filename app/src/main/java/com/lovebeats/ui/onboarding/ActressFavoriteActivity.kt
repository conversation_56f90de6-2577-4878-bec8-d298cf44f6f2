package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.LinearLayoutManager
import com.lovebeats.R
import com.lovebeats.analytics.AnalyticsTrackingService
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.analytics.ONBOARDING_COMPLETE
import com.lovebeats.databinding.ActivityOnboardingFavoritesBinding
import com.lovebeats.firebase.cloudFunctions.Favorites
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.Favorite
import com.lovebeats.models.FavoritesType
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.favorites.FavoritesListRecyclerAdapter
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import java.util.Locale


class ActressFavoriteActivity : AppCompatActivity() {

    private lateinit var binding: ActivityOnboardingFavoritesBinding

    private val favoritesData: ArrayList<Favorite> = ArrayList()
    private lateinit var favoritesListRecyclerAdapter: FavoritesListRecyclerAdapter

    companion object {

        const val TAG = ScreenRouter.ACTRESS_FAVORITE_ACTIVITY
        fun newIntent(context: Context): Intent {
            return Intent(context, ActressFavoriteActivity::class.java)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOnboardingFavoritesBinding.inflate(layoutInflater)
        setContentView(binding.root)

        init()

        ScreenRouter.setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, ActressFavoriteActivity::class.java.simpleName)
    }

    private fun init() {

        startLoading()
        getData()

        binding.titleTextView.setText(R.string.onboarding_favorites_item_2)
        binding.headerTitle.setText(R.string.favorite_onboarding_actress)

        binding.accountHeaderLeftImageView.setOnClickListener {
            onBackPressed()
        }

        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener,
            android.widget.SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(p0: String?): Boolean {
                return false
            }

            override fun onQueryTextChange(msg: String): Boolean {
                filter(msg)
                return false
            }
        })
    }

    private fun startLoading() {
        binding.progressBar.visibility = View.VISIBLE
    }

    private fun hideLoading() {
        binding.progressBar.visibility = View.GONE
    }

    private fun getData() {

        Favorites.getFavoritesData(this, FavoritesType.actress.name) {response ->

            response.let { favoritesData.addAll(it) }

            if (favoritesData.isEmpty()) {
                binding.searchView.visibility = View.GONE
            } else {
                binding.searchView.visibility = View.VISIBLE
            }

            hideLoading()

            binding.favoritesViewRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
            favoritesListRecyclerAdapter = FavoritesListRecyclerAdapter(favoritesData, this)
            binding.favoritesViewRecyclerView.adapter = favoritesListRecyclerAdapter
            favoritesListRecyclerAdapter.setOnItemClickListener(object : FavoritesListRecyclerAdapter.OnItemClickListener {
                override fun onClick(view: View, data: Favorite) {
                    updateFavoriteToFirebase(data)
                }
            })
        }
    }

    private fun updateFavoriteToFirebase(favorite: Favorite) {
        // Key is favorite ID, Value is favorite name in array list and at 0 index
        val favoriteDataMap: HashMap<String, List<String>> = HashMap()
        favoriteDataMap[favorite.id] = listOf(favorite.name, favorite.imageUrl)

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserFavoriteInfoToFirebase(FavoritesType.actress.name, favoriteDataMap)

        val intent = Intent(this, MusicianFavoriteActivity::class.java)
        startActivity(intent)
    }

    private fun filter(text: String) {
        val filteredlist: ArrayList<Favorite> = ArrayList()
        for (item in favoritesData) {
            if (item.name.lowercase(Locale.getDefault()).contains(text.lowercase(Locale.getDefault()))) {
                filteredlist.add(item)
            }
        }
        favoritesListRecyclerAdapter.filterList(filteredlist)
    }
}
