package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.MenuItem
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import com.lovebeats.R
import com.lovebeats.extensions.launchModeWithNoBackStack
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.ui.home.browseProfiles.BrowseProfilesActivity
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils.Companion.afterTextChanged

class HometownActivity : AppCompatActivity() {

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, HometownActivity::class.java)
        }
    }

    private lateinit var buttonLargeActiveButton: Button
    private lateinit var toolbar: Toolbar
    private lateinit var skipTextView: TextView
    private lateinit var homeTownEditText: EditText

    private var homeTown: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.hometown_activity)
        this.init()
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        // Configure Navigation Bar #1 component
        toolbar = this.findViewById(R.id.toolbar)

        homeTownEditText = this.findViewById(R.id.home_town_edit_text)
        homeTownEditText.requestFocus()

        homeTownEditText.afterTextChanged {
            if (it.isNotEmpty()) {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                buttonLargeActiveButton.isEnabled = true
            } else {
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
                buttonLargeActiveButton.isEnabled = false
            }
        }

        setupToolbar()
    }

    fun setupToolbar() {
        toolbar = findViewById(R.id.toolbar)

        skipTextView = this.findViewById(R.id.skip_text_view)

        setSupportActionBar(toolbar)

        val actionBar = supportActionBar
        val drawable: Drawable? = ContextCompat.getDrawable(this, R.drawable.ic_arrow_left)

        actionBar?.setHomeAsUpIndicator(drawable)

        // Display the app icon in action bar/toolbar
        actionBar?.setDisplayShowTitleEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)

        skipTextView.setOnClickListener { view -> this.onSkipButtonPressed() }
    }

    private fun onSkipButtonPressed() {
        startBrowseProfilesScreen()
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            homeTown = homeTownEditText.text.toString()
            saveHomeTownToFirebase()
            startBrowseProfilesScreen()
        }
    }

    private fun startBrowseProfilesScreen() {
        val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
        this.startActivity(intent)
    }

    private fun saveHomeTownToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.homeTown, homeTown)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }
}
