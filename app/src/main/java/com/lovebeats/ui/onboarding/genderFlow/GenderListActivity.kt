package com.lovebeats.ui.onboarding.genderFlow

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.lovebeats.analytics.AnalyticsTrackingService
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.analytics.USER_ORIENTATION_PROPERTY
import com.lovebeats.databinding.ActivityGenderListBinding
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.OtherGenderType
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter

class GenderListActivity : AppCompatActivity() {

    private lateinit var binding: ActivityGenderListBinding

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, GenderListActivity::class.java)
        }
    }

    private val genders: ArrayList<OtherGenderType> = ArrayList()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGenderListBinding.inflate(layoutInflater)
        setContentView(binding.root)

        init()

        ScreenRouter.saveScreenInfoToFirebase(this, GenderListActivity::class.java.simpleName)
    }

    private fun init() {

        binding.skipTextView.setOnClickListener {
            onCancelButtonPressed()
        }

        // Configure table component
        binding.tableRecyclerView.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)

        addGenders()

        val genderAdapter = GenderTypeListRecyclerAdapter(genders, this)

        binding.tableRecyclerView.adapter = genderAdapter

        genderAdapter.setOnItemClickListener(object :
            GenderTypeListRecyclerAdapter.OnItemClickListener {
            override fun onClick(view: View, data: OtherGenderType) {
                saveGenderOrientation(data)
                startWhoShouldSeeProfileActivity()
            }
        })
    }

    private fun onCancelButtonPressed() {
        val intent = IdentifyYourselfActivity.newIntent(this)
        this.startActivity(intent)
    }

    private fun startWhoShouldSeeProfileActivity() {
        if (!AppUtils.isNetworkConnected(application)) {
            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            val intent = WhoShouldSeeActivity.newIntent(this)
            this.startActivity(intent)
        }
    }

    private fun addGenders() {
        OtherGenderType.values().forEach {
            genders.add(it)
        }
    }

    private fun saveGenderOrientation(genderType: OtherGenderType) {
        AccountPreferences.getInstance(this).setValue(Constants.genderOrientation, genderType.toString())

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.genderOrientation, genderType.toString())

        AnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, genderType.toString())
        MixPanelAnalyticsTrackingService.setUserProperty(this, USER_ORIENTATION_PROPERTY, genderType.toString())
    }
}