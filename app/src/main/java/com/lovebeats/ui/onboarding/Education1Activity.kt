package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.SpannableString
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.lovebeats.R
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.Utils
import com.lovebeats.utils.Utils.Companion.afterTextChanged


class Education1Activity : AppCompatActivity() {

    companion object {

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, Education1Activity::class.java)
        }
    }

    private lateinit var buttonLargeActiveButton: Button
    private lateinit var addAnotherTextView: TextView
    private lateinit var toolbar: Toolbar
    private lateinit var skipTextView: TextView
    private lateinit var school1EditText: EditText
    private lateinit var school2EditText: EditText
    private lateinit var school2ContraintLayout: ConstraintLayout

    private var school1: String = ""
    private var school2: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.education1_activity)
        this.init()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->
            this.onButtonLargeActivePressed()
        }

        // Configure +Add Another component
        addAnotherTextView = this.findViewById(R.id.add_another_text_view)
        val addAnotherTextViewText = SpannableString(this.getString(R.string.education1_activity_add_another_text_view_text))
        addAnotherTextView.text = addAnotherTextViewText

        addAnotherTextView.setOnClickListener { view -> this.addAnotherSchoolButtonClicked() }

        // Configure Navigation Bar #1 component
        toolbar = this.findViewById(R.id.toolbar)

        school1EditText = this.findViewById(R.id.school_edit_text)
        school2EditText = this.findViewById(R.id.school_edit_text_2)

        school1EditText.requestFocus()

        school1EditText.afterTextChanged {
            if (it.isNotEmpty()) {
                addAnotherTextView.isEnabled = true
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    addAnotherTextView.setTextColor(resources.getColor(R.color.color_primary, null))
                }
                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_active_state_ripple)
                buttonLargeActiveButton.isEnabled = true
            } else {
                addAnotherTextView.isEnabled = false

                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    addAnotherTextView.setTextColor(resources.getColor(R.color.white, null))
                }

                buttonLargeActiveButton.setBackgroundResource(R.drawable.bottom_button_disabled_state)
                buttonLargeActiveButton.isEnabled = false
            }
        }

        school2ContraintLayout = this.findViewById(R.id.field_short_placeholder_constraint_layout_2)

        setupToolbar()
    }

    private fun addAnotherSchoolButtonClicked() {
        addAnotherTextView.visibility = View.GONE
        school2ContraintLayout.visibility = View.VISIBLE
    }

    fun setupToolbar() {

        toolbar = findViewById(R.id.toolbar)

        skipTextView = this.findViewById(R.id.skip_text_view)

        setSupportActionBar(toolbar)

        val actionBar = supportActionBar
        val drawable: Drawable? = ContextCompat.getDrawable(this, R.drawable.ic_arrow_left)

        actionBar?.setHomeAsUpIndicator(drawable)

        // Display the app icon in action bar/toolbar
        actionBar?.setDisplayShowTitleEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)

        skipTextView.setOnClickListener { view -> this.onSkipButtonPressed() }
    }

    private fun onSkipButtonPressed() {
        startHometownActivity()
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {
            school1 = school1EditText.text.toString()
            startHometownActivity()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {

        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }

        return super.onOptionsItemSelected(item)
    }

    private fun startHometownActivity() {

        saveEducationToFirebase()
        this.startActivity(HometownActivity.newIntent(this))
    }

    private fun saveEducationToFirebase() {

        val firebaseDatabaseReference = FirebaseDatabaseUtil(this)
        firebaseDatabaseReference.saveUserInfoToFirebase(Constants.school, school1)

        school2 = school2EditText.text.toString()
        if (Utils.isValidString(school2)) {
            firebaseDatabaseReference.saveUserInfoToFirebase(Constants.school2, school2)
        }
    }
}
