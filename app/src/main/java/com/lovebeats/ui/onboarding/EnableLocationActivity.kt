package com.lovebeats.ui.onboarding

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.SpannableString
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.lovebeats.R
import com.lovebeats.extensions.launchModeWithNoBackStack
import com.lovebeats.models.UserObject
import com.lovebeats.services.BackgroundLocationIntentService
import com.lovebeats.services.BackgroundLocationIntentService.Companion.locationPermissionRequestCode
import com.lovebeats.services.BackgroundLocationIntentService.Companion.locationPermissionsList
import com.lovebeats.ui.home.browseProfiles.BrowseProfilesActivity
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.ManagePermissions
import com.lovebeats.utils.ScreenRouter
import com.lovebeats.utils.ScreenRouter.Companion.setLastSeenScreen
import timber.log.Timber

class EnableLocationActivity : AppCompatActivity() {

    private lateinit var managePermissions: ManagePermissions

    companion object {

        const val TAG = ScreenRouter.LOCATION_ACTIVITY

        fun newIntent(context: Context): Intent {

            // Fill the created intent with the data you want to be passed to this Activity when it's opened.
            return Intent(context, EnableLocationActivity::class.java)
        }
    }

    private lateinit var whereAreYouLocateTextView: TextView
    private lateinit var enableYourLocationTextView: TextView
    private lateinit var buttonLargeActiveButton: Button

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        this.setContentView(R.layout.enable_location_activity)
        this.init()

        setLastSeenScreen(TAG, this)
        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun init() {

        managePermissions = ManagePermissions(this, locationPermissionsList, locationPermissionRequestCode)

        // Configure Where are you locate component
        whereAreYouLocateTextView = this.findViewById(R.id.where_are_you_locate_text_view)
        val whereAreYouLocateTextViewText = SpannableString(this.getString(R.string.location_screen_title))
        whereAreYouLocateTextView.text = whereAreYouLocateTextViewText

        // Configure Enable your location component
        enableYourLocationTextView = this.findViewById(R.id.enable_your_location_text_view)
        val enableYourLocationTextViewText = SpannableString(this.getString(R.string.location_screen_desc))
        enableYourLocationTextView.text = enableYourLocationTextViewText

        // Configure button_large_active component
        buttonLargeActiveButton = this.findViewById(R.id.button_enable_location)
        buttonLargeActiveButton.setOnClickListener { view ->

            this.onButtonLargeActivePressed()
        }
    }

    fun onButtonLargeActivePressed() {

        if (!AppUtils.isNetworkConnected(application)) {

            ScreenRouter.navigateToBlankNoNetworkConnectionScreen(this)
        } else {

            if (managePermissions.isPermissionGranted()) {

                startSpecifyNeighborhoodActivity()
            } else {

                managePermissions.checkPermissions()
            }
        }
    }

    private fun startSpecifyNeighborhoodActivity() {

        if (UserObject.isServerOnboardingComplete) {

            val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
            this.startActivity(intent)
        } else {
            this.startActivity(FirstNameActivity.newIntent(this))
        }
    }

    // Receive the permissions request result
    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>,
                                            grantResults: IntArray) {
        when (requestCode) {

            locationPermissionRequestCode -> {

                val isPermissionsGranted = managePermissions
                        .processPermissionsResult(requestCode, permissions, grantResults)

                if (isPermissionsGranted) {

                    startSpecifyNeighborhoodActivity()

                    saveLocationDetails()
                } else {

                    Timber.d("Location permission denied")

                    startSpecifyNeighborhoodActivity()
                }
                return
            }
        }
    }

    private fun saveLocationDetails() {

        val intent = Intent(this, BackgroundLocationIntentService::class.java)
        BackgroundLocationIntentService.enqueueWork(this, intent)
    }
}
