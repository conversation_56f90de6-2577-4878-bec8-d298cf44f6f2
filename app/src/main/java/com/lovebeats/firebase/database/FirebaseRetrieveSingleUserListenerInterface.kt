package com.lovebeats.firebase.database

import com.google.firebase.database.DataSnapshot
import com.google.firebase.firestore.DocumentSnapshot
import com.lovebeats.models.User

interface FirebaseRetrieveSingleUserListenerInterface {

    fun onSuccess(dataSnapshot: DataSnapshot)
    fun onFailure()
}

interface FirebaseRetrieveSingleUserListenerInterfaceFirestore {

    fun onSuccess(user: User)
    fun onFailure()
}

interface FirebaseRetrieveSingleUserRawDataListenerInterfaceFirestore {

    fun onSuccess(userDocumentSnapShot: DocumentSnapshot)
    fun onFailure()
}