package com.lovebeats.firebase.fcm

import android.app.NotificationManager
import android.content.Context
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.Constants

class NotificationManager {

    companion object {

        fun clearAllNotifications(context: Context) {

            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancelAll()
        }

        fun setUnreadMessageStatus(context: Context, status: Boolean) {
            AccountPreferences.getInstance(context).setValue(Constants.isNewMessageReceived, status)
        }

        fun getUnreadMessageStatus(context: Context) {
            AccountPreferences.getInstance(context).getBooleanValue(Constants.isNewMessageReceived, false)
        }
    }
}