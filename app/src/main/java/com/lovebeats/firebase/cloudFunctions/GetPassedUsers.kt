package com.lovebeats.firebase.cloudFunctions
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import com.google.firebase.perf.FirebasePerformance
import com.google.gson.Gson
import com.lovebeats.models.LikesTabUser
import timber.log.Timber
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlin.jvm.java
import kotlin.to

object GetPassedUsers {
    enum class GetUsersResponse {
        SUCCESS, FAILED
    }

    data class GetPassedUsersResult(
        val status: String,
        val users: List<LikesTabUser>
    )


    suspend fun getPassedUsers(
        uid: String,
        page: Int = 1,
        limit: Int = 20
    ): Pair<List<LikesTabUser>?, GetUsersResponse> = suspendCoroutine { continuation ->
        Timber.d("getPassedUsers API called")
        val trace = FirebasePerformance.startTrace("getPassedUsers_Trace")
        trace.start()

        try {
            val functions = Firebase.functions("asia-southeast1")
            val data = hashMapOf(
                "uid" to uid,
                "page" to page,
                "limit" to limit
            )
            functions
                .getHttpsCallable("getPassedUsers")
                .call(data)
                .addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        try {
                            val result = Gson().fromJson(
                                task.result?.getData()?.toString(),
                                GetPassedUsersResult::class.java
                            )
                            continuation.resume(Pair(result.users, GetUsersResponse.SUCCESS))
                        } catch (e: Exception) {
                            Timber.e("JSON parsing error in getPassedUsers: $e")
                            continuation.resume(Pair(null, GetUsersResponse.FAILED))
                        }
                    } else {
                        Timber.e("getPassedUsers API failed: ${task.exception}")
                        continuation.resume(Pair(null, GetUsersResponse.FAILED))
                    }
                    trace.stop()
                }
        } catch (exception: Exception) {
            Timber.e("Exception in getPassedUsers API: $exception")
            trace.stop()
            continuation.resume(Pair(null, GetUsersResponse.FAILED))
        }
    }
}

