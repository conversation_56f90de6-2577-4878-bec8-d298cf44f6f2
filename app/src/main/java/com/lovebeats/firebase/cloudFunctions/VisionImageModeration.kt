package com.lovebeats.firebase.cloudFunctions

import android.content.Context
import android.util.Base64.DEFAULT
import android.util.Base64.encodeToString
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import com.google.firebase.perf.ktx.performance
import com.google.gson.Gson
import com.lovebeats.BuildConfig
import com.lovebeats.analytics.MP_CLOUD_VISION
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.models.ImageModerationResponse
import timber.log.Timber

object VisionImageModeration {
    fun checkImageForModerationNode(context: Context?, uid: String, imageByteArray: ByteArray, callback:(Boolean) -> Unit) {
        val trace = Firebase.performance.newTrace("CheckImageForModerationNode_Trace")
        trace.start()
        try {
            if (BuildConfig.DEBUG) {
                callback(false)
                return
            }

            val functions = Firebase.functions
            val data = hashMapOf(
                    "uid" to uid,
                    "encodedImageString" to encodeToString(imageByteArray, DEFAULT)
            )
            functions
                    .getHttpsCallable("scanImageForModeration")
                    .call(data)
                    .addOnCompleteListener { task ->
                       if (task.isSuccessful) {
                           val result = Gson().fromJson(task.result?.getData()?.toString(), ImageModerationResponse::class.java)
                           MixPanelAnalyticsTrackingService.logApiSuccessEvent(context, MP_CLOUD_VISION)
                           callback(result?.isPhotoViolated?: false)
                       }else {
                           MixPanelAnalyticsTrackingService.logApiFailedEvent(
                               context,
                               MP_CLOUD_VISION,
                               task.exception?.message.toString()
                           )
                           callback(false)
                       }
                    }
        }catch (exception: Exception) {
            MixPanelAnalyticsTrackingService.logApiFailedEvent(
                context,
                MP_CLOUD_VISION,
                exception.message.toString()
            )
            Timber.e("Error in image moderation: $exception")
            callback(false)
        }
        trace.stop()
    }
}
