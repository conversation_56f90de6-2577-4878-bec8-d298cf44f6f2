package com.lovebeats.firebase.cloudFunctions

import android.content.Context
import com.google.gson.Gson
import com.lovebeats.firebase.cloudFunctions.FavoritesData.getActorsData
import com.lovebeats.firebase.cloudFunctions.FavoritesData.getActressData
import com.lovebeats.firebase.cloudFunctions.FavoritesData.getMusiciansData
import com.lovebeats.firebase.cloudFunctions.FavoritesData.getSingersData
import com.lovebeats.models.Favorite
import com.lovebeats.models.FavoritesType
import com.lovebeats.utils.fromJson
import com.lovebeats.utils.loadJSONFromAssets
import org.json.JSONArray

object Favorites {
    fun getFavoritesData(context: Context, type:String, callback:(List<Favorite>) -> Unit) {
        when (type) {
            FavoritesType.actor.name -> {
                callback(getActorsData(context))
            }
            FavoritesType.actress.name -> {
                callback(getActressData(context))
            }
            FavoritesType.musician.name -> {
                callback(getMusiciansData(context))
            }
            FavoritesType.singer.name -> {
                callback(getSingersData(context))
            }
        }
    }

    fun parseFavoritesFromFireStoreObject(data: Map<String, Map<String, List<String>>>): ArrayList<Favorite> {
        // key is favorite type eg. actor
        // value is hashmap with key as ID
        // value is array with [name, imageUrl]
        val favoriteList = arrayListOf<Favorite>()
        for((key, value) in data) {
            var favorite: Favorite? = null
            val dataArrayList: List<String>? = value[value.keys.first()]
            if (!dataArrayList.isNullOrEmpty()) {
                favorite = Favorite(id = value.keys.first(), name = dataArrayList[0], imageUrl = dataArrayList[1], type = key)
            }
            favorite?.let { favoriteList.add(it) }
        }

        return favoriteList
    }
}

object FavoritesData {
    fun getActorsData(context: Context): List<Favorite> {
        val actors = JSONArray(context.loadJSONFromAssets("actors.json")).toString()
        return Gson().fromJson<List<Favorite>>(actors)
    }

    fun getActressData(context: Context): List<Favorite> {
        val actress = JSONArray(context.loadJSONFromAssets("actress.json")).toString()
        return Gson().fromJson<List<Favorite>>(actress)
    }

    fun getMusiciansData(context: Context): List<Favorite> {
        val musicians = JSONArray(context.loadJSONFromAssets("musicians.json")).toString()
        return Gson().fromJson<List<Favorite>>(musicians)
    }

    fun getSingersData(context: Context): List<Favorite> {
        val singers = JSONArray(context.loadJSONFromAssets("singers.json")).toString()
        return Gson().fromJson<List<Favorite>>(singers)
    }
}

