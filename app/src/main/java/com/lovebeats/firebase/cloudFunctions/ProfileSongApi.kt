package com.lovebeats.firebase.cloudFunctions

import android.content.Context
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import com.google.firebase.perf.ktx.performance
import com.google.gson.Gson
import com.lovebeats.analytics.MP_GET_SONGS
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.models.ProfileSongsResponse
import com.lovebeats.models.Song
import com.lovebeats.utils.Constants.songId
import com.lovebeats.utils.Constants.songImageUrl
import com.lovebeats.utils.Constants.songSubTitle
import com.lovebeats.utils.Constants.songTitle
import com.lovebeats.utils.Constants.songUrl
import com.lovebeats.utils.Constants.spotifyUrl
import com.lovebeats.utils.fromJson
import com.lovebeats.utils.loadJSONFromAssets
import org.json.JSONArray
import timber.log.Timber

object ProfileSongApi {
    fun getProfileSongs(context: Context, uid: String, searchText: String, callback:(List<Song>?) -> Unit) {
        Timber.d("getProfileSongs API called")
        val trace = Firebase.performance.newTrace("getProfileSongs_Trace")
        trace.start()
        try {
            val functions = Firebase.functions
            val data = hashMapOf(
                "uid" to uid,
                "searchText" to searchText,
            )
            functions
                .getHttpsCallable("getTracks")
                .call(data)
                .addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        try {
                            val result = Gson().fromJson(task.result?.getData()?.toString(), ProfileSongsResponse::class.java)
                            MixPanelAnalyticsTrackingService.logApiSuccessEvent(context, MP_GET_SONGS)
                            callback(result.tracks)
                        }catch (ex: Exception) {
                            MixPanelAnalyticsTrackingService.logApiFailedEvent(
                                context,
                                MP_GET_SONGS,
                                ex.message.toString()
                            )
                            callback(getTracksData(context))
                            Timber.e("Exception in getProfileSongs API Parsing: $ex")
                        }
                    }else {
                        MixPanelAnalyticsTrackingService.logApiFailedEvent(
                            context,
                            MP_GET_SONGS,
                            task.exception?.message.toString()
                        )
                        callback(getTracksData(context))
                        Timber.e("getProfileSongs API failed: ${task.exception}")
                    }
                }
        }catch (exception: Exception) {
            MixPanelAnalyticsTrackingService.logApiFailedEvent(
                context,
                MP_GET_SONGS,
                exception.message.toString()
            )
            callback(getTracksData(context))
            Timber.e("Exception in getProfileSongs API: $exception")
        }
        trace.stop()
    }

    fun parseProfileSongFromFireStore(songDataMap: Map<String, String?>): Song? {
        try {
            return Song(
                id = songDataMap[songId],
                title = songDataMap[songTitle],
                audioUrl = songDataMap[songUrl],
                imageUrl = songDataMap[songImageUrl],
                subTitle = songDataMap[songSubTitle],
                spotifyUrl = songDataMap[spotifyUrl]
            )
        } catch (ex: Exception) {
            Timber.e("Error in parseProfileSongFromFireStore: $ex")
        }
        return null
    }

    private fun getTracksData(context: Context): List<Song> {
        val tracks = JSONArray(context.loadJSONFromAssets("tracks.json")).toString()
        return Gson().fromJson<List<Song>>(tracks)
    }
}