package com.lovebeats.firebase.cloudFunctions

import android.content.Context
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import com.google.firebase.perf.ktx.performance
import com.google.gson.Gson
import com.lovebeats.analytics.MP_GET_USERS
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService.Companion.logApiFailedEvent
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService.Companion.logApiSuccessEvent
import com.lovebeats.models.GetNearByUsersResult
import com.lovebeats.models.NearByUser
import timber.log.Timber

object NearByUsers {

    fun getNearByUsers(context: Context?, uid: String, latitude:Double, longitude:Double, radius: Int, callback: (List<NearByUser>?, GetUsersResponse) -> Unit) {
        Timber.d("getNearByUsers API called")
        val trace = Firebase.performance.newTrace("getNearByUsers_Trace")
        trace.start()
        try {
            val functions = Firebase.functions("asia-southeast1")
            val data = hashMapOf(
                "uid" to uid,
                "latitude" to latitude,
                "longitude" to longitude,
                "radius" to radius
            )
            functions
                .getHttpsCallable("getUsers")
                .call(data)
                .addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        val result = Gson().fromJson(task.result?.getData()?.toString(), GetNearByUsersResult::class.java)
                        logApiSuccessEvent(context, MP_GET_USERS)
                        callback(result.users, GetUsersResponse.SUCCESS)
                    }else {
                        logApiFailedEvent(context, MP_GET_USERS, task.exception?.message.toString())
                        callback(null, GetUsersResponse.FAILED)
                        Timber.e("getNearByUsers API failed: ${task.exception}")
                    }
                }
        }catch (exception: Exception) {
            logApiFailedEvent(context, MP_GET_USERS, exception.message.toString())
            callback(null, GetUsersResponse.FAILED)
            Timber.e("Exception in getNearByUsers API: $exception")
        }
        trace.stop()
    }
}

enum class GetUsersResponse{
    SUCCESS,
    FAILED
}