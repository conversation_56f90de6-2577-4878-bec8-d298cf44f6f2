package com.lovebeats.glide

import android.app.Activity
import android.content.Context
import android.graphics.PorterDuff
import android.graphics.drawable.Drawable
import android.widget.ImageView
import androidx.swiperefreshlayout.widget.CircularProgressDrawable
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.signature.ObjectKey
import com.lovebeats.R
import com.lovebeats.ui.SpacesImagesViewModel
import com.lovebeats.utils.AppUtils
import timber.log.Timber
import java.io.File

object ImageLoaderModule {

    fun loadImageIntoImageViewWithLocalPath(context: Context, localImagePath: String, resultImageView: ImageView) {
        GlideApp.with(context)
                .load(localImagePath)
                .apply(RequestOptions().centerCrop())
                .into(resultImageView)
    }

    fun loadImageIntoImageViewWithFile(context: Context, file: File, resultImageView: ImageView) {
        GlideApp.with(context)
                .load(file)
                .apply(RequestOptions().centerCrop())
                .into(resultImageView)
    }

    fun loadImageIntoImageViewWithSpacesPathCircleTransformAndLoading(activity: Activity,
                                                                      spacesImagesViewModel: SpacesImagesViewModel,
                                                                      spacesImagePath: String,
                                                                      resultImageView: ImageView) {

        try {
            if (!activity.isFinishing && !activity.isDestroyed) {
                val circularProgressDrawable = CircularProgressDrawable(activity)
                circularProgressDrawable.setColorFilter(activity.resources.getColor(R.color.color_primary, null), PorterDuff.Mode.SRC_ATOP)
                circularProgressDrawable.strokeWidth = 5f
                circularProgressDrawable.centerRadius = 30f
                circularProgressDrawable.start()

                spacesImagesViewModel.getImageUrl(spacesImagePath) { url, _ ->
                    if (!activity.isFinishing && !activity.isDestroyed) {
                        activity.runOnUiThread {
                            GlideApp.with(activity)
                                .load(url)
                                .apply(RequestOptions.circleCropTransform())
                                .placeholder(circularProgressDrawable)
                                .into(resultImageView)
                        }
                    }
                }
            }
        }catch (exception: Exception) {
            Timber.e("Exception in showing circle image: $exception")
        }
    }

    fun loadImageIntoImageViewWithLoadingAndCallback(context: Context,
                                                     activity: Activity,
                                                     spacesImagesViewModel: SpacesImagesViewModel,
                                                     spacesImagePath: String,
                                                     resultImageView: ImageView,
                                                     onSuccess: () -> Unit,
                                                     onFailure: () -> Unit,
                                                     shouldCacheImage: Boolean = false) {

        try {
            if (isValidContextForGlide(context)) {
                val screenWidth = AppUtils.getScreenWidth(activity)
                val screenHeight = AppUtils.getHeightForImages()

                val circularProgressDrawable = CircularProgressDrawable(activity)
                circularProgressDrawable.setColorFilter(context.resources.getColor(R.color.color_primary, null), PorterDuff.Mode.SRC_ATOP)
                circularProgressDrawable.strokeWidth = 5f
                circularProgressDrawable.centerRadius = 30f
                circularProgressDrawable.start()

                spacesImagesViewModel.getImageUrl(spacesImagePath) { url, lastModifiedDate ->
                    activity.runOnUiThread {
                        if (url != null) {
                            val glide = GlideApp.with(context)
                                .load(AwsGlideCacheUrl(url))
                                .override(screenWidth, screenHeight)
                                .placeholder(circularProgressDrawable)

                            if (shouldCacheImage && lastModifiedDate != null && lastModifiedDate != "null") {
                                val requestOptions = RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL).signature(ObjectKey(lastModifiedDate))
                                glide.apply(requestOptions)
                            }

                            glide.centerCrop()
                                .listener(object : RequestListener<Drawable> {
                                    override fun onResourceReady(
                                        resource: Drawable,
                                        model: Any,
                                        target: Target<Drawable>?,
                                        dataSource: DataSource,
                                        isFirstResource: Boolean
                                    ): Boolean {
                                        onSuccess()
                                        return false
                                    }

                                    override fun onLoadFailed(
                                        e: GlideException?,
                                        model: Any?,
                                        target: Target<Drawable>,
                                        isFirstResource: Boolean
                                    ): Boolean {
                                        onFailure()
                                        return false
                                    }
                                })
                                .into(resultImageView)
                        } else {
                            onFailure()
                        }
                    }
                }
            }
        }catch (exception: Exception) {
            Timber.e("Exception in showing loadImageIntoImageViewWithLoadingAndCallback: $exception")
        }
    }

    fun loadImageIntoImageViewForIceBreakersCallback(context: Context,
                                                     activity: Activity,
                                                     spacesImagesViewModel: SpacesImagesViewModel,
                                                     spacesImagePath: String,
                                                     resultImageView: ImageView,
                                                     onSuccess: () -> Unit,
                                                     onFailure: () -> Unit,
                                                     shouldCacheImage: Boolean = false) {

        try {
            if (isValidContextForGlide(context)) {
                val circularProgressDrawable = CircularProgressDrawable(activity)
                circularProgressDrawable.setColorFilter(context.resources.getColor(R.color.color_primary, null), PorterDuff.Mode.SRC_ATOP)
                circularProgressDrawable.strokeWidth = 5f
                circularProgressDrawable.centerRadius = 30f
                circularProgressDrawable.start()

                spacesImagesViewModel.getImageUrl(spacesImagePath) { url, lastModifiedDate ->
                    activity.runOnUiThread {
                        if (url != null) {
                            val glide = GlideApp.with(context)
                                .load(AwsGlideCacheUrl(url))
                                .placeholder(circularProgressDrawable)

                            if (shouldCacheImage && lastModifiedDate != null && lastModifiedDate != "null") {
                                val requestOptions = RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL).signature(ObjectKey(lastModifiedDate))
                                glide.apply(requestOptions)
                            }

                            glide.centerCrop()
                                .listener(object : RequestListener<Drawable> {
                                    override fun onResourceReady(
                                        resource: Drawable,
                                        model: Any,
                                        target: Target<Drawable>?,
                                        dataSource: DataSource,
                                        isFirstResource: Boolean
                                    ): Boolean {
                                        onSuccess()
                                        return false
                                    }

                                    override fun onLoadFailed(
                                        e: GlideException?,
                                        model: Any?,
                                        target: Target<Drawable>,
                                        isFirstResource: Boolean
                                    ): Boolean {
                                        onFailure()
                                        return false
                                    }
                                })
                                .into(resultImageView)
                        } else {
                            onFailure()
                        }
                    }
                }
            }
        }catch (exception: Exception) {
            Timber.e("Exception in showing loadImageIntoImageViewWithLoadingAndCallback: $exception")
        }
    }

    fun loadImageIntoImageViewNoCropWithLoadingAndCallback(activity: Activity,
                                                     spacesImagesViewModel: SpacesImagesViewModel,
                                                     spacesImagePath: String,
                                                     resultImageView: ImageView,
                                                     width: Int,
                                                     height: Int,
                                                     shouldCacheImage: Boolean = false) {

        try {
            if (!activity.isFinishing && !activity.isDestroyed) {

                val circularProgressDrawable = CircularProgressDrawable(activity)
                circularProgressDrawable.setColorFilter(activity.resources.getColor(R.color.color_primary, null), PorterDuff.Mode.SRC_ATOP)
                circularProgressDrawable.strokeWidth = 5f
                circularProgressDrawable.centerRadius = 30f
                circularProgressDrawable.start()

                spacesImagesViewModel.getImageUrl(spacesImagePath) { url, lastModifiedDate ->
                    activity.runOnUiThread {
                        val glide = GlideApp.with(activity)
                            .load(AwsGlideCacheUrl(url))
                            .override(width, height)
                            .placeholder(circularProgressDrawable)

                        if (shouldCacheImage && lastModifiedDate != null && lastModifiedDate != "null") {
                            val requestOptions = RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL).signature(ObjectKey(lastModifiedDate))
                            glide.apply(requestOptions)
                        }
                        glide.into(resultImageView)
                    }
                }
            }
        }catch (exception: Exception) {
            Timber.e("Exception in loadImageIntoImageViewNoCropWithLoadingAndCallback: $exception")
        }
    }

    fun loadImageIntoImageViewWithCallback(activity: Activity,
                                           spacesImagesViewModel: SpacesImagesViewModel,
                                           spacesImagePath: String,
                                           resultImageView: ImageView,
                                           placeHolder: Int,
                                           onSuccess: () -> Unit,
                                           onFailure: () -> Unit,
                                           shouldCacheImage: Boolean = false) {
        try {
            if (!activity.isFinishing && !activity.isDestroyed) {
                spacesImagesViewModel.getImageUrl(spacesImagePath) { url, lastModifiedDate ->
                    activity.runOnUiThread {
                        if (!url.isNullOrEmpty()) {
                            val glide = GlideApp.with(activity)
                                .load(AwsGlideCacheUrl(url))
                                .placeholder(placeHolder)

                            if (shouldCacheImage && lastModifiedDate != null && lastModifiedDate != "null") {
                                val requestOptions = RequestOptions().diskCacheStrategy(DiskCacheStrategy.ALL).signature(ObjectKey(lastModifiedDate))
                                glide.apply(requestOptions)
                            }

                            glide.centerCrop()
                                .listener(object : RequestListener<Drawable> {
                                    override fun onResourceReady(
                                        resource: Drawable,
                                        model: Any,
                                        target: Target<Drawable>?,
                                        dataSource: DataSource,
                                        isFirstResource: Boolean
                                    ): Boolean {
                                        onSuccess()
                                        return false
                                    }

                                    override fun onLoadFailed(
                                        e: GlideException?,
                                        model: Any?,
                                        target: Target<Drawable>,
                                        isFirstResource: Boolean
                                    ): Boolean {
                                        onFailure()
                                        return false
                                    }
                                })
                                .into(resultImageView)
                        }else {
                            Timber.e("glide url is empty")
                        }
                    }
                }
            }
        }catch (exception: Exception) {
            Timber.e("Exception in loadImageIntoImageViewWithCallback: $exception")
        }
    }

    private fun isValidContextForGlide(context: Context): Boolean {
        if (context is Activity) {
            if (context.isDestroyed || context.isFinishing) {
                return false
            }
        }
        return true
    }
}