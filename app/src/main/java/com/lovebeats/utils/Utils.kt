package com.lovebeats.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.telephony.TelephonyManager
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Patterns
import android.widget.EditText
import androidx.appcompat.app.AlertDialog
import com.google.firebase.installations.FirebaseInstallations
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.LikesTabUser
import com.lovebeats.models.User
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.Constants.cmMultiplier
import com.lovebeats.utils.Constants.inchMultiplier
import timber.log.Timber
import java.text.DateFormat
import java.text.ParseException
import java.text.ParsePosition
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import kotlin.math.roundToLong

class Utils {

    companion object {

        fun isValidEmail(email: String): Boolean = email.isNotEmpty() &&
                Patterns.EMAIL_ADDRESS.matcher(email).matches()

        fun isValidPhone(phone: String): Boolean = phone.isNotEmpty() &&
                Patterns.PHONE.matcher(phone).matches()

        fun isValidString(string: String): Boolean = !TextUtils.isEmpty(string)

        fun heightInFeetFromCms(heightInCms: Double): String {

            val feet = heightInCms / cmMultiplier
            val inches = heightInCms / inchMultiplier - feet.toInt() * 12
            return (feet.toInt().toString()).plus("ft").plus(inches.toInt().toString()).plus("in")
        }

        fun getDeviceDataForLogging(context: Context, callback: (String) -> Unit) {
            FirebaseInstallations.getInstance().id.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val deviceId = task.result
                    val version = AppUtils.getAppVersion()
                    val locale: String =
                        context.resources.configuration.locales[0].country
                    val tm = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager?
                    val countryISO = tm?.simCountryIso

                    val result = "deviceId: $deviceId, appVersion: $version, locale: $locale, countryISO: $countryISO"
                    callback(result)
                }
            }
        }

        fun heightInFeetFromInches(heightInInches: Double): String {

            if (!TextUtils.isEmpty(heightInInches.toString())) {
                val feet = heightInInches / 12
                val inches = heightInInches % 12
                return (feet.toInt().toString()).plus("ft").plus(inches.toInt().toString())
                    .plus("in")
            }
            return ""
        }

        fun heightInFeetFromInchesWithQuotes(heightInInches: Double?): String {

            if (heightInInches != null) {

                val feet = heightInInches / 12
                val inches = heightInInches % 12
                return feet.toInt().toString() + "'" + inches.toInt().toString() + "\""
            }
            return ""
        }

        fun heightInFeet2(heightInCms: Double): String {

            if (!TextUtils.isEmpty(heightInCms.toString())) {
                val feet = heightInCms.div(cmMultiplier)
                val inches = heightInCms / inchMultiplier - feet.toInt() * 12

                val roundedInch = roundToHalf(inches)

                val inchAfterDecimal = roundedInch - roundedInch.toInt()

                var inchAfterDecimalString = ""
                if (inchAfterDecimal == 0.5) {
                    inchAfterDecimalString = " 1/2 "
                }

                val finalInches = roundedInch.toInt().toString() + inchAfterDecimalString
                return (feet.toInt().toString()).plus(" ft ").plus(finalInches).plus(" in")
            }
            return ""
        }

        private fun roundToHalf(d: Double): Double {

            return (d * 2).roundToLong() / 2.0
        }

        fun EditText.afterTextChanged(afterTextChanged: (String) -> Unit) {
            this.addTextChangedListener(object : TextWatcher {

                override fun afterTextChanged(s: Editable?) {
                    afterTextChanged.invoke(s.toString())
                }

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            })
        }

        fun EditText.validate(message: String, validator: (String) -> Boolean) {
            this.afterTextChanged {
                validator(it)
                //this.error = if (validator(it)) null else message
            }
            //this.error = if (validator(this.text.toString())) null else message
        }

        fun isUserAgerestricted(dob: String): Boolean {

            if (dob.isEmpty()) {

                return false
            }

            try {

                val userFormattedDob = dob.replace('/', '-')
                val sdf = SimpleDateFormat("MM-dd-yyyy", Locale.US)
                val dobCalender = Calendar.getInstance()
                dobCalender.time = sdf.parse(userFormattedDob)

                if (!isUser18YearsOld(dobCalender)) {

                    return true
                }
            } catch (exception: java.lang.Exception) {

                Timber.d("exception in isUserAgerestricted: $exception")
            }

            return false
        }

        fun isValidDate(dateString: String): Boolean {

            try {

                val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.US)
                sdf.isLenient = false
                return sdf.parse(dateString, ParsePosition(0)) != null

            } catch (exception: java.lang.Exception) {

                Timber.e("exception in isValidDate: $exception")
            }

            return false
        }

        fun isUser18YearsOld(dob: Calendar): Boolean {

            try {

                val today = Calendar.getInstance()

                val curYear = today.get(Calendar.YEAR)
                val dobYear = dob.get(Calendar.YEAR)

                var age = curYear - dobYear

                // if dob is month or day is behind today's month or day
                // reduce age by 1
                val curMonth = today.get(Calendar.MONTH)
                val dobMonth = dob.get(Calendar.MONTH)
                if (dobMonth > curMonth) { // this year can't be counted!
                    age--
                } else if (dobMonth == curMonth) { // same month? check for day
                    val curDay = today.get(Calendar.DAY_OF_MONTH)
                    val dobDay = dob.get(Calendar.DAY_OF_MONTH)
                    if (dobDay > curDay) { // this year can't be counted!
                        age--
                    }
                }

                if (age < 18) {

                    return false
                }

            } catch (exception: java.lang.Exception) {

                Timber.e("exception in isUser18YearsOld: $exception")
            }

            return true
        }

        fun getAge(dateOfBirth: Date?): Int {
            var age = 0
            try {
                val born = Calendar.getInstance()
                val now = Calendar.getInstance()
                if (dateOfBirth != null) {
                    now.time = Date()
                    born.time = dateOfBirth
                    if (born.after(now)) {
                        throw IllegalArgumentException("Can't be born in the future")
                    }
                    age = now.get(Calendar.YEAR) - born.get(Calendar.YEAR)
                    if (now.get(Calendar.DAY_OF_YEAR) < born.get(Calendar.DAY_OF_YEAR)) {
                        age -= 1
                    }
                }
            } catch (exception: java.lang.Exception) {
                Timber.e("exception in getting age: $exception")
            }
            return age
        }

        fun isValidUser(userInfo: User?): Boolean {
            return !(TextUtils.isEmpty(userInfo?.name.toString())
                    || TextUtils.isEmpty(userInfo?.dob.toString())
                    || TextUtils.isEmpty(userInfo?.gender.toString())
                    || TextUtils.isEmpty(userInfo?.interestedIn.toString())
                    || userInfo?.isOnboardingComplete == null
                    || userInfo.isOnboardingComplete == false)
        }

        fun isValidLikesYouUser(userInfo: LikesTabUser?): Boolean {
            return !(TextUtils.isEmpty(userInfo?.name.toString())
                    || TextUtils.isEmpty(userInfo?.gender.toString())
                    || TextUtils.isEmpty(userInfo?.interestedIn.toString())
                    || userInfo?.isOnboardingComplete == null
                    || userInfo.isOnboardingComplete == false
                    || userInfo.isUserReported == true)
        }

        fun isThisWeek(date: Date): Boolean {

            val c = Calendar.getInstance()
            c.firstDayOfWeek = Calendar.MONDAY

            c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            c.set(Calendar.HOUR_OF_DAY, 0)
            c.set(Calendar.MINUTE, 0)
            c.set(Calendar.SECOND, 0)
            c.set(Calendar.MILLISECOND, 0)

            val monday = c.time

            val nextMonday = Date(monday.time + 7 * 24 * 60 * 60 * 1000)

            return date.after(monday) && date.before(nextMonday)
        }

        fun getDateFromTimestamp(time: Long): Date {
            val cal = Calendar.getInstance()
            val tz = cal.timeZone//get your local time zone.
            val sdf = SimpleDateFormat("dd/MM/yyyy hh:mm a", Locale.US)
            sdf.timeZone = tz//set time zone.
            val localTime = sdf.format(Date(time))
            var date = Date()
            try {
                date = sdf.parse(localTime)//get local date
            } catch (e: ParseException) {
                Timber.e("exception in get date from timestamp: $e")
            }
            return date
        }

        fun getDeviceDate(): String {
            val dateFormat: DateFormat = SimpleDateFormat("MM-dd-yyyy", Locale.US)
            return dateFormat.format(Calendar.getInstance().time)
        }

        fun extractDateStringFromDateObject(date: Date): String {
            val dateFormat: DateFormat = SimpleDateFormat("MM-dd-yyyy", Locale.US)
            return dateFormat.format(date)
        }

        fun openGmailApp(context: Context, subject: String) {
            try {
                val externalId = AccountPreferences.getInstance(context)
                    .getStringValue(Constants.externalId, "")
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data = Uri.parse("mailto:${Constants.lovebeatAdminEmail}?subject=" + subject + "&body=\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n***********************\nDevice: " + externalId)
                context.startActivity(intent)
            } catch (e: Exception) {
                Timber.d("error opening gmail app: $e")
            }
        }

        fun getKmsFromMeters(meters: Int): Int {

            return (meters * 0.001).toInt()
        }

        fun setDeviceHardwareIdentifier(context: Context) {
            FirebaseInstallations.getInstance().id.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val firebaseDatabaseReference = FirebaseDatabaseUtil(context)
                    firebaseDatabaseReference.saveUserInfoToFirebase(Constants.deviceHardwareIdentifier, task.result)
                }
            }
        }

        fun getIceBreakerQuestions(): HashMap<String, String> {
            val questionsMap: HashMap<String, String> = HashMap()
            questionsMap["The title of my autobiography"] = "Lights, Camera, Chaos"
            questionsMap["My entrance theme song if I was an actor"] = "Dhivara from Bahubali"
            questionsMap["People tell me I look like"] = "Deepika Padukone, especially during her role as Mastani in the movie Bajirao Mastani"
            questionsMap["A hobby I've been meaning to pick up"] =
                "Singing. You would make band member #2."
            questionsMap["The song that always gets me on my feet and dancing is"] =
                "Vachinde from Fidaa"
            questionsMap["If I could have dinner with any musician or actor/actress, it would be"] = "It would be Allu Arjun for his stylish dance moves and charismatic screen presence."
            questionsMap["The movie that made me laugh uncontrollably was"] = "Aha na pellanta"
            questionsMap["I can't resist a good movie soundtrack, especially from"] =
                "Vikram music composed by Anirudh"
            questionsMap["The movie that made me cry my heart out was"] =
                "Jersey, a touching sports drama with a powerful emotional journey."
            questionsMap["The song that perfectly captures my current mood is"] =
                "Inkem Inkem Inkem Kaavaale from the movie Geetha Govindam"
            questionsMap["One movie that I can watch over and over again is"] =
                "Arjun Reddy, a bold and intense romantic drama that pushed boundaries"
            questionsMap["I love going to live music performances, especially when the venue is"] =
                "Fully crowded with lot of energy"
            questionsMap["The movie character I relate to the most is"] =
                "Geet from the movie 'Jab We Met' because of her free-spirited nature and zest for life"
            questionsMap["A movie soundtrack that I find incredibly inspiring is from the film"] =
                "Swades. The music composed by A.R. Rahman beautifully captures the essence of self-discovery and patriotism"
            questionsMap["The most visually stunning movie I've ever watched was"] = "RRR The grandeur and intricate details in the sets and costumes left me mesmerized"
            questionsMap["If I could witness the making of any movie, it would be"] = "Bahubali The Beginning. The scale of production and the innovative techniques used were groundbreaking for Indian cinema."
            questionsMap["My day usually consists of"] =
                "Listening to my favorite movie soundtracks, catching up on the latest movie trailers, and discussing films with friends"
            questionsMap["If I could be any movie character for a day, I would choose"] =
                "Karthik from the movie Vinnaithaandi Varuvaayaa because his journey of love and self-discovery resonates with me, and the film beautifully captures the complexities of relationships"
            questionsMap["I enjoy watching movies from different film industries, including"] =
                "Malayalam cinema. The storytelling and realism in movies like Premam captivate me"
            questionsMap["If I could attend the premiere of any movie, it would be"] =
                "It would be Kaithi because of its gripping storyline, intense action sequences, and the stellar performance by Karthi"
            questionsMap["If I could have a duet with any Indian singer, it would be"] = "With Shreya Ghoshal because her soulful voice brings out the emotions in every song."
            questionsMap["The most memorable on-screen couple for me is"] =
                "Naga Chaitanya and Samantha Akkineni because their chemistry and performances in movies like Majili and Ye Maaya Chesave create a magical on-screen presence"
            questionsMap["If I could have a movie marathon with friends, we would binge-watch"] =
                "We would binge-watch the blockbuster Bahubali series and immerse ourselves in the grandiose world of epic storytelling"
            return questionsMap
        }

        fun getMovieGenres(): List<String> {
            return arrayListOf(
                "Action",
                "Adventure",
                "Animation",
                "Comedy",
                "Crime",
                "Documentary",
                "Drama",
                "Fantasy",
                "Historical",
                "Horror",
                "Musical",
                "Mystery",
                "Romance",
                "Science Fiction",
                "Thriller",
                "War",
                "Western",
                "Biography",
                "Family",
                "Film Noir",
                "Sports",
                "Supernatural",
                "Suspense",
                "Teen",
                "Time Travel",
                "Disaster",
                "Epic",
                "Heist",
                "Martial Arts",
                "Noir",
                "Parody",
                "Psychological",
                "Satire",
                "Silent",
                "Slasher",
                "Spy",
                "Superhero",
                "Swashbuckler",
                "True Crime",
                "Urban",
                "Vampire",
                "Werewolf",
                "Zombie",
                "Courtroom",
                "Cyberpunk",
                "Gangster",
                "Historical Fiction",
                "Political",
                "Art House",
                "Coming-of-Age",
                "Dance",
                "Eco-Thriller",
                "Found Footage",
                "LGBT",
                "Medical",
                "Mockumentary",
                "Post-Apocalyptic",
                "Road Movie",
                "Steampunk",
                "Techno-Thriller",
                "Women's",
                "Action-Comedy",
                "Adventure-Comedy",
                "Dark Comedy",
                "Romantic Comedy",
                "Crime Drama",
                "Erotic Thriller",
                "Experimental",
                "Faith-Based",
                "War Drama"
            )
        }

        fun getMusicGenres(): List<String> {
            return arrayListOf(
                "Telugu",
                "Kannada",
                "Malayalam",
                "Tamil",
                "Bollywood",
                "Classical Indian",
                "Country",
                "Dance",
                "Disco",
                "Electronic",
                "Folk",
                "Funk",
                "Gospel",
                "Hip Hop",
                "Indie",
                "Jazz",
                "Metal",
                "New Age",
                "Opera",
                "Pop",
                "Punk",
                "R&B",
                "Rap",
                "Reggae",
                "Rock",
                "Salsa",
                "Samba",
                "Soul",
                "Techno",
                "Trance",
                "Sufi",
                "Ghazal",
                "Qawwali",
                "Folk",
                "Bhangra",
                "Carnatic",
                "Hindustani",
                "Rabindra Sangeet",
                "Thumri",
                "Lavani",
                "Garba",
                "Rajasthani",
                "Marathi",
                "Bengali",
                "Punjabi",
                "Bhojpuri",
                "Gujarati",
                "Kashmiri",
                "Assamese",
                "Odia",
                "Haryanvi",
                "Marwari",
                "Manipuri",
                "Alternative",
                "Blues",
                "Classical",
                "Acoustic",
                "Ambient",
                "Bossa Nova",
                "Celtic",
                "Chamber",
                "Choral",
                "Christian",
                "Dancehall",
                "Dubstep",
                "Flamenco",
                "House",
                "Instrumental",
                "Latin",
                "Merengue",
                "Orchestral",
                "Psychedelic",
                "Rockabilly",
                "Ska",
                "Swing",
                "Tango",
                "Trip Hop",
                "World",
                "Zydeco"
            )
        }

        fun showAlert(text: String, activity: Context) {
            if (activity is Activity &&
                !activity.isFinishing
            ) {

                val dialogBuilder = AlertDialog.Builder(activity)

                dialogBuilder.setMessage(text)
                    .setCancelable(false)
                    .setPositiveButton("ok") { dialog, id ->

                        dialog.dismiss()
                    }

                val alert = dialogBuilder.create()
                alert.show()
            }
        }

        fun showPermissionAlert(text: String, activity: Context) {
            if (activity is Activity &&
                !activity.isFinishing
            ) {

                val dialogBuilder = AlertDialog.Builder(activity)

                dialogBuilder.setMessage(text)
                    .setCancelable(false)
                    .setPositiveButton("Go to settings") { dialog, id ->
                        activity.startActivity(Intent().apply {
                            action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                            data = Uri.fromParts("package", activity.packageName, null)
                        })
                    }
                    .setNegativeButton("cancel") { dialog, id ->
                        dialog.dismiss()
                    }

                val alert = dialogBuilder.create()
                alert.show()
            }
        }
    }
}
