package com.lovebeats.utils

import android.content.Context
import android.content.Intent
import com.lovebeats.analytics.MP_SCREEN_NAME
import com.lovebeats.analytics.MP_SCREEN_VIEWED
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.extensions.launchModeWithNoBackStack
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.ui.blanks.BlankNoNetworkConnectionActivity
import com.lovebeats.ui.favorites.ProfileSongSelectionActivity
import com.lovebeats.ui.onboarding.ActorFavoriteActivity
import com.lovebeats.ui.onboarding.ActressFavoriteActivity
import com.lovebeats.ui.onboarding.AgerestrictionActivity
import com.lovebeats.ui.onboarding.DateOfBirthActivity
import com.lovebeats.ui.onboarding.EmailActivity
import com.lovebeats.ui.onboarding.EnableLocationActivity
import com.lovebeats.ui.onboarding.FirstNameActivity
import com.lovebeats.ui.onboarding.MusicianFavoriteActivity
import com.lovebeats.ui.onboarding.PhotoUploadActivity
import com.lovebeats.ui.onboarding.PledgeActivity
import com.lovebeats.ui.onboarding.SexPreferenceActivity
import com.lovebeats.ui.onboarding.SingerFavoriteActivity
import com.lovebeats.ui.onboarding.YourHeightActivity
import com.lovebeats.ui.onboarding.genderFlow.IdentifyYourselfActivity
import com.lovebeats.ui.onboarding.genderFlow.InterestedInActivity

class ScreenRouter {

    companion object {

        private const val lastSeenScreen = "lastSeenScreen"

        const val EMAIL_ACTIVITY = "EMAIL_ACTIVITY"
        const val LOCATION_ACTIVITY = "LOCATION_ACTIVITY"
        const val FIRST_NAME_ACTIVITY = "FIRST_NAME_ACTIVITY"
        const val DATE_OF_BIRTH_ACTIVITY = "DATE_OF_BIRTH_ACTIVITY"
        const val AGE_RESTRICTION_ACTIVITY = "AGE_RESTRICTION_ACTIVITY"
        const val SEX_PREFERENCE_ACTIVITY = "SEX_PREFERENCE_ACTIVITY"
        const val IDENTIFY_YOURSELF_ACTIVITY = "IDENTIFY_YOURSELF_ACTIVITY"
        const val INTERESTED_IN_ACTIVITY = "INTERESTED_IN_ACTIVITY"
        const val WHO_SHOULD_SEE_ACTIVITY = "WHO_SHOULD_SEE_ACTIVITY"
        const val YOUR_HEIGHT_ACTIVITY = "YOUR_HEIGHT_ACTIVITY"
        const val IMAGES_ACTIVITY = "IMAGES_ACTIVITY"
        const val PROFILE_SONG_SELECTION_ACTIVITY = "PROFILE_SONG_SELECTION_ACTIVITY"
        const val ACTOR_FAVORITE_ACTIVITY = "ACTOR_FAVORITE_ACTIVITY"
        const val ACTRESS_FAVORITE_ACTIVITY = "ACTRESS_FAVORITE_ACTIVITY"
        const val MUSICIAN_FAVORITE_ACTIVITY = "MUSICIAN_FAVORITE_ACTIVITY"
        const val SINGER_FAVORITE_ACTIVITY = "SINGER_FAVORITE_ACTIVITY"
        const val PLEDGE_ACTIVITY = "PLEDGE_ACTIVITY"

        fun setLastSeenScreen(screenTag: String, context: Context) {
            val sharedPreferences = AccountPreferences(context)
            sharedPreferences.setValue(lastSeenScreen, screenTag)
        }

        fun getLastScreenScreen(context: Context): String {
            val sharedPreferences = AccountPreferences(context)
            return sharedPreferences.getStringValue(lastSeenScreen, "")
        }

        fun navigate(screenTag: String, context: Context) {

            when (screenTag) {

                EMAIL_ACTIVITY -> {
                    navigateToScreen(context, EmailActivity::class.java)
                }
                LOCATION_ACTIVITY -> {
                    navigateToScreen(context, EnableLocationActivity::class.java)
                }
                FIRST_NAME_ACTIVITY -> {
                    navigateToScreen(context, FirstNameActivity::class.java)
                }
                DATE_OF_BIRTH_ACTIVITY -> {
                    navigateToScreen(context, DateOfBirthActivity::class.java)
                }
                AGE_RESTRICTION_ACTIVITY -> {
                    navigateToScreen(context, AgerestrictionActivity::class.java)
                }
                SEX_PREFERENCE_ACTIVITY -> {
                    navigateToScreen(context, SexPreferenceActivity::class.java)
                }
                YOUR_HEIGHT_ACTIVITY -> {
                    navigateToScreen(context, YourHeightActivity::class.java)
                }
                IMAGES_ACTIVITY -> {
                    navigateToScreen(context, PhotoUploadActivity::class.java)
                }
                PROFILE_SONG_SELECTION_ACTIVITY -> {
                    navigateToScreenWithOnboardingIntent(context, ProfileSongSelectionActivity::class.java)
                }
                ACTOR_FAVORITE_ACTIVITY -> {
                    navigateToScreen(context, ActorFavoriteActivity::class.java)
                }
                ACTRESS_FAVORITE_ACTIVITY -> {
                    navigateToScreen(context, ActressFavoriteActivity::class.java)
                }
                MUSICIAN_FAVORITE_ACTIVITY -> {
                    navigateToScreen(context, MusicianFavoriteActivity::class.java)
                }
                SINGER_FAVORITE_ACTIVITY -> {
                    navigateToScreen(context, SingerFavoriteActivity::class.java)
                }
                PLEDGE_ACTIVITY -> {
                    navigateToScreen(context, PledgeActivity::class.java)
                }
                IDENTIFY_YOURSELF_ACTIVITY -> {
                    navigateToScreen(context, IdentifyYourselfActivity::class.java)
                }
                INTERESTED_IN_ACTIVITY -> {
                    navigateToScreen(context, InterestedInActivity::class.java)
                }
            }
        }

        private fun navigateToScreenWithOnboardingIntent(context: Context, activityName: Class<*>) {
            val intent = Intent(context, activityName).launchModeWithNoBackStack()
            intent.putExtra(ProfileSongSelectionActivity.EXTRA_PROFILE_SONG_ONBOARDING, true)
            context.startActivity(intent)
        }

        private fun navigateToScreen(context: Context, activityName: Class<*>) {

            val intent = Intent(context, activityName).launchModeWithNoBackStack()
            context.startActivity(intent)
        }

        fun navigateToBlankNoNetworkConnectionScreen(context: Context) {

            val intent = Intent(context, BlankNoNetworkConnectionActivity::class.java)
            context.startActivity(intent)
        }

        fun saveScreenInfoToFirebase(context: Context, screenName: String) {
            val firebaseDatabaseUtil = FirebaseDatabaseUtil(context)
            firebaseDatabaseUtil.saveLastScreen(screenName)

            val map = hashMapOf<String, String>()
            map[MP_SCREEN_NAME] = screenName
            MixPanelAnalyticsTrackingService.logEvent(context, MP_SCREEN_VIEWED, map)
        }
    }
}