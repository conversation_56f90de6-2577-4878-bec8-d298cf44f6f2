package com.lovebeats.utils

import com.lovebeats.models.UserObject

class LoveBeatException() : Exception() {

    private var errorCode: Int? = 0
    private var errorCause: String? = ""

    constructor(errorCode: Int? = 0,
                errorCause: String = "") : this() {

        this.errorCode = errorCode
        this.errorCause = errorCause
    }

    override val message: String
        get() = getExceptionMessage()

    private fun getExceptionMessage(): String {

        val userInfoMap = HashMap<String, Any>()

        val userId = UserObject.userFirebaseId

        if (!userId.isNullOrEmpty()) {

            userInfoMap["userFirebaseId"] = userId
        }

        userInfoMap["code"] = errorCode.toString().toInt()
        userInfoMap["errorCause"] = errorCause.toString()

        return userInfoMap.toString()
    }
}