package com.lovebeats.utils

import android.app.Application
import android.content.pm.PackageInfo
import timber.log.Timber
import kotlin.math.min

class ApplicationUtility {

    companion object {

        private fun getPackageInfo(application: Application): PackageInfo {
            return application.packageManager.getPackageInfo(application.packageName, 0)
        }

        fun getAppVersionName(application: Application): String? {
            return getPackageInfo(application).versionName
        }

        fun compareVersionNames(deviceVersionName: String,
                                serverVersionName: String): Int {

            var result = 0

            try {

                val deviceVersionWithoutBuildNum = getAppVersionWithoutBuildNumber(deviceVersionName)
                val serverVersionWithoutBuildNum = getAppVersionWithoutBuildNumber(serverVersionName)

                val deviceVersionNumbers: List<String> = deviceVersionWithoutBuildNum.split(".")
                val serverVersionNumbers: List<String> = serverVersionWithoutBuildNum.split(".")

                val maxIndex = min(deviceVersionNumbers.size, serverVersionNumbers.size)

                for (index in 0 until maxIndex) {

                    val deviceVersionPart = Integer.valueOf(deviceVersionNumbers[index])
                    val serverVersionPart = Integer.valueOf(serverVersionNumbers[index])

                    if (deviceVersionPart < serverVersionPart) {

                        result = -1
                        break
                    } else if (deviceVersionPart > serverVersionPart) {

                        result = 1
                        break
                    }
                }

                // If versions are the same so far, but they have different length...
                if (result == 0 &&
                    deviceVersionNumbers.size != serverVersionNumbers.size) {

                    result = if (deviceVersionNumbers.size > serverVersionNumbers.size) 1 else -1
                }
            } catch (exception: Exception) {

                Timber.e("exception while comparing version names: $exception")
            }
            return result
        }

        private fun getAppVersionWithoutBuildNumber(appVersionName: String): String {

            Timber.d("exception App version: $appVersionName")
            var appVersionWithoutBuildNum = appVersionName

            val indexRemoveBuildNum: Int = appVersionName.indexOf(" ")

            if (indexRemoveBuildNum != -1) {

                appVersionWithoutBuildNum = appVersionName.substring(0, indexRemoveBuildNum)
            }
            return appVersionWithoutBuildNum
        }
    }
}