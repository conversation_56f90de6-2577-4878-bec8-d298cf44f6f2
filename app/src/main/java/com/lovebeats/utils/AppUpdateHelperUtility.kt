package com.lovebeats.utils

import android.app.Activity
import android.app.Application
import android.content.IntentSender
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.model.ActivityResult
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
import timber.log.Timber

class AppUpdateHelperUtility(activity: Activity) {

    companion object {

        const val REQUEST_CODE_IMMEDIATE_UPDATE = 5001
    }

    private val mAppUpdateManager = AppUpdateManagerFactory.create(activity)
    private val mActivity = activity

    fun isImmediateUpdateRequired(forceUpgradeVersion: String?, application: Application): Boolean {

        return !forceUpgradeVersion.isNullOrEmpty() &&
                (ApplicationUtility.getAppVersionName(application)?.let {
                    ApplicationUtility.compareVersionNames(
                        it,
                        forceUpgradeVersion
                    )
                } == -1)
    }

    fun checkIfAppUpdateAvailable(callback: (Boolean) -> Unit) {

        var appUpdateResult = false
        mAppUpdateManager.appUpdateInfo.addOnCompleteListener { appUpdateInfo ->
            if (appUpdateInfo.isSuccessful) {
                appUpdateResult = appUpdateInfo.result.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE
                callback(appUpdateResult)
            } else {
                callback(appUpdateResult)
            }
        }
    }

    fun startImmediateUpdate() {
        mAppUpdateManager.appUpdateInfo.addOnSuccessListener { appUpdateInfo ->
            startImmediateUpdateFlow(appUpdateInfo)
        }
    }

    fun startImmediateUpdateIfInProgress() {
        mAppUpdateManager.appUpdateInfo.addOnSuccessListener { appUpdateInfo ->
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS) {

                startImmediateUpdateFlow(appUpdateInfo)
            }
        }
    }

    private fun startImmediateUpdateFlow(appUpdateInfo: AppUpdateInfo) {
        try {
            mAppUpdateManager.startUpdateFlowForResult(
                    appUpdateInfo,
                    AppUpdateType.IMMEDIATE,
                    mActivity,
                    REQUEST_CODE_IMMEDIATE_UPDATE
            )
        } catch (exception: IntentSender.SendIntentException) {

            Timber.e("Exception when starting the flow's Activity for result in immediate app update : $exception")
        }
    }

    fun handleOnActivityResult(resultCode: Int) {
        when (resultCode) {
            Activity.RESULT_CANCELED, ActivityResult.RESULT_IN_APP_UPDATE_FAILED -> {
                // If the app update failed or user cancelled the update then start immediate update again.
                startImmediateUpdate()
            }
        }
    }
}
