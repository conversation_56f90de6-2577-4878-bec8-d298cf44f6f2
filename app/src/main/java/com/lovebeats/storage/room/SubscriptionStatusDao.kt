package com.lovebeats.storage.room

import androidx.lifecycle.LiveData
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.lovebeats.subscriptions.model.SubscriptionStatus

@Dao
interface SubscriptionStatusDao {

    @Query("SELECT * FROM SubscriptionStatus")
    fun getAll(): LiveData<List<SubscriptionStatus>>

    @Query("SELECT * FROM SubscriptionStatus WHERE purchaseToken = :purchaseToken")
    fun getSubscription(purchaseToken: String): LiveData<SubscriptionStatus>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(subscriptionDataList: List<SubscriptionStatus>)

    @Query("UPDATE SubscriptionStatus SET is_purchase_acknowledged = :isAcknowledged WHERE purchaseToken = :purchaseToken")
    suspend fun updateSubscriptionAcknowledgementData(purchaseToken: String, isAcknowledged: <PERSON>olean)

    @Query("DELETE FROM SubscriptionStatus")
    suspend fun deleteAll()

    @Transaction
    suspend fun replaceAll(subscriptionDataList: List<SubscriptionStatus>) {
        deleteAll()
        insertAll((subscriptionDataList))
    }
}
