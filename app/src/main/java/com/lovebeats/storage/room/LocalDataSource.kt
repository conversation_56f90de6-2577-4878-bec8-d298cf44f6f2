package com.lovebeats.storage.room

import androidx.lifecycle.LiveData
import com.lovebeats.subscriptions.model.SubscriptionStatus

class LocalDataSource private constructor(private val appDatabase: AppDatabase) {

    /**
     * Get the list of subscriptions from the localDataSource and get notified when the data changes.
     */

    fun getAllSubscriptionData(): LiveData<List<SubscriptionStatus>> {
        return appDatabase.subscriptionDataDao().getAll()
    }

    suspend fun updateAllSubscriptions(subscriptionData: List<SubscriptionStatus>) {
        appDatabase.subscriptionDataDao().replaceAll(subscriptionData)
    }

    suspend fun updateSubscription(purchaseToken: String, isAcknowledged: Boolean) {
        appDatabase.subscriptionDataDao().updateSubscriptionAcknowledgementData(purchaseToken, isAcknowledged)
    }

    suspend fun getSubscription(purchaseToken: String): LiveData<SubscriptionStatus> {
        return appDatabase.subscriptionDataDao().getSubscription(purchaseToken)
    }

    /**
     * Delete local user data when the user signs out.
     */
    suspend fun deleteLocalUserData() = updateAllSubscriptions(listOf())

    companion object {

        @Volatile
        private var INSTANCE: LocalDataSource? = null

        fun getInstance(database: AppDatabase): LocalDataSource =
                INSTANCE ?: synchronized(this) {
                    INSTANCE ?: LocalDataSource(database).also { INSTANCE = it }
                }
    }
}