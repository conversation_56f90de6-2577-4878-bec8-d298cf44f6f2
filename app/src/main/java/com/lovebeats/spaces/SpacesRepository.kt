package com.lovebeats.spaces

import android.content.Context
import android.graphics.Bitmap
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.event.ProgressEvent
import com.amazonaws.internal.StaticCredentialsProvider
import com.amazonaws.regions.Region
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.s3.model.DeleteObjectRequest
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest
import com.amazonaws.services.s3.model.ObjectMetadata
import com.amazonaws.services.s3.model.PutObjectRequest
import com.lovebeats.firebase.database.FirebaseDatabaseUtil.Companion.SPACES_ACCESS_KEY
import com.lovebeats.firebase.database.FirebaseDatabaseUtil.Companion.SPACES_BUCKET_NAME
import com.lovebeats.firebase.database.FirebaseDatabaseUtil.Companion.SPACES_DOWNLOAD_ENDPOINT
import com.lovebeats.firebase.database.FirebaseDatabaseUtil.Companion.SPACES_REGION_NAME
import com.lovebeats.firebase.database.FirebaseDatabaseUtil.Companion.SPACES_SECRET_KEY
import com.lovebeats.firebase.database.FirebaseDatabaseUtil.Companion.SPACES_UPLOAD_ENDPOINT
import com.lovebeats.utils.Constants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import me.shouheng.compress.Compress
import me.shouheng.compress.strategy.Strategies
import timber.log.Timber
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.net.URL
import java.util.Date

class SpacesRepository {

    enum class SpacesStatus {
        SUCCESS,
        FAILED,
        COMPLETED
    }

    suspend fun getImageUrl(imagePath: String, callback: (String?, String?) -> Unit) {
        withContext(Dispatchers.IO) {
            getImageMetaData(imagePath) { objectMetadata ->
                callback(getImageUrl(imagePath), objectMetadata?.lastModified.toString())
            }
        }
    }

    suspend fun uploadBitmap(context: Context, uid: String, fileName: String, bitmap: Bitmap, callback: (SpacesStatus) -> Unit) {
        withContext(Dispatchers.IO) {
            val compressor = Compress.with(context, bitmap)
                    .strategy(Strategies.compressor())
                    .setMaxHeight(1500f)
                    .setMaxWidth(1500f)
                    .asBitmap()
            val resultFile = compressor.get()
            val baos = ByteArrayOutputStream()
            if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.R) {
                resultFile?.compress(Bitmap.CompressFormat.WEBP, 75, baos)
            } else {
                resultFile?.compress(Bitmap.CompressFormat.WEBP_LOSSY, 75, baos)
            }
            val data = baos.toByteArray()
            val inputStream: InputStream = ByteArrayInputStream(data)
            try {
                val credentials = StaticCredentialsProvider(BasicAWSCredentials(SPACES_ACCESS_KEY, SPACES_SECRET_KEY))
                val client = AmazonS3Client(credentials, Region.getRegion(SPACES_REGION_NAME))
                client.endpoint = SPACES_UPLOAD_ENDPOINT
                val metadata = ObjectMetadata()
                metadata.contentType = "image/webp"
                /*Out of memory can be caused if you don't specify minimum metadata of Content Length of your input stream */
                val contentLength = data.size.toLong()
                metadata.contentLength = contentLength
                val putObjectRequest = PutObjectRequest(SPACES_BUCKET_NAME, "profile_photos/$uid/$fileName", inputStream, metadata)
                putObjectRequest.setGeneralProgressListener { progressEvent ->
                    if (progressEvent.eventCode == ProgressEvent.COMPLETED_EVENT_CODE) {
                        callback(SpacesStatus.SUCCESS)
                    } else if (progressEvent.eventCode == ProgressEvent.FAILED_EVENT_CODE) {
                        callback(SpacesStatus.FAILED)
                    }
                }
                client.putObject(putObjectRequest)
            } catch (exception: java.lang.Exception) {
                Timber.e("Exception in uploading bitmap: $exception")
                callback(SpacesStatus.FAILED)
            } finally {
                inputStream.close()
            }
        }
    }

    private fun getImageUrl(imagePath: String): String? {
        return try {
            val credentials = StaticCredentialsProvider(BasicAWSCredentials(SPACES_ACCESS_KEY, SPACES_SECRET_KEY))
            val client = AmazonS3Client(credentials, Region.getRegion(SPACES_REGION_NAME))
            client.endpoint = SPACES_DOWNLOAD_ENDPOINT
            val expires = Date(Date().time + 1000 * 600) // 10 minutes
            val generatePresignedUrlRequest = GeneratePresignedUrlRequest(SPACES_BUCKET_NAME, "profile_photos/$imagePath")
            generatePresignedUrlRequest.expiration = expires
            val url: URL = client.generatePresignedUrl(generatePresignedUrlRequest)
            url.toString()
        } catch (exception: Exception) {
            if (imagePath.contains(Constants.photoFileName1) || imagePath.contains(Constants.photoFileName1)) {
                Timber.e("Exception in spaces downloading bitmap url: $exception")
            }
            null
        }
    }

    private suspend fun getImageMetaData(imagePath: String, callback: (ObjectMetadata?) -> Unit) {
        withContext(Dispatchers.IO) {
            try {
                val credentials = StaticCredentialsProvider(BasicAWSCredentials(SPACES_ACCESS_KEY, SPACES_SECRET_KEY))
                val client = AmazonS3Client(credentials, Region.getRegion(SPACES_REGION_NAME))
                client.endpoint = SPACES_DOWNLOAD_ENDPOINT
                val metaData = client.getObjectMetadata(SPACES_BUCKET_NAME, "profile_photos/$imagePath")
                callback(metaData)
            } catch (exception: Exception) {
                if (imagePath.contains(Constants.photoFileName1) || imagePath.contains(Constants.photoFileName1)) {
                    Timber.e("Exception in spaces downloading bitmap url: $exception")
                }
                callback(null)
            }
        }
    }

    suspend fun delete(imagePath: String, callback: (SpacesStatus) -> Unit) {
        withContext(Dispatchers.IO) {
            try {
                val credentials = StaticCredentialsProvider(BasicAWSCredentials(SPACES_ACCESS_KEY, SPACES_SECRET_KEY))
                val client = AmazonS3Client(credentials, Region.getRegion(SPACES_REGION_NAME))
                client.endpoint = SPACES_UPLOAD_ENDPOINT
                val deleteObjectRequest = DeleteObjectRequest(SPACES_BUCKET_NAME, "profile_photos/$imagePath")
                client.deleteObject(deleteObjectRequest)
                callback(SpacesStatus.COMPLETED)
            } catch (exception: Exception) {
                Timber.e("Exception in deleting spaces object: $exception")
                callback(SpacesStatus.COMPLETED)
            }
        }
    }

    companion object {
        fun isSpacesConfigValid(): Boolean {
            return SPACES_UPLOAD_ENDPOINT.isNotEmpty() &&
                    SPACES_DOWNLOAD_ENDPOINT.isNotEmpty() &&
                    SPACES_BUCKET_NAME.isNotEmpty() &&
                    SPACES_REGION_NAME.isNotEmpty() &&
                    SPACES_ACCESS_KEY.isNotEmpty() &&
                    SPACES_SECRET_KEY.isNotEmpty()
        }
    }
}