package com.lovebeats.services

class ServiceResponse {

    lateinit var responseStatus: ResponseStatus

    enum class ResponseStatus {
        Success,
        Error
    }

    companion object {
        fun getServiceResponseOnSuccess(): ServiceResponse {
            val serviceResponse = ServiceResponse()
            serviceResponse.responseStatus = ResponseStatus.Success
            return serviceResponse
        }

        fun getServiceResponseOnError(): ServiceResponse {
            val serviceResponse = ServiceResponse()
            serviceResponse.responseStatus = ResponseStatus.Error
            return serviceResponse
        }
    }

    fun isSuccess(): Boolean {
        return ::responseStatus.isInitialized && ResponseStatus.Success == responseStatus
    }

    fun isError(): Boolean {
        return ::responseStatus.isInitialized && ResponseStatus.Error == responseStatus
    }
}