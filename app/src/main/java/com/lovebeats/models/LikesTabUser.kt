package com.lovebeats.models

import com.google.firebase.database.IgnoreExtraProperties
import java.io.Serializable

@IgnoreExtraProperties
data class LikesTabUser(var uid: Any?,
                        var dob: Any?,
                        var email: Any?,
                        var gender: Any?,
                        var height: Any?,
                        var homeTown: Any?,
                        var iceBreaker1: Any?,
                        var iceBreaker2: Any?,
                        var interestedIn: Any?,
                        var name: Any?,
                        var phone: Any?,
                        var profession: Any?,
                        var distance: Any?,
                        @field:JvmField var isOnboardingComplete: Boolean?,
                        var country: Any? = "",
                        var city: Any?,
                        var school: Any?,
                        var school2: Any?,
                        var zip: Any?,
                        var minMatchAge: Any?,
                        var maxMatchAge: Any?,
                        var minMatchHeight: Any?,
                        var maxMatchHeight: Any?,
                        @field:JvmField var isUserReported: Boolean?,
                        var state: Any?,
                        var favorites: Any? = HashMap<String, HashMap<String, List<String>>>(),
                        var movieGenres: Any? = setOf<String>(),
                        var musicGenres: Any? = setOf<String>(),
                        var profileSong: Any? = HashMap<String, String?>()) : Serializable {

    var likedYouUserFirebaseId: String = ""

    constructor() : this("", "", "", "", "", "", "", "", "", "",
        "", "", "", true, "", "",
        "", "", "", "", "", "", "", false, "",
        HashMap<String, HashMap<String, List<String>>>(),setOf<String>(),setOf<String>(), HashMap<String, String?>())
}