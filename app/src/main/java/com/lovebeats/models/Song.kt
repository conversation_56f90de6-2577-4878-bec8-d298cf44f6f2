package com.lovebeats.models

import com.google.firebase.database.IgnoreExtraProperties

@IgnoreExtraProperties
data class ProfileSongsResponse(
    val tracks: List<Song>?)
data class Song(val id: String?,
                val imageUrl: String? = "",
                val audioUrl: String?,
                val title:String?,
                val subTitle: String? = "",
                val spotifyUrl: String? = "")