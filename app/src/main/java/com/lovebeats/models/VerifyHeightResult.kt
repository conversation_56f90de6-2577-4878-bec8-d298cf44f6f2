package com.lovebeats.models

import java.io.Serializable

data class VerifyHeightResult(val status: String?, val dob: String?, val age: Int?, val height: Double?) : Serializable

data class VerifyHeightConfirmationResult(val status: String?)

enum class HeightVerificationStatus(rawText: String) {
    Success("Success"),
    Error("Error"),
    UserAgeRestricted("UserAgeRestricted"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>("ParsingError"),
    DobMismatch("DobMismatch")
}

