package com.lovebeats.models

import com.google.firebase.database.IgnoreExtraProperties

data class UnlockedLocationsResponse(val status: String?, val unlockedLocations: List<UnlockedLocation>?)

data class UnlockedLocation(val latitude: Double?, val longitude: Double?, val city: String?, val state: String?)

@IgnoreExtraProperties
data class Cities(var latitude: Double?,
                var longitude: Double?,
                var city: String?,
                var state: String?,
                var zip: String?)