package com.lovebeats.models

import com.google.firebase.database.IgnoreExtraProperties

@IgnoreExtraProperties
data class GetNearByUsersResult(
    val status: String?,
    val users: List<NearByUser>?)

@IgnoreExtraProperties
data class NearByUser(
                val uid: String?,
                val dob: String?,
                var externalId: String?,
                val height: Any?,
                val gender: String?,
                val homeTown: String?,
                val iceBreaker1: Any?,
                val iceBreaker2: Any?,
                val name: String?,
                val profession: String?,
                val school: String?,
                val school2: String?,
                val distance: Int?,
                val city: String?,
                val state: String?,
                val zip: String?,
                val country: String?,
                var favorites: Any? = HashMap<String, HashMap<String, List<String>>>(),
                var movieGenres: Any? = setOf<String>(),
                var musicGenres: Any? = setOf<String>(),
                var profileSong: Any? = HashMap<String, String?>())
