package com.lovebeats.models

import com.google.firebase.database.IgnoreExtraProperties
import com.google.firebase.database.PropertyName

@IgnoreExtraProperties
data class User(var dob: Any?,
                var email: Any?,
                var gender: Any?,
                var height: Any?,
                var homeTown: Any?,
                var iceBreaker1: Any?,
                var iceBreaker2: Any?,
                var interestedIn: Any?,
                var name: Any?,
                var phone: Any?,
                var profession: Any?,
                var distance: Any?,
                @field:JvmField var isOnboardingComplete: Boolean?,
                var country: Any? = "",
                var city: Any?,
                var school: Any?,
                var school2: Any?,
                var zip: Any?,
                var minMatchAge: Any?,
                var maxMatchAge: Any?,
                var minMatchHeight: Any?,
                var maxMatchHeight: Any?,
                var connections: Connections?,
                @field:JvmField var isUserReported: Boolean?,
                @set:PropertyName("showNewMatches")
                @get:PropertyName("showNewMatches")
                var showNewMatches: Boolean?,
                @set:PropertyName("showMessages")
                @get:PropertyName("showMessages")
                var showMessages: Boolean?,
                @set:PropertyName("showEverythingElse")
                @get:PropertyName("showEverythingElse")
                var showEverythingElse: Boolean?,
                var state: Any?,
                @set:PropertyName("freeTrailUsed")
                @get:PropertyName("freeTrailUsed")
                var freeTrailUsed: Boolean?,
                @set:PropertyName("rsDel")
                @get:PropertyName("rsDel")
                var rsDel: Boolean?,
                var rTitle: Any? = "",
                var rMsg: Any? = "",
                var externalId: Any? = "",
                var favorites: Any? = HashMap<String, HashMap<String, List<String>>>(),
                var movieGenres: Any? = setOf<String>(),
                var musicGenres: Any? = setOf<String>(),
                var profileSong: Any? = HashMap<String, String?>()) {

    constructor() : this("", "", "", "", "", "", "", "",
            "", "", "", "", false, "",
            "", "", "", "", "", "", "", "", Connections(), false, true,
            true, true, "", false, false, "", "", "", HashMap<String, HashMap<String,
                List<String>>>(), setOf<String>(), setOf<String>(), HashMap<String, String?>())
}

