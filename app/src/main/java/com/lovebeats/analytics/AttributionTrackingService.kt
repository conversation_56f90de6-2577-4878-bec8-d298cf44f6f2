package com.lovebeats.analytics

import android.content.Context
import android.os.Bundle
import com.lovebeats.storage.prefs.AccountPreferences
import timber.log.Timber

/**
 * Centralized service for managing attribution tracking across Firebase and Mixpanel
 */
class AttributionTrackingService {
    
    companion object {
        private const val ATTRIBUTION_SOURCE_KEY = "attribution_source"
        private const val ATTRIBUTION_DETECTED_KEY = "attribution_detected"
        private const val ATTRIBUTION_TIMESTAMP_KEY = "attribution_timestamp"
        
        /**
         * Store the detected attribution source in SharedPreferences
         */
        fun storeAttributionSource(context: Context, source: String) {
            try {
                val prefs = AccountPreferences.getInstance(context)
                prefs.setValue(ATTRIBUTION_SOURCE_KEY, source)
                prefs.setValue(ATTRIBUTION_DETECTED_KEY, true)
                prefs.setValue(ATTRIBUTION_TIMESTAMP_KEY, System.currentTimeMillis())
                
                Timber.d("Attribution source stored: $source")
                
                // Log app install event with attribution
                logAppInstallEvent(context, source)
            } catch (e: Exception) {
                Timber.e(e, "Failed to store attribution source")
            }
        }
        
        /**
         * Get the stored attribution source, fallback to organic if not found
         */
        fun getAttributionSource(context: Context): String {
            return try {
                val prefs = AccountPreferences.getInstance(context)
                prefs.getStringValue(ATTRIBUTION_SOURCE_KEY, ORGANIC) ?: ORGANIC
            } catch (e: Exception) {
                Timber.e(e, "Failed to get attribution source")
                ORGANIC
            }
        }
        
        /**
         * Check if attribution has been detected and stored
         */
        fun isAttributionDetected(context: Context): Boolean {
            return try {
                val prefs = AccountPreferences.getInstance(context)
                prefs.getBooleanValue(ATTRIBUTION_DETECTED_KEY, false) ?: false
            } catch (e: Exception) {
                Timber.e(e, "Failed to check attribution detection status")
                false
            }
        }
        
        /**
         * Log app install event with attribution to both Firebase and Mixpanel
         */
        private fun logAppInstallEvent(context: Context, source: String) {
            try {
                // Firebase Analytics
                val firebaseBundle = Bundle().apply {
                    putString(AD_SOURCE, source)
                    putString(STATUS, ANALYTICS_SUCCESS)
                }
                AnalyticsTrackingService.logEvent(context, APP_INSTALL, firebaseBundle)
                
                // Mixpanel Analytics
                val mixpanelMap = hashMapOf<String, String>().apply {
                    put(AD_SOURCE, source)
                    put(STATUS, ANALYTICS_SUCCESS)
                }
                MixPanelAnalyticsTrackingService.logEvent(context, APP_INSTALL, mixpanelMap)
                
                Timber.d("App install event logged with source: $source")
            } catch (e: Exception) {
                Timber.e(e, "Failed to log app install event")
            }
        }
        
        /**
         * Log event with attribution to both Firebase and Mixpanel
         */
        fun logEventWithAttribution(context: Context, eventName: String, additionalParams: Map<String, String> = emptyMap()) {
            try {
                val attributionSource = getAttributionSource(context)
                
                // Firebase Analytics
                val firebaseBundle = Bundle().apply {
                    putString(AD_SOURCE, attributionSource)
                    additionalParams.forEach { (key, value) ->
                        putString(key, value)
                    }
                }
                AnalyticsTrackingService.logEvent(context, eventName, firebaseBundle)
                
                // Mixpanel Analytics
                val mixpanelMap = hashMapOf<String, String>().apply {
                    put(AD_SOURCE, attributionSource)
                    putAll(additionalParams)
                }
                MixPanelAnalyticsTrackingService.logEvent(context, eventName, mixpanelMap)
                
                Timber.d("Event '$eventName' logged with attribution: $attributionSource")
            } catch (e: Exception) {
                Timber.e(e, "Failed to log event with attribution: $eventName")
            }
        }
        
        /**
         * Log onboarding completion with attribution
         */
        fun logOnboardingComplete(context: Context) {
            logEventWithAttribution(context, ONBOARDING_COMPLETE)
        }
        
        /**
         * Log subscription success with attribution
         */
        fun logSubscriptionSuccess(context: Context, sku: String, price: String? = null) {
            val params = mutableMapOf<String, String>().apply {
                put(SKU, sku)
                put(STATUS, ANALYTICS_SUCCESS)
                price?.let { put(PRICE, it) }
            }
            logEventWithAttribution(context, SUBSCRIPTION_SUCCESS, params)
        }
        
        /**
         * Log subscription failure with attribution
         */
        fun logSubscriptionFailure(context: Context, sku: String, failureReason: String, responseCode: String? = null) {
            val params = mutableMapOf<String, String>().apply {
                put(SKU, sku)
                put(STATUS, ANALYTICS_FAILURE)
                put(FAILURE_REASON, failureReason)
                responseCode?.let { put(RESPONSE_CODE, it) }
            }
            logEventWithAttribution(context, SUBSCRIPTION_FAILED, params)
        }
        
        /**
         * Get attribution timestamp for debugging
         */
        fun getAttributionTimestamp(context: Context): Long {
            return try {
                val prefs = AccountPreferences.getInstance(context)
                prefs.getDoubleValue(ATTRIBUTION_TIMESTAMP_KEY, 0.0)?.toLong() ?: 0L
            } catch (e: Exception) {
                Timber.e(e, "Failed to get attribution timestamp")
                0L
            }
        }
        
        /**
         * Reset attribution data (for testing purposes)
         */
        fun resetAttributionData(context: Context) {
            try {
                val prefs = AccountPreferences.getInstance(context)
                prefs.setValue(ATTRIBUTION_SOURCE_KEY, "")
                prefs.setValue(ATTRIBUTION_DETECTED_KEY, false)
                prefs.setValue(ATTRIBUTION_TIMESTAMP_KEY, 0L)
                Timber.d("Attribution data reset")
            } catch (e: Exception) {
                Timber.e(e, "Failed to reset attribution data")
            }
        }
    }
}
