package com.lovebeats.analytics

import android.content.Context
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.android.installreferrer.api.ReferrerDetails
import timber.log.Timber

/**
 * Handles Google Play Install Referrer API for Google Ads attribution
 */
class InstallReferrerHandler {
    
    companion object {
        private val GOOGLE_ADS_REFERRER_PATTERNS = arrayOf(
            "utm_source=google",
            "utm_medium=cpc",
            "gclid=",
            "utm_campaign="
        )
        
        /**
         * Initialize and retrieve install referrer information
         */
        fun retrieveInstallReferrer(context: Context, callback: (String) -> Unit) {
            val referrerClient = InstallReferrerClient.newBuilder(context).build()
            
            referrerClient.startConnection(object : InstallReferrerStateListener {
                override fun onInstallReferrerSetupFinished(responseCode: Int) {
                    when (responseCode) {
                        InstallReferrerClient.InstallReferrerResponse.OK -> {
                            try {
                                val response: ReferrerDetails = referrerClient.installReferrer
                                val referrerUrl = response.installReferrer
                                val referrerClickTime = response.referrerClickTimestampSeconds
                                val appInstallTime = response.installBeginTimestampSeconds
                                val instantExperienceLaunched = response.googlePlayInstantParam
                                
                                Timber.d("Install Referrer URL: $referrerUrl")
                                Timber.d("Referrer Click Time: $referrerClickTime")
                                Timber.d("App Install Time: $appInstallTime")
                                Timber.d("Instant Experience Launched: $instantExperienceLaunched")
                                
                                val attributionSource = determineAttributionSource(referrerUrl)
                                callback(attributionSource)
                                
                            } catch (e: Exception) {
                                Timber.e(e, "Failed to get install referrer details")
                                callback(ORGANIC)
                            } finally {
                                referrerClient.endConnection()
                            }
                        }
                        InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED -> {
                            Timber.w("Install Referrer API not supported")
                            callback(ORGANIC)
                            referrerClient.endConnection()
                        }
                        InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE -> {
                            Timber.w("Install Referrer service unavailable")
                            callback(ORGANIC)
                            referrerClient.endConnection()
                        }
                        else -> {
                            Timber.w("Install Referrer setup failed with code: $responseCode")
                            callback(ORGANIC)
                            referrerClient.endConnection()
                        }
                    }
                }
                
                override fun onInstallReferrerServiceDisconnected() {
                    Timber.w("Install Referrer service disconnected")
                    callback(ORGANIC)
                }
            })
        }
        
        /**
         * Determine attribution source based on referrer URL
         */
        private fun determineAttributionSource(referrerUrl: String?): String {
            if (referrerUrl.isNullOrBlank()) {
                Timber.d("Empty referrer URL, defaulting to organic")
                return ORGANIC
            }
            
            Timber.d("Analyzing referrer URL: $referrerUrl")
            
            // Check for Google Ads patterns
            val isGoogleAds = GOOGLE_ADS_REFERRER_PATTERNS.any { pattern ->
                referrerUrl.contains(pattern, ignoreCase = true)
            }
            
            return if (isGoogleAds) {
                Timber.d("Google Ads attribution detected")
                GOOGLE_ADS
            } else {
                Timber.d("No known attribution patterns found, defaulting to organic")
                ORGANIC
            }
        }
        
        /**
         * Initialize install referrer tracking with retry logic
         */
        fun initializeWithRetry(context: Context, maxRetries: Int = 3, retryDelayMs: Long = 2000) {
            var attempts = 0
            
            fun attemptRetrieval() {
                attempts++
                Timber.d("Install referrer retrieval attempt $attempts/$maxRetries")
                
                retrieveInstallReferrer(context) { attributionSource ->
                    if (attributionSource != ORGANIC || attempts >= maxRetries) {
                        // Store the attribution source
                        AttributionTrackingService.storeAttributionSource(context, attributionSource)
                        Timber.d("Install referrer tracking completed with source: $attributionSource")
                    } else {
                        // Retry after delay if organic and retries remaining
                        Timber.d("Retrying install referrer retrieval in ${retryDelayMs}ms")
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                            attemptRetrieval()
                        }, retryDelayMs)
                    }
                }
            }
            
            attemptRetrieval()
        }
        
        /**
         * Parse UTM parameters from referrer URL
         */
        fun parseUtmParameters(referrerUrl: String?): Map<String, String> {
            val utmParams = mutableMapOf<String, String>()
            
            if (referrerUrl.isNullOrBlank()) return utmParams
            
            try {
                val params = referrerUrl.split("&")
                for (param in params) {
                    val keyValue = param.split("=")
                    if (keyValue.size == 2) {
                        val key = keyValue[0].trim()
                        val value = keyValue[1].trim()
                        
                        when (key) {
                            "utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content", "gclid" -> {
                                utmParams[key] = value
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to parse UTM parameters from: $referrerUrl")
            }
            
            return utmParams
        }
    }
}
