package com.lovebeats.analytics

import android.content.Context
import android.net.Uri
import com.facebook.applinks.AppLinkData
import com.facebook.FacebookSdk
import timber.log.Timber

/**
 * Handles Meta Ads (Facebook/Instagram) attribution through deferred deep links
 */
class MetaAdsHandler {
    
    companion object {
        private val META_ADS_REFERRER_PATTERNS = arrayOf(
            "fb_source=",
            "fb_ref=",
            "fbclid=",
            "utm_source=facebook",
            "utm_source=instagram",
            "utm_medium=social"
        )
        
        /**
         * Initialize Meta Ads attribution tracking
         */
        fun initialize(context: Context) {
            try {
                // Ensure Facebook SDK is initialized
                if (!FacebookSdk.isInitialized()) {
                    FacebookSdk.sdkInitialize(context)
                }
                
                // Fetch deferred app link data
                fetchDeferredAppLinkData(context) { attributionSource ->
                    if (attributionSource != ORGANIC) {
                        AttributionTrackingService.storeAttributionSource(context, attributionSource)
                        Timber.d("Meta Ads attribution detected: $attributionSource")
                    } else {
                        Timber.d("No Meta Ads attribution found")
                    }
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Failed to initialize Meta Ads handler")
            }
        }
        
        /**
         * Fetch deferred app link data for attribution
         */
        private fun fetchDeferredAppLinkData(context: Context, callback: (String) -> Unit) {
            try {
                AppLinkData.fetchDeferredAppLinkData(context) { appLinkData ->
                    if (appLinkData != null) {
                        val targetUri = appLinkData.targetUri
                        val promotionCode = appLinkData.promotionCode
                        val ref = appLinkData.ref
                        
                        Timber.d("Deferred App Link Data:")
                        Timber.d("Target URI: $targetUri")
                        Timber.d("Promotion Code: $promotionCode")
                        Timber.d("Ref: $ref")
                        
                        val attributionSource = determineAttributionSource(targetUri, promotionCode, ref)
                        callback(attributionSource)
                        
                    } else {
                        Timber.d("No deferred app link data found")
                        callback(ORGANIC)
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to fetch deferred app link data")
                callback(ORGANIC)
            }
        }
        
        /**
         * Determine attribution source from app link data
         */
        private fun determineAttributionSource(targetUri: Uri?, promotionCode: String?, ref: String?): String {
            // Check promotion code first
            if (!promotionCode.isNullOrBlank()) {
                Timber.d("Meta Ads attribution detected via promotion code: $promotionCode")
                return META_ADS
            }
            
            // Check ref parameter
            if (!ref.isNullOrBlank()) {
                Timber.d("Meta Ads attribution detected via ref: $ref")
                return META_ADS
            }
            
            // Check target URI for Meta Ads patterns
            if (targetUri != null) {
                val uriString = targetUri.toString()
                Timber.d("Analyzing target URI: $uriString")
                
                val isMetaAds = META_ADS_REFERRER_PATTERNS.any { pattern ->
                    uriString.contains(pattern, ignoreCase = true)
                }
                
                if (isMetaAds) {
                    Timber.d("Meta Ads attribution detected via URI patterns")
                    return META_ADS
                }
                
                // Check UTM parameters in URI
                val utmSource = targetUri.getQueryParameter("utm_source")
                val utmMedium = targetUri.getQueryParameter("utm_medium")
                
                if (utmSource != null && (utmSource.equals("facebook", true) || utmSource.equals("instagram", true))) {
                    Timber.d("Meta Ads attribution detected via utm_source: $utmSource")
                    return META_ADS
                }
                
                if (utmMedium != null && utmMedium.equals("social", true)) {
                    Timber.d("Meta Ads attribution detected via utm_medium: $utmMedium")
                    return META_ADS
                }
            }
            
            Timber.d("No Meta Ads attribution patterns found")
            return ORGANIC
        }
        
        /**
         * Initialize with retry logic for better attribution detection
         */
        fun initializeWithRetry(context: Context, maxRetries: Int = 3, retryDelayMs: Long = 3000) {
            var attempts = 0
            
            fun attemptInitialization() {
                attempts++
                Timber.d("Meta Ads attribution attempt $attempts/$maxRetries")
                
                fetchDeferredAppLinkData(context) { attributionSource ->
                    if (attributionSource != ORGANIC || attempts >= maxRetries) {
                        if (attributionSource != ORGANIC) {
                            AttributionTrackingService.storeAttributionSource(context, attributionSource)
                        }
                        Timber.d("Meta Ads tracking completed with source: $attributionSource")
                    } else {
                        // Retry after delay if organic and retries remaining
                        Timber.d("Retrying Meta Ads attribution in ${retryDelayMs}ms")
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                            attemptInitialization()
                        }, retryDelayMs)
                    }
                }
            }
            
            attemptInitialization()
        }
        
        /**
         * Handle incoming app links for real-time attribution
         */
        fun handleAppLink(context: Context, uri: Uri?) {
            if (uri == null) return
            
            try {
                Timber.d("Handling app link: $uri")
                
                val attributionSource = determineAttributionSource(uri, null, null)
                if (attributionSource != ORGANIC) {
                    // Only update if we haven't already detected attribution
                    if (!AttributionTrackingService.isAttributionDetected(context)) {
                        AttributionTrackingService.storeAttributionSource(context, attributionSource)
                        Timber.d("Real-time Meta Ads attribution detected: $attributionSource")
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to handle app link for attribution")
            }
        }
        
        /**
         * Parse Facebook-specific parameters from URI
         */
        fun parseFacebookParameters(uri: Uri?): Map<String, String> {
            val fbParams = mutableMapOf<String, String>()
            
            if (uri == null) return fbParams
            
            try {
                // Facebook-specific parameters
                val fbSource = uri.getQueryParameter("fb_source")
                val fbRef = uri.getQueryParameter("fb_ref")
                val fbclid = uri.getQueryParameter("fbclid")
                
                fbSource?.let { fbParams["fb_source"] = it }
                fbRef?.let { fbParams["fb_ref"] = it }
                fbclid?.let { fbParams["fbclid"] = it }
                
                // Standard UTM parameters
                val utmSource = uri.getQueryParameter("utm_source")
                val utmMedium = uri.getQueryParameter("utm_medium")
                val utmCampaign = uri.getQueryParameter("utm_campaign")
                
                utmSource?.let { fbParams["utm_source"] = it }
                utmMedium?.let { fbParams["utm_medium"] = it }
                utmCampaign?.let { fbParams["utm_campaign"] = it }
                
            } catch (e: Exception) {
                Timber.e(e, "Failed to parse Facebook parameters from: $uri")
            }
            
            return fbParams
        }
    }
}
