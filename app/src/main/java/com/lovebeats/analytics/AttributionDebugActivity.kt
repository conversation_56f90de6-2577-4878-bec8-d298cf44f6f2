package com.lovebeats.analytics

import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.lovebeats.R
import timber.log.Timber

/**
 * Debug activity for testing attribution tracking
 * Only use in debug builds for QA testing
 */
class AttributionDebugActivity : AppCompatActivity() {
    
    private lateinit var debugInfoText: TextView
    private lateinit var simulateGoogleAdsButton: Button
    private lateinit var simulateMetaAdsButton: Button
    private lateinit var simulateOrganicButton: Button
    private lateinit var testOnboardingButton: Button
    private lateinit var testSubscriptionSuccessButton: Button
    private lateinit var testSubscriptionFailureButton: Button
    private lateinit var resetAttributionButton: Button
    private lateinit var validateFlowButton: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_attribution_debug)
        
        initViews()
        setupClickListeners()
        updateDebugInfo()
    }
    
    private fun initViews() {
        debugInfoText = findViewById(R.id.debug_info_text)
        simulateGoogleAdsButton = findViewById(R.id.simulate_google_ads_button)
        simulateMetaAdsButton = findViewById(R.id.simulate_meta_ads_button)
        simulateOrganicButton = findViewById(R.id.simulate_organic_button)
        testOnboardingButton = findViewById(R.id.test_onboarding_button)
        testSubscriptionSuccessButton = findViewById(R.id.test_subscription_success_button)
        testSubscriptionFailureButton = findViewById(R.id.test_subscription_failure_button)
        resetAttributionButton = findViewById(R.id.reset_attribution_button)
        validateFlowButton = findViewById(R.id.validate_flow_button)
    }
    
    private fun setupClickListeners() {
        simulateGoogleAdsButton.setOnClickListener {
            AttributionTestingHelper.simulateAttribution(this, GOOGLE_ADS)
            updateDebugInfo()
        }
        
        simulateMetaAdsButton.setOnClickListener {
            AttributionTestingHelper.simulateAttribution(this, META_ADS)
            updateDebugInfo()
        }
        
        simulateOrganicButton.setOnClickListener {
            AttributionTestingHelper.simulateAttribution(this, ORGANIC)
            updateDebugInfo()
        }
        
        testOnboardingButton.setOnClickListener {
            AttributionTestingHelper.testOnboardingComplete(this)
        }
        
        testSubscriptionSuccessButton.setOnClickListener {
            AttributionTestingHelper.testSubscriptionSuccess(this)
        }
        
        testSubscriptionFailureButton.setOnClickListener {
            AttributionTestingHelper.testSubscriptionFailure(this)
        }
        
        resetAttributionButton.setOnClickListener {
            AttributionTestingHelper.resetAttributionForTesting(this)
            updateDebugInfo()
        }
        
        validateFlowButton.setOnClickListener {
            AttributionTestingHelper.logValidationResults(this)
        }
    }
    
    private fun updateDebugInfo() {
        val debugInfo = AttributionTestingHelper.getAttributionDebugInfo(this)
        debugInfoText.text = debugInfo
        Timber.d("Attribution Debug Info:\n$debugInfo")
    }
}
