package com.lovebeats.analytics

import android.content.Context
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*

/**
 * Helper class for testing and debugging attribution tracking
 * Only use in debug builds or for QA testing
 */
class AttributionTestingHelper {
    
    companion object {
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        
        /**
         * Get comprehensive attribution debug information
         */
        fun getAttributionDebugInfo(context: Context): String {
            val sb = StringBuilder()
            
            try {
                sb.appendLine("=== ATTRIBUTION DEBUG INFO ===")
                sb.appendLine("Timestamp: ${dateFormat.format(Date())}")
                sb.appendLine()
                
                // Current attribution state
                val isDetected = AttributionTrackingService.isAttributionDetected(context)
                val source = AttributionTrackingService.getAttributionSource(context)
                val timestamp = AttributionTrackingService.getAttributionTimestamp(context)
                
                sb.appendLine("Attribution Detected: $isDetected")
                sb.appendLine("Attribution Source: $source")
                sb.appendLine("Attribution Timestamp: ${if (timestamp > 0) dateFormat.format(Date(timestamp)) else "Not set"}")
                sb.appendLine()
                
                // Attribution sources info
                sb.appendLine("Available Attribution Sources:")
                sb.appendLine("- $GOOGLE_ADS (Google Ads/UAC)")
                sb.appendLine("- $META_ADS (Facebook/Instagram)")
                sb.appendLine("- $ORGANIC (Default/Unknown)")
                sb.appendLine()
                
                // Events info
                sb.appendLine("Attribution Events:")
                sb.appendLine("- $APP_INSTALL (logged when attribution detected)")
                sb.appendLine("- $ONBOARDING_COMPLETE (logged with attribution)")
                sb.appendLine("- $SUBSCRIPTION_SUCCESS (logged with attribution)")
                sb.appendLine("- $SUBSCRIPTION_FAILED (logged with attribution)")
                sb.appendLine()
                
            } catch (e: Exception) {
                sb.appendLine("Error getting debug info: ${e.message}")
                Timber.e(e, "Failed to get attribution debug info")
            }
            
            return sb.toString()
        }
        
        /**
         * Simulate attribution for testing purposes
         */
        fun simulateAttribution(context: Context, source: String): Boolean {
            return try {
                when (source) {
                    GOOGLE_ADS, META_ADS, ORGANIC -> {
                        AttributionTrackingService.storeAttributionSource(context, source)
                        Timber.d("Simulated attribution: $source")
                        true
                    }
                    else -> {
                        Timber.w("Invalid attribution source for simulation: $source")
                        false
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to simulate attribution")
                false
            }
        }
        
        /**
         * Test onboarding completion with current attribution
         */
        fun testOnboardingComplete(context: Context) {
            try {
                val source = AttributionTrackingService.getAttributionSource(context)
                Timber.d("Testing onboarding completion with attribution: $source")
                AttributionTrackingService.logOnboardingComplete(context)
            } catch (e: Exception) {
                Timber.e(e, "Failed to test onboarding completion")
            }
        }
        
        /**
         * Test subscription success with current attribution
         */
        fun testSubscriptionSuccess(context: Context, sku: String = "test_sku", price: String? = "9.99") {
            try {
                val source = AttributionTrackingService.getAttributionSource(context)
                Timber.d("Testing subscription success with attribution: $source")
                AttributionTrackingService.logSubscriptionSuccess(context, sku, price)
            } catch (e: Exception) {
                Timber.e(e, "Failed to test subscription success")
            }
        }
        
        /**
         * Test subscription failure with current attribution
         */
        fun testSubscriptionFailure(context: Context, sku: String = "test_sku", reason: String = "test_failure") {
            try {
                val source = AttributionTrackingService.getAttributionSource(context)
                Timber.d("Testing subscription failure with attribution: $source")
                AttributionTrackingService.logSubscriptionFailure(context, sku, reason, "TEST_CODE")
            } catch (e: Exception) {
                Timber.e(e, "Failed to test subscription failure")
            }
        }
        
        /**
         * Reset attribution for testing
         */
        fun resetAttributionForTesting(context: Context) {
            try {
                AttributionTrackingService.resetAttributionData(context)
                Timber.d("Attribution data reset for testing")
            } catch (e: Exception) {
                Timber.e(e, "Failed to reset attribution data")
            }
        }
        
        /**
         * Validate attribution flow end-to-end
         */
        fun validateAttributionFlow(context: Context): Map<String, Boolean> {
            val results = mutableMapOf<String, Boolean>()
            
            try {
                // Test 1: Can store and retrieve attribution
                val testSource = GOOGLE_ADS
                AttributionTrackingService.storeAttributionSource(context, testSource)
                val retrievedSource = AttributionTrackingService.getAttributionSource(context)
                results["store_retrieve"] = (retrievedSource == testSource)
                
                // Test 2: Attribution detection flag works
                val isDetected = AttributionTrackingService.isAttributionDetected(context)
                results["detection_flag"] = isDetected
                
                // Test 3: Timestamp is set
                val timestamp = AttributionTrackingService.getAttributionTimestamp(context)
                results["timestamp_set"] = (timestamp > 0)
                
                // Test 4: Can log events with attribution
                try {
                    AttributionTrackingService.logEventWithAttribution(context, "test_event")
                    results["event_logging"] = true
                } catch (e: Exception) {
                    results["event_logging"] = false
                }
                
                // Reset after testing
                AttributionTrackingService.resetAttributionData(context)
                
            } catch (e: Exception) {
                Timber.e(e, "Failed to validate attribution flow")
                results["validation_error"] = false
            }
            
            return results
        }
        
        /**
         * Log attribution validation results
         */
        fun logValidationResults(context: Context) {
            val results = validateAttributionFlow(context)
            val sb = StringBuilder()
            
            sb.appendLine("=== ATTRIBUTION VALIDATION RESULTS ===")
            results.forEach { (test, passed) ->
                val status = if (passed) "✓ PASS" else "✗ FAIL"
                sb.appendLine("$test: $status")
            }
            
            val allPassed = results.values.all { it }
            sb.appendLine()
            sb.appendLine("Overall Status: ${if (allPassed) "✓ ALL TESTS PASSED" else "✗ SOME TESTS FAILED"}")
            
            Timber.d(sb.toString())
        }
    }
}
