package com.lovebeats.deepLinking

import android.app.Activity
import android.content.Intent
import android.net.Uri
import com.google.firebase.dynamiclinks.ktx.dynamicLinks
import com.google.firebase.ktx.Firebase
import timber.log.Timber

class InviteLinking {

    fun getAndSetDynamicLinkData(intent: Intent?, activity: Activity?) {

        if (intent != null &&
                activity != null) {

            Firebase.dynamicLinks
                    .getDynamicLink(intent)
                    .addOnSuccessListener(activity) { pendingDynamicLinkData ->

                        var deepLink: Uri? = null

                        if (pendingDynamicLinkData != null) {

                            deepLink = pendingDynamicLinkData.link
                        }

                        Timber.d("Testing dynamic link: ${deepLink.toString()}")

                        // Handle the deep link. For example, open the linked
                        // content, or apply promotional credit to the user's
                        // account.
                    }
                    .addOnFailureListener(activity) { exception ->

                        Timber.e("Error in dynamic links $exception")
                    }
        }
    }


}
