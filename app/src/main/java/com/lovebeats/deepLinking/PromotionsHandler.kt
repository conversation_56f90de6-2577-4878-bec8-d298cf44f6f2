package com.lovebeats.deepLinking

import android.content.Context
import com.lovebeats.storage.prefs.AccountPreferences
import com.lovebeats.utils.Constants

object PromotionsHandler {

    fun handleInfluencerSignups(context: Context, influencerId: String?, influencerName: String?) {
        AccountPreferences.getInstance(context).setValue(Constants.influencerId, influencerId)
        AccountPreferences.getInstance(context).setValue(Constants.influencerName, influencerName)
    }

    fun resetInfluencerSignups(context: Context) {
        AccountPreferences.getInstance(context).setValue(Constants.influencerId, "")
        AccountPreferences.getInstance(context).setValue(Constants.influencerName, "")
    }

    fun handleBranchCampaignSignups(context: Context, source: String?, influencerId: String?, influencerName: String?) {
        AccountPreferences.getInstance(context).setValue(Constants.source, source)
        handleInfluencerSignups(context, influencerId, influencerName)
    }
}