<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="228dp"
    android:height="188dp"
    android:viewportWidth="228"
    android:viewportHeight="188">
  <path
      android:pathData="M117.873,179.436C103.598,179.435 92.027,167.947 92.027,153.777V151.404H143.718V153.777C143.719,160.582 140.996,167.108 136.149,171.92C131.302,176.733 124.728,179.436 117.873,179.436Z"
      android:fillColor="@color/color_tertiary"/>
  <path
      android:pathData="M137.77,137.82C147.596,137.82 155.562,129.912 155.562,120.158C155.562,110.403 147.596,102.496 137.77,102.496C127.944,102.496 119.978,110.403 119.978,120.158C119.978,129.912 127.944,137.82 137.77,137.82Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M152.059,115.727C152.752,117.908 152.924,120.22 152.562,122.479"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M151.721,125.547C150.074,129.761 146.58,132.999 142.232,134.341"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.641,151.478L122.228,157.458C121.187,159.278 118.449,159.603 116.923,158.085L110.28,151.478L104.793,125.497H120.16L125.641,151.478Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M122.493,136.555L108.464,142.864L105.68,129.681L121.019,129.581L122.493,136.555Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M125.641,151.478L122.228,157.458C121.187,159.278 118.449,159.603 116.923,158.085L110.28,151.478L104.793,125.497H120.16L125.641,151.478Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M85.457,151.171C94.504,151.171 101.838,143.89 101.838,134.909C101.838,125.928 94.504,118.647 85.457,118.647C76.41,118.647 69.076,125.928 69.076,134.909C69.076,143.89 76.41,151.171 85.457,151.171Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M89.871,130.558C91.884,130.558 93.516,128.938 93.516,126.939C93.516,124.941 91.884,123.321 89.871,123.321C87.857,123.321 86.226,124.941 86.226,126.939C86.226,128.938 87.857,130.558 89.871,130.558Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M111.179,137.695C124.002,137.695 134.397,127.376 134.397,114.646C134.397,101.917 124.002,91.598 111.179,91.598C98.356,91.598 87.961,101.917 87.961,114.646C87.961,127.376 98.356,137.695 111.179,137.695Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M90.865,120.942C87.157,109.15 93.475,96.553 105.189,92.382C104.879,92.465 104.566,92.55 104.257,92.645C92.016,96.44 85.192,109.366 89.014,121.517C92.488,132.555 103.148,139.317 114.323,137.589C115.449,137.414 115.794,137.089 114.323,137.187C103.851,137.887 94.13,131.317 90.865,120.942Z"
      android:fillColor="#FFF4EE"/>
  <path
      android:pathData="M134.856,116.21C136.869,116.21 138.501,114.59 138.501,112.591C138.501,110.593 136.869,108.973 134.856,108.973C132.843,108.973 131.211,110.593 131.211,112.591C131.211,114.59 132.843,116.21 134.856,116.21Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M116.22,112.012L116.774,113.83C117.625,116.191 119.449,118.356 121.51,119.807L123.328,121.085L122.774,121.99C121.57,123.96 119.389,125.133 117.068,125.058L116.358,125.033"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.168,126.934C88.814,126.411 89.766,126.508 90.294,127.149"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M135.284,113.104C135.232,112.709 135.341,112.309 135.585,111.994C135.83,111.678 136.191,111.472 136.589,111.421"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.401,108.172C113.497,109.3 112.001,109.783 110.602,109.397"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.643,105.851C118.001,105.341 119.534,105.688 120.535,106.731"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M110.894,130.673C109.443,130.661 108.14,129.788 107.584,128.458"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M94.651,121.251C99.219,119.124 102.943,115.546 105.231,111.083"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34747"
      android:fillColor="#00000000"
      android:strokeColor="#B08155"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M70.502,149.113C75.421,150.241 80.579,149.707 85.158,147.595"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34747"
      android:fillColor="#00000000"
      android:strokeColor="#B88352"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M75.86,152.184C80.877,151.619 85.561,149.407 89.168,145.9"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34747"
      android:fillColor="#00000000"
      android:strokeColor="#B88352"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M119.602,80.814C115.348,83.515 112.124,87.547 110.441,92.272"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34747"
      android:fillColor="#00000000"
      android:strokeColor="#C4905F"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.247,81.97C111.433,86.129 110.097,91.101 110.451,96.099"
      android:strokeLineJoin="round"
      android:strokeWidth="1.34747"
      android:fillColor="#00000000"
      android:strokeColor="#B88352"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M130.191,153.894C130.19,153.033 130.01,152.181 129.662,151.393L118.18,151.113L106.285,151.395C105.939,152.185 105.76,153.036 105.759,153.896C105.759,158.622 111.227,162.453 117.973,162.453C124.72,162.453 130.191,158.615 130.191,153.894Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M134.4,115.076C134.229,123.857 129.054,131.781 121.049,135.519"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.059,124.234C86.463,116.429 87.533,107.286 92.837,100.507C98.141,93.728 106.799,90.436 115.308,91.963C123.816,93.49 130.765,99.584 133.342,107.777C133.531,108.383 133.697,108.993 133.846,109.603"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114.963,137.39C106.168,138.829 97.319,135.148 92.185,127.915"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.591,131.711C91.262,131.711 91.806,131.171 91.806,130.505C91.806,129.84 91.262,129.3 90.591,129.3C89.921,129.3 89.377,129.84 89.377,130.505C89.377,131.171 89.921,131.711 90.591,131.711Z"
      android:fillColor="#FFF4EE"/>
  <path
      android:pathData="M137.146,116.176C137.816,116.176 138.36,115.636 138.36,114.971C138.36,114.305 137.816,113.766 137.146,113.766C136.475,113.766 135.932,114.305 135.932,114.971C135.932,115.636 136.475,116.176 137.146,116.176Z"
      android:fillColor="#FFF4EE"/>
  <path
      android:pathData="M112.32,153.157H117.283"
      android:strokeLineJoin="round"
      android:strokeWidth="0.60155"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.807,153.157H124.233"
      android:strokeLineJoin="round"
      android:strokeWidth="0.60155"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M120.298,154.309C120.298,154.889 119.824,155.359 119.24,155.359C118.656,155.359 118.182,154.889 118.182,154.309"
      android:strokeLineJoin="round"
      android:strokeWidth="0.60155"
      android:fillColor="#00000000"
      android:strokeColor="#F0917A"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M72.222,139.01C70.764,134.385 71.865,129.34 75.121,125.729"
      android:strokeAlpha="0.8"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#FFF4EE"
      android:fillAlpha="0.8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M117.976,162.452C124.722,162.452 130.191,158.621 130.191,153.895"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.761,153.895C105.761,155.83 106.68,157.618 108.227,159.051"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M111.693,151.606H101.06"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M113.441,115.118C114.112,115.118 114.656,114.151 114.656,112.958C114.656,111.765 114.112,110.797 113.441,110.797C112.771,110.797 112.227,111.765 112.227,112.958C112.227,114.151 112.771,115.118 113.441,115.118Z"
      android:fillColor="#171A2D"/>
  <path
      android:pathData="M114.371,111.875C114.371,112.092 114.194,112.267 113.976,112.267C113.757,112.267 113.58,112.092 113.58,111.875C113.58,111.658 113.757,111.483 113.976,111.483C114.081,111.482 114.182,111.523 114.256,111.597C114.33,111.671 114.372,111.771 114.371,111.875Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M119.774,114.972C120.444,114.972 120.988,114.004 120.988,112.811C120.988,111.618 120.444,110.65 119.774,110.65C119.103,110.65 118.56,111.618 118.56,112.811C118.56,114.004 119.103,114.972 119.774,114.972Z"
      android:fillColor="#171A2D"/>
  <path
      android:pathData="M120.311,112.117C120.528,112.117 120.703,111.943 120.703,111.728C120.703,111.512 120.528,111.338 120.311,111.338C120.093,111.338 119.918,111.512 119.918,111.728C119.918,111.943 120.093,112.117 120.311,112.117Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M124.668,151.281H132.475"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109,182.624L100.299,177.823L104.569,160.751L113.897,163.052L109,182.624Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M111.598,172.244L109.421,180.947"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M101.072,174.732L104.569,160.751L113.897,163.052L112.32,169.356"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.913,147.132L107.278,147.214L106.558,159.043L106.429,159.035C103.392,158.853 101.079,156.261 101.263,153.246L101.393,151.104C101.537,148.769 103.56,146.99 105.913,147.132Z"
      android:fillColor="#F3D5CB"/>
  <path
      android:pathData="M118.683,156.864L120.759,148.574C121.021,147.528 120.379,146.47 119.326,146.209C118.273,145.949 117.207,146.586 116.945,147.632L116.237,150.465L116.21,150.44L117.217,146.451C117.479,145.406 116.837,144.348 115.784,144.088C115.277,143.961 114.739,144.039 114.291,144.306C113.842,144.573 113.519,145.007 113.393,145.511C113.655,144.466 113.013,143.408 111.96,143.148C110.907,142.888 109.841,143.525 109.58,144.571L112.411,133.248C112.668,132.204 112.027,131.15 110.976,130.891C109.925,130.632 108.862,131.266 108.597,132.308L103.101,154.286L102.864,155.226L102.413,157.034C101.905,159.059 102.228,161.202 103.313,162.989C104.397,164.777 106.153,166.063 108.194,166.564C110.234,167.067 112.391,166.745 114.192,165.669C115.992,164.593 117.288,162.85 117.794,160.825L118.482,158.075C118.581,157.677 118.648,157.272 118.683,156.864Z"
      android:fillColor="#FDB988"/>
  <path
      android:pathData="M105.08,165.042C102.697,163.118 101.65,159.999 102.393,157.04L103.08,154.289"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.377,158.05L117.689,160.801C116.901,163.972 114.221,166.33 110.955,166.727"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M106.992,138.665L108.645,132.065C108.874,131.15 109.904,130.62 110.965,130.88V130.88C112.02,131.13 112.688,132.09 112.458,133.006L108.34,149.47"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M103.632,152.106L106.29,141.488"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.015,146.774L109.567,144.566C109.828,143.521 110.894,142.884 111.947,143.143V143.143C113,143.403 113.642,144.461 113.381,145.507"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M113.394,145.512C113.655,144.466 114.721,143.829 115.774,144.089V144.089C116.827,144.349 117.469,145.407 117.208,146.452L116.618,148.808"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M112.529,148.922L112.832,147.714"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#252944"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M116.225,150.466L116.933,147.632C117.195,146.587 118.26,145.95 119.314,146.21V146.21C120.367,146.47 121.009,147.529 120.747,148.575L119.18,154.827"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M102.34,157.29C101.536,156.343 101.131,155.125 101.209,153.889L101.408,150.623C101.508,148.996 102.656,147.62 104.247,147.217"
      android:fillColor="#FDB988"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M102.34,157.29C101.536,156.343 101.131,155.125 101.209,153.889L101.408,150.623C101.508,148.996 102.656,147.62 104.247,147.217"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M109.867,132.406L109.809,132.601C109.718,132.914 109.757,133.25 109.917,133.535C110.077,133.819 110.344,134.029 110.66,134.117L110.769,134.147"
      android:strokeLineJoin="round"
      android:strokeWidth="0.60155"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M96.921,78.077C88.582,78.966 81.424,84.366 78.328,92.103C75.231,99.841 76.705,108.644 82.157,114.972C91.741,120.068 103.621,117.67 110.439,109.263C117.257,100.856 117.081,88.822 110.02,80.615C106.013,78.474 101.445,77.589 96.921,78.077Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M137.692,101.351C140.282,97.321 136.684,90.446 129.655,85.994C122.626,81.542 114.828,81.199 112.239,85.229C109.649,89.258 113.247,96.134 120.276,100.586C127.305,105.038 135.102,105.38 137.692,101.351Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M135.468,109.305C139.312,109.305 142.428,106.211 142.428,102.395C142.428,98.579 139.312,95.486 135.468,95.486C131.624,95.486 128.508,98.579 128.508,102.395C128.508,106.211 131.624,109.305 135.468,109.305Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M84.744,124.054C88.046,124.054 90.722,121.397 90.722,118.12C90.722,114.842 88.046,112.186 84.744,112.186C81.443,112.186 78.766,114.842 78.766,118.12C78.766,121.397 81.443,124.054 84.744,124.054Z"
      android:fillColor="#B88352"/>
  <path
      android:pathData="M34.246,117.363C49.885,117.363 62.563,104.757 62.563,89.206C62.563,73.656 49.885,61.049 34.246,61.049C18.607,61.049 5.928,73.656 5.928,89.206C5.928,104.757 18.607,117.363 34.246,117.363Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M32.765,94.864C32.121,94.864 31.613,94.653 31.242,94.232C30.895,93.811 30.722,93.217 30.722,92.45C30.722,91.286 30.945,90.32 31.39,89.553C31.836,88.761 32.455,87.956 33.247,87.139C33.817,86.52 34.238,85.988 34.51,85.542C34.807,85.096 34.956,84.601 34.956,84.056C34.956,83.512 34.758,83.078 34.362,82.757C33.965,82.435 33.421,82.274 32.728,82.274C32.158,82.274 31.588,82.385 31.019,82.608C30.474,82.806 29.855,83.103 29.162,83.499L28.271,83.945C27.429,84.415 26.748,84.651 26.228,84.651C25.609,84.651 25.077,84.391 24.631,83.871C24.21,83.326 24,82.67 24,81.902C24,81.382 24.087,80.949 24.26,80.603C24.458,80.256 24.767,79.922 25.188,79.6C26.302,78.783 27.565,78.151 28.976,77.706C30.413,77.235 31.848,77 33.285,77C34.894,77 36.33,77.285 37.593,77.854C38.855,78.399 39.846,79.166 40.564,80.157C41.282,81.122 41.641,82.224 41.641,83.462C41.641,84.378 41.443,85.22 41.047,85.988C40.675,86.755 40.217,87.411 39.672,87.956C39.128,88.501 38.422,89.157 37.556,89.924C36.738,90.593 36.107,91.175 35.661,91.67C35.241,92.14 34.968,92.635 34.844,93.155C34.745,93.7 34.498,94.121 34.102,94.418C33.73,94.715 33.285,94.864 32.765,94.864ZM32.839,103.74C31.824,103.74 30.97,103.393 30.276,102.7C29.608,102.007 29.274,101.153 29.274,100.137C29.274,99.122 29.608,98.268 30.276,97.575C30.97,96.882 31.824,96.535 32.839,96.535C33.879,96.535 34.733,96.882 35.402,97.575C36.095,98.268 36.441,99.122 36.441,100.137C36.441,101.153 36.095,102.007 35.402,102.7C34.733,103.393 33.879,103.74 32.839,103.74Z"
      android:fillColor="@color/color_tertiary"/>
  <path
      android:pathData="M33.98,117.417C19.69,117.411 7.598,106.932 5.662,92.877C3.727,78.822 12.541,65.506 26.305,61.692"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M33.98,60.657C47.525,60.66 59.21,70.098 61.968,83.263C64.726,96.428 57.8,109.708 45.378,115.071"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M115.096,61.264C130.735,61.264 143.413,48.658 143.413,33.107C143.413,17.556 130.735,4.95 115.096,4.95C99.457,4.95 86.779,17.556 86.779,33.107C86.779,48.658 99.457,61.264 115.096,61.264Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M112.765,37.864C112.121,37.864 111.614,37.653 111.242,37.232C110.896,36.811 110.722,36.217 110.722,35.45C110.722,34.286 110.945,33.32 111.391,32.553C111.836,31.761 112.455,30.956 113.248,30.139C113.817,29.52 114.238,28.988 114.51,28.542C114.807,28.096 114.956,27.601 114.956,27.056C114.956,26.512 114.758,26.078 114.362,25.757C113.966,25.435 113.421,25.274 112.728,25.274C112.158,25.274 111.589,25.385 111.019,25.608C110.475,25.806 109.856,26.103 109.162,26.499L108.271,26.945C107.429,27.415 106.748,27.651 106.228,27.651C105.609,27.651 105.077,27.391 104.631,26.871C104.211,26.326 104,25.67 104,24.902C104,24.382 104.087,23.949 104.26,23.603C104.458,23.256 104.768,22.922 105.189,22.6C106.303,21.783 107.565,21.151 108.977,20.706C110.413,20.235 111.849,20 113.285,20C114.894,20 116.33,20.285 117.593,20.854C118.856,21.399 119.846,22.166 120.564,23.157C121.282,24.122 121.641,25.224 121.641,26.462C121.641,27.378 121.443,28.22 121.047,28.988C120.675,29.755 120.217,30.411 119.673,30.956C119.128,31.501 118.422,32.157 117.556,32.924C116.739,33.593 116.107,34.175 115.662,34.67C115.241,35.14 114.968,35.635 114.845,36.155C114.746,36.7 114.498,37.121 114.102,37.418C113.73,37.715 113.285,37.864 112.765,37.864ZM112.839,46.74C111.824,46.74 110.97,46.393 110.277,45.7C109.608,45.007 109.274,44.153 109.274,43.137C109.274,42.122 109.608,41.268 110.277,40.575C110.97,39.882 111.824,39.535 112.839,39.535C113.879,39.535 114.733,39.882 115.402,40.575C116.095,41.268 116.442,42.122 116.442,43.137C116.442,44.153 116.095,45.007 115.402,45.7C114.733,46.393 113.879,46.74 112.839,46.74Z"
      android:fillColor="@color/color_tertiary"/>
  <path
      android:pathData="M85.935,32.952C85.936,22.358 91.881,12.645 101.354,7.764C110.827,2.882 122.25,3.645 130.979,9.741"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.317,14.561C144.337,23.895 145.411,37.286 138.98,47.762C132.55,58.238 120.068,63.429 108.036,60.632C96.003,57.835 87.141,47.683 86.064,35.462"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M193.364,135.513C209.004,135.513 221.682,122.907 221.682,107.356C221.682,91.805 209.004,79.199 193.364,79.199C177.725,79.199 165.047,91.805 165.047,107.356C165.047,122.907 177.725,135.513 193.364,135.513Z"
      android:fillColor="#ffffff"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M192.765,113.864C192.121,113.864 191.613,113.653 191.242,113.232C190.895,112.811 190.722,112.217 190.722,111.45C190.722,110.286 190.945,109.32 191.391,108.553C191.836,107.761 192.455,106.956 193.248,106.139C193.817,105.52 194.238,104.988 194.51,104.542C194.807,104.096 194.956,103.601 194.956,103.056C194.956,102.512 194.758,102.078 194.362,101.756C193.966,101.435 193.421,101.274 192.728,101.274C192.158,101.274 191.589,101.385 191.019,101.608C190.475,101.806 189.856,102.103 189.162,102.499L188.271,102.945C187.429,103.415 186.748,103.651 186.228,103.651C185.609,103.651 185.077,103.391 184.631,102.871C184.211,102.326 184,101.67 184,100.902C184,100.382 184.087,99.949 184.26,99.603C184.458,99.256 184.768,98.922 185.188,98.6C186.303,97.783 187.565,97.151 188.977,96.706C190.413,96.235 191.849,96 193.285,96C194.894,96 196.33,96.285 197.593,96.854C198.856,97.399 199.846,98.166 200.564,99.157C201.282,100.122 201.641,101.224 201.641,102.462C201.641,103.378 201.443,104.22 201.047,104.988C200.675,105.755 200.217,106.411 199.673,106.956C199.128,107.501 198.422,108.157 197.556,108.924C196.739,109.593 196.107,110.175 195.662,110.67C195.241,111.14 194.968,111.635 194.845,112.155C194.746,112.7 194.498,113.121 194.102,113.418C193.73,113.715 193.285,113.864 192.765,113.864ZM192.839,122.74C191.824,122.74 190.97,122.393 190.276,121.7C189.608,121.007 189.274,120.153 189.274,119.137C189.274,118.122 189.608,117.268 190.276,116.575C190.97,115.882 191.824,115.535 192.839,115.535C193.879,115.535 194.733,115.882 195.402,116.575C196.095,117.268 196.442,118.122 196.442,119.137C196.442,120.153 196.095,121.007 195.402,121.7C194.733,122.393 193.879,122.74 192.839,122.74Z"
      android:fillColor="@color/color_tertiary"/>
  <path
      android:pathData="M178.138,83.576C187.189,78.19 198.492,78.191 207.542,83.579C216.592,88.966 221.918,98.863 221.396,109.326C220.875,119.788 214.59,129.115 205.049,133.589"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M192.924,136.25C181.358,136.253 170.929,129.337 166.503,118.729C162.077,108.121 164.527,95.91 172.709,87.794"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M60.014,56.336C60.778,56.336 61.397,55.721 61.397,54.963C61.397,54.205 60.778,53.59 60.014,53.59C59.251,53.59 58.631,54.205 58.631,54.963C58.631,55.721 59.251,56.336 60.014,56.336Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M71.633,51.993C72.138,51.993 72.547,51.586 72.547,51.085C72.547,50.583 72.138,50.177 71.633,50.177C71.128,50.177 70.718,50.583 70.718,51.085C70.718,51.586 71.128,51.993 71.633,51.993Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M55.591,44.77H54.584V43.77C54.595,43.646 54.534,43.527 54.428,43.462C54.322,43.397 54.188,43.397 54.082,43.462C53.975,43.527 53.915,43.646 53.926,43.77V44.77H52.919C52.736,44.77 52.589,44.917 52.589,45.097C52.589,45.278 52.736,45.425 52.919,45.425H53.926V46.425C53.941,46.595 54.084,46.724 54.255,46.724C54.426,46.724 54.569,46.595 54.584,46.425V45.425H55.591C55.774,45.425 55.921,45.278 55.921,45.097C55.921,44.917 55.774,44.77 55.591,44.77Z"
      android:fillColor="#F0917A"/>
  <path
      android:pathData="M151.723,16.105C152.096,16.105 152.398,15.805 152.398,15.435C152.398,15.065 152.096,14.765 151.723,14.765C151.35,14.765 151.048,15.065 151.048,15.435C151.048,15.805 151.35,16.105 151.723,16.105Z"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#EDA257"/>
  <path
      android:pathData="M165.978,69.004C166.368,69.004 166.684,68.691 166.684,68.304C166.684,67.917 166.368,67.604 165.978,67.604C165.589,67.604 165.273,67.917 165.273,68.304C165.273,68.691 165.589,69.004 165.978,69.004Z"
      android:fillColor="#529ED6"/>
  <path
      android:pathData="M172.873,68.909C173.387,68.909 173.803,68.495 173.803,67.986C173.803,67.476 173.387,67.063 172.873,67.063C172.36,67.063 171.944,67.476 171.944,67.986C171.944,68.495 172.36,68.909 172.873,68.909Z"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#E4F4FC"/>
  <path
      android:pathData="M210.714,70.567C211.584,70.567 212.289,69.868 212.289,69.005C212.289,68.141 211.584,67.442 210.714,67.442C209.845,67.442 209.14,68.141 209.14,69.005C209.14,69.868 209.845,70.567 210.714,70.567Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M134.404,40.906C134.777,40.906 135.079,40.606 135.079,40.236C135.079,39.866 134.777,39.566 134.404,39.566C134.031,39.566 133.729,39.866 133.729,40.236C133.729,40.606 134.031,40.906 134.404,40.906Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M137.372,33.932C138.093,33.932 138.677,33.352 138.677,32.637C138.677,31.922 138.093,31.342 137.372,31.342C136.651,31.342 136.067,31.922 136.067,32.637C136.067,33.352 136.651,33.932 137.372,33.932Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.808484"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#161E24"
      android:strokeLineCap="round"/>
</vector>
