<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="200dp"
    android:height="152dp"
    android:viewportWidth="200"
    android:viewportHeight="152">
  <group>
    <clip-path android:pathData="M0,0h200v152h-200z M 0,0"/>
    <path
        android:pathData="M0,0h200v152h-200z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M0,0h200v152h-200z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="0"
            android:startX="100"
            android:endY="152"
            android:endX="100"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M58.409,75.851C57.325,75.718 56.221,75.649 55.1,75.649C40.243,75.649 28.2,87.68 28.2,102.523C28.2,103.388 28.241,104.244 28.322,105.089C28.682,108.86 25.705,112.117 21.913,112.117C19.722,112.117 17.944,113.892 17.944,116.082C17.944,118.272 19.722,120.047 21.913,120.047H178.544C180.737,120.047 182.513,118.272 182.513,116.082C182.513,113.892 180.737,112.117 178.544,112.117H172.554C166.809,112.117 162.12,107.561 161.918,101.825C161.915,101.744 161.912,101.664 161.908,101.583C161.447,91.84 153.7,83.852 143.966,83.099C141.133,82.879 138.42,83.273 135.934,84.149C132.291,65.374 115.75,51.198 95.892,51.198C79.087,51.198 64.658,61.351 58.409,75.851Z"
        android:fillColor="@color/color_tertiary_lighter"/>
    <path
        android:pathData="M4.116,107.32C1.843,107.32 0,109.16 0,111.431C0,113.703 1.843,115.544 4.116,115.544H11.172C13.446,115.544 15.288,113.703 15.288,111.431C15.288,109.16 13.446,107.32 11.172,107.32H4.116Z"
        android:fillColor="@color/color_tertiary_lighter"/>
    <path
        android:pathData="M12.642,100.074C11.83,100.074 11.172,100.732 11.172,101.543C11.172,102.354 11.83,103.012 12.642,103.012H14.994C15.806,103.012 16.464,102.354 16.464,101.543C16.464,100.732 15.806,100.074 14.994,100.074H12.642Z"
        android:fillColor="@color/color_tertiary_lighter"/>
    <path
        android:pathData="M162.51,130.241C158.856,130.241 155.895,133.2 155.895,136.85C155.895,140.499 158.856,143.458 162.51,143.458H193.459C197.112,143.458 200.074,140.499 200.074,136.85C200.074,133.2 197.112,130.241 193.459,130.241H162.51Z"
        android:fillColor="@color/color_tertiary_lighter"/>
    <path
        android:pathData="M106.872,144.72C104.843,144.72 103.197,146.364 103.197,148.391C103.197,150.419 104.843,152.063 106.872,152.063H122.161C124.19,152.063 125.836,150.419 125.836,148.391C125.836,146.364 124.19,144.72 122.161,144.72H106.872Z"
        android:fillColor="@color/color_tertiary_lighter"/>
    <path
        android:pathData="M61.447,88.648H72.112"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.753,90.088H108.295"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M104.092,67.701C100.785,66.572 99.361,62.685 101.157,59.689L129.095,29.686L132.327,35.119L110.864,64.79C109.691,67.355 106.763,68.613 104.092,67.701Z"
        android:fillColor="@color/color_primary"/>
    <path
        android:pathData="M106.834,67.936C104.097,68.368 101.543,66.458 101.188,63.713L98.154,40.281C97.754,37.194 99.935,34.368 103.025,33.968C106.25,33.551 109.159,35.937 109.378,39.178L110.973,62.751C111.144,65.289 109.349,67.538 106.834,67.936Z"
        android:fillColor="@color/color_primary"/>
    <path
        android:pathData="M104.563,41.565L101.163,63.528L101.187,63.713C101.543,66.458 104.097,68.368 106.833,67.936C107.356,67.853 107.845,67.687 108.295,67.458L104.563,41.565Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M94.523,93.909H81.115C74.142,93.909 69.051,86.955 70.813,79.837L80.514,40.667C81.926,34.967 86.807,30.99 92.392,30.99C99.171,30.99 104.668,36.788 104.668,43.94V83.207C104.668,89.118 100.126,93.909 94.523,93.909Z"
        android:fillColor="@color/color_primary"/>
    <path
        android:pathData="M116.274,13.051C114.791,16.657 110.662,18.378 107.052,16.896C103.443,15.414 101.72,11.289 103.203,7.683C104.687,4.077 108.816,2.356 112.425,3.838C116.035,5.32 117.758,9.445 116.274,13.051Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M94.141,37.777C91.455,35.26 91.321,31.044 93.84,28.361L106.625,14.746L116.352,23.862L103.566,37.476C101.047,40.16 96.827,40.294 94.141,37.777Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M110.267,30.34L93.839,28.361L104.911,16.57L114.637,25.686L110.267,30.34Z"
        android:fillColor="#F0917A"/>
    <path
        android:pathData="M102.253,19.4L93.838,28.361L93.542,28.772C93.064,29.438 92.678,30.165 92.396,30.934L92.182,31.517"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.242,29.302L103.566,37.477C101.046,40.16 96.826,40.294 94.141,37.777C92.805,36.526 92.101,34.855 92.039,33.161"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M121.1,24.476C120.179,25.807 120.512,27.631 121.844,28.552C123.176,29.471 125.002,29.139 125.923,27.808C126.844,26.477 126.511,24.653 125.179,23.733C123.847,22.813 122.021,23.145 121.1,24.476Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M121.1,24.476C120.179,25.807 120.512,27.631 121.844,28.552C123.176,29.471 125.002,29.139 125.923,27.808C126.844,26.477 126.511,24.653 125.179,23.733C123.847,22.813 122.021,23.145 121.1,24.476Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M99.401,17.461C97.129,25.205 101.572,33.322 109.323,35.591C117.074,37.861 125.199,33.423 127.471,25.679C129.742,17.935 125.3,9.818 117.549,7.549C109.797,5.28 101.672,9.717 99.401,17.461Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M116.684,17.893C115.594,17.632 114.497,18.303 114.235,19.393"
        android:strokeLineJoin="round"
        android:strokeWidth="1.10362"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M104.857,23.047C104.295,24.017 104.628,25.258 105.6,25.819"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M121.196,19.978C121.081,18.863 120.083,18.052 118.967,18.167"
        android:strokeLineJoin="round"
        android:strokeWidth="1.10362"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.472,25.679C129.743,17.935 125.301,9.818 117.55,7.549C109.798,5.28 101.673,9.718 99.402,17.461C98.547,20.374 98.643,23.34 99.507,26.028"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.158,14.185C95.415,15.992 96.278,18.058 98.087,18.8C99.895,19.543 101.963,18.681 102.707,16.874C103.45,15.068 102.587,13.001 100.778,12.259C98.97,11.516 96.902,12.379 96.158,14.185Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M99.895,16.827C100.283,15.887 99.833,14.812 98.891,14.425"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M102.707,16.874C103.45,15.068 102.587,13.002 100.778,12.259C98.97,11.516 96.902,12.379 96.158,14.185C95.415,15.991 96.278,18.058 98.087,18.801C98.752,19.073 99.451,19.13 100.108,19.002"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.274,20.187C105.104,20.045 104.041,20.878 103.898,22.046"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#F0917A"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M121.179,10.36C120.068,13.059 116.977,14.348 114.275,13.239C111.573,12.129 110.283,9.041 111.394,6.342C112.504,3.642 115.595,2.354 118.297,3.463C120.999,4.573 122.289,7.661 121.179,10.36Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M133.339,13.925C131.621,18.101 126.84,20.094 122.659,18.378C118.479,16.662 116.483,11.884 118.202,7.709C119.92,3.533 124.702,1.539 128.881,3.255C133.062,4.972 135.057,9.749 133.339,13.925Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M112.661,5.869C112.168,5.99 111.67,5.689 111.549,5.196L110.675,1.631C110.554,1.139 110.856,0.641 111.349,0.521C111.842,0.4 112.34,0.701 112.461,1.194L113.335,4.759C113.456,5.251 113.154,5.749 112.661,5.869Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M112.081,4.724C111.716,4.373 111.705,3.791 112.057,3.426L115.088,0.281C115.44,-0.084 116.023,-0.095 116.388,0.257C116.754,0.609 116.765,1.19 116.413,1.556L113.382,4.7C113.029,5.065 112.447,5.076 112.081,4.724Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M94.523,93.909C99.906,93.909 104.307,89.719 104.644,84.426C102.343,82.785 99.528,81.816 96.485,81.816H79.921C76.337,81.816 73.089,83.247 70.713,85.566C71.75,90.234 75.907,93.909 81.115,93.909H94.523Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M85.1,144.49C83.56,142.627 83.629,139.914 85.262,138.132L97.153,125.153C99.646,122.431 103.973,122.541 106.325,125.386C108.463,127.972 108.098,131.8 105.509,133.936L91.93,145.141C89.864,146.845 86.806,146.554 85.1,144.49Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M85.1,144.49C83.56,142.627 83.629,139.914 85.262,138.132L97.153,125.153C99.646,122.431 103.973,122.541 106.325,125.386C108.463,127.972 108.098,131.8 105.509,133.936L91.93,145.141C89.864,146.845 86.806,146.554 85.1,144.49Z"
        android:fillColor="#E43C28"/>
    <path
        android:pathData="M85.2,138.205C88.913,138.465 91.843,141.462 91.843,145.124C91.843,145.152 91.841,145.179 91.841,145.206C89.777,146.833 86.782,146.525 85.1,144.49C83.579,142.651 83.63,139.988 85.2,138.205Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M101.978,136.85L91.93,145.14C89.864,146.845 86.806,146.554 85.1,144.49C83.56,142.627 83.629,139.915 85.262,138.132"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M87.686,135.486L97.152,125.153C99.646,122.431 103.973,122.541 106.325,125.385C108.463,127.972 108.097,131.8 105.508,133.936"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M98.935,132.477L94.502,132.113"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.5,129.899L96.718,134.183"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M95.853,135.485L91.42,135.121"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.418,132.907L93.636,137.191"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M95.608,144.49C97.149,142.627 97.08,139.914 95.447,138.132L83.556,125.153C81.062,122.431 76.735,122.541 74.383,125.386C72.245,127.972 72.611,131.8 75.199,133.936L88.779,145.141C90.845,146.845 93.902,146.554 95.608,144.49Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M95.608,144.49C97.149,142.627 97.08,139.914 95.447,138.132L83.556,125.153C81.062,122.431 76.735,122.541 74.383,125.386C72.245,127.972 72.611,131.8 75.199,133.936L88.779,145.141C90.845,146.845 93.902,146.554 95.608,144.49Z"
        android:fillColor="#E43C28"/>
    <path
        android:pathData="M95.507,138.205C91.794,138.465 88.864,141.462 88.864,145.124C88.864,145.152 88.866,145.179 88.866,145.206C90.93,146.833 93.925,146.525 95.607,144.49C97.127,142.651 97.077,139.988 95.507,138.205Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M78.729,136.85L88.778,145.14C90.844,146.845 93.901,146.554 95.607,144.49C97.148,142.627 97.079,139.914 95.446,138.132"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.022,135.486L83.556,125.153C81.062,122.431 76.735,122.541 74.383,125.385C72.245,127.972 72.61,131.8 75.2,133.936"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M78.644,129.887C76.237,129.887 74.286,127.807 74.286,125.241V87.049C74.286,82.375 77.84,78.586 82.224,78.586C83.594,78.586 84.883,78.955 86.008,79.607C89.019,81.351 90.531,85.065 89.866,88.648L82.919,126.142C82.515,128.318 80.725,129.887 78.644,129.887Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M76.101,129.015C76.817,129.564 77.695,129.887 78.644,129.887C80.725,129.887 82.515,128.318 82.919,126.142L84.854,116.523H79.57H74.286V125.241C74.286,126.304 74.621,127.283 75.183,128.066"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M81.772,132.477L86.205,132.113"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M84.207,129.899L83.988,134.183"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M84.854,135.485L89.287,135.121"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M87.29,132.907L87.071,137.191"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.103,129.015C76.818,129.564 77.696,129.887 78.645,129.887C80.726,129.887 82.516,128.318 82.92,126.142L84.855,116.523"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M102.063,129.887C104.469,129.887 106.42,127.807 106.42,125.241V87.049C106.42,82.375 102.866,78.586 98.482,78.586C97.112,78.586 95.823,78.955 94.698,79.607C91.687,81.351 90.176,85.065 90.84,88.648L97.788,126.142C98.191,128.318 99.982,129.887 102.063,129.887Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M104.606,129.015C103.891,129.564 103.013,129.887 102.064,129.887C99.983,129.887 98.192,128.318 97.789,126.142L95.854,116.523H101.137H106.421V125.241C106.421,126.304 106.087,127.283 105.524,128.066L105.127,128.657"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M104.606,129.015C103.891,129.564 103.013,129.887 102.064,129.887C99.983,129.887 98.193,128.318 97.789,126.142L95.854,116.523"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M117.439,43.334H104.546L107.434,23.865C107.754,21.705 109.61,20.106 111.795,20.106H112.264C115.113,20.106 117.422,22.412 117.425,25.258L117.439,43.334Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M117.421,43.334C117.421,44.467 114.543,45.385 110.992,45.385C107.442,45.385 104.564,44.467 104.564,43.334C104.564,42.201 107.442,41.283 110.992,41.283C114.543,41.283 117.421,42.201 117.421,43.334Z"
        android:fillColor="#E4F4FC"/>
    <path
        android:pathData="M117.421,43.334C117.421,44.467 114.543,45.385 110.992,45.385C107.442,45.385 104.564,44.467 104.564,43.334C104.564,42.201 107.442,41.283 110.992,41.283C114.543,41.283 117.421,42.201 117.421,43.334Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.827,41.403C108.815,41.496 108.807,41.589 108.807,41.685C108.807,42.891 109.785,43.868 110.993,43.868C112.2,43.868 113.179,42.891 113.179,41.685C113.179,41.589 113.17,41.496 113.158,41.403C112.481,41.326 111.753,41.284 110.993,41.284C110.233,41.284 109.504,41.326 108.827,41.403Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M117.438,43.334H130.332L129.807,39.795L127.444,23.865C127.374,23.394 127.231,22.949 127.027,22.542C126.298,21.084 124.791,20.106 123.082,20.106H122.614C119.765,20.106 117.455,22.412 117.453,25.258L117.438,43.334Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M117.457,43.334C117.457,44.467 120.335,45.385 123.885,45.385C127.435,45.385 130.313,44.467 130.313,43.334C130.313,42.201 127.435,41.283 123.885,41.283C120.335,41.283 117.457,42.201 117.457,43.334Z"
        android:fillColor="#E4F4FC"/>
    <path
        android:pathData="M117.457,43.334C117.457,44.467 120.335,45.385 123.885,45.385C127.435,45.385 130.313,44.467 130.313,43.334C130.313,42.201 127.435,41.283 123.885,41.283C120.335,41.283 117.457,42.201 117.457,43.334Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.031,42.072C130.031,40.94 127.216,40.021 123.744,40.021C120.272,40.021 117.458,40.94 117.458,42.072"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M117.215,42.072C117.215,40.94 114.421,40.021 110.974,40.021C107.527,40.021 104.732,40.94 104.732,42.072"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M126.051,41.403C126.063,41.496 126.071,41.589 126.071,41.685C126.071,42.891 125.092,43.868 123.885,43.868C122.678,43.868 121.699,42.891 121.699,41.685C121.699,41.589 121.707,41.496 121.72,41.403C122.396,41.326 123.125,41.284 123.885,41.284C124.645,41.284 125.374,41.326 126.051,41.403Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M106.202,28.017L102.543,30.312C101.727,30.824 100.65,30.578 100.138,29.763C99.625,28.948 99.871,27.872 100.687,27.36L104.347,25.064C105.162,24.552 106.24,24.798 106.752,25.613C107.265,26.428 107.019,27.505 106.202,28.017Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M126.077,37.792L130.365,37.256C130.484,37.241 130.599,37.214 130.709,37.177C131.478,36.917 131.984,36.146 131.879,35.31C131.76,34.354 130.888,33.677 129.931,33.797L125.644,34.332C124.688,34.452 124.009,35.323 124.129,36.278C124.249,37.234 125.121,37.911 126.077,37.792Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M125.381,34.466L129.668,33.93C130.624,33.811 131.303,32.939 131.183,31.984C131.063,31.028 130.191,30.351 129.235,30.471L124.948,31.007C123.991,31.126 123.313,31.997 123.433,32.953C123.552,33.908 124.424,34.585 125.381,34.466Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M124.973,31.196L129.26,30.661C130.216,30.541 130.894,29.67 130.775,28.715C130.655,27.759 129.783,27.082 128.827,27.201L124.539,27.737C123.583,27.857 122.905,28.728 123.024,29.683C123.144,30.638 124.016,31.316 124.973,31.196Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M127.26,27.136L131.357,28.507C132.271,28.813 133.26,28.321 133.566,27.408C133.872,26.495 133.38,25.508 132.466,25.201L128.369,23.83C127.455,23.524 126.467,24.016 126.16,24.929C125.854,25.842 126.347,26.83 127.26,27.136Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M123.231,27.371L127.542,27.083C128.503,27.019 129.231,26.188 129.167,25.227C129.102,24.267 128.271,23.54 127.309,23.604L122.998,23.892C122.036,23.956 121.309,24.787 121.373,25.748C121.437,26.708 122.269,27.435 123.231,27.371Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M131.078,38.254C132.454,38.264 133.579,37.158 133.589,35.783L133.651,27.521C133.661,26.145 132.554,25.022 131.177,25.012C129.8,25.001 128.676,26.108 128.665,27.483L128.604,35.745C128.593,37.121 129.701,38.244 131.078,38.254Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M129.26,30.661L124.972,31.196C124.016,31.316 123.144,30.638 123.024,29.683C122.905,28.728 123.583,27.856 124.539,27.737L126.576,27.482"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M123.433,32.952C123.552,33.908 124.424,34.585 125.381,34.466L129.358,33.868"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M90.175,71.652C87.438,72.084 84.884,70.174 84.529,67.429L81.494,43.997C81.095,40.91 83.276,38.083 86.366,37.684C89.591,37.267 92.5,39.653 92.719,42.894L94.313,66.467C94.485,69.005 92.689,71.254 90.175,71.652Z"
        android:fillColor="@color/color_primary"/>
    <path
        android:pathData="M82.429,51.198L81.496,43.997"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M84.529,67.429L84.278,65.489L82.924,55.036L82.827,54.288"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M86.366,37.684C89.591,37.267 92.5,39.653 92.719,42.894L92.956,45.883"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M87.995,71.627C84.688,70.497 83.265,66.61 85.06,63.615L100.136,29.763L103.899,39.795L94.767,68.716C93.594,71.28 90.666,72.539 87.995,71.627Z"
        android:fillColor="@color/color_primary"/>
    <path
        android:pathData="M104.546,43.334L104.921,40.811"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.434,23.865C107.754,21.705 109.61,20.106 111.795,20.106H112.264C115.112,20.106 117.422,22.412 117.424,25.258L117.439,43.334"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M130.332,43.334L129.765,39.514"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M127.027,22.542C126.298,21.084 124.791,20.106 123.082,20.106H122.614C119.765,20.106 117.455,22.412 117.453,25.258L117.438,43.334"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.489,22.854C124.864,21.986 123.845,21.431 122.711,21.431H122.348"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M109.859,38.096L105.566,38.582C105.446,38.596 105.328,38.597 105.212,38.586C104.404,38.515 103.73,37.884 103.635,37.047C103.527,36.09 104.215,35.226 105.173,35.118L109.466,34.632C110.423,34.523 111.288,35.21 111.396,36.167C111.505,37.124 110.817,37.987 109.859,38.096Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M109.753,34.7L105.459,35.186C104.502,35.295 103.638,34.607 103.529,33.651C103.42,32.694 104.109,31.83 105.066,31.722L109.359,31.236C110.317,31.127 111.181,31.815 111.29,32.771C111.399,33.728 110.71,34.591 109.753,34.7Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M109.381,31.426L105.087,31.912C104.13,32.021 103.266,31.333 103.157,30.377C103.048,29.42 103.737,28.557 104.694,28.448L108.987,27.962C109.945,27.853 110.809,28.541 110.918,29.497C111.027,30.454 110.338,31.317 109.381,31.426Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M108.701,28.539L104.443,29.272C103.493,29.435 102.59,28.799 102.427,27.85C102.263,26.901 102.9,26 103.85,25.836L108.108,25.103C109.058,24.94 109.96,25.576 110.124,26.525C110.288,27.474 109.651,28.375 108.701,28.539Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M105.108,39.72C103.772,40.054 102.419,39.243 102.085,37.908L100.08,29.892C99.746,28.558 100.558,27.206 101.894,26.872C103.23,26.539 104.583,27.35 104.917,28.685L106.922,36.701C107.255,38.035 106.443,39.387 105.108,39.72Z"
        android:fillColor="#F3D5CB"/>
    <path
        android:pathData="M106.947,28.193L108.987,27.962C109.945,27.853 110.809,28.541 110.917,29.497C111.026,30.454 110.338,31.317 109.38,31.426L105.087,31.912"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.29,32.771C111.399,33.728 110.711,34.591 109.753,34.7L105.746,35.053"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M105.108,39.72C103.772,40.054 102.419,39.243 102.085,37.908L100.08,29.892C99.746,28.558 100.558,27.206 101.894,26.872"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M102.555,26.839C102.785,26.329 103.256,25.938 103.85,25.836L108.108,25.103C109.058,24.94 109.96,25.576 110.124,26.525C110.221,27.088 110.036,27.635 109.671,28.021"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M131.079,38.254C132.456,38.264 133.58,37.158 133.591,35.783L133.652,27.521C133.663,26.145 132.555,25.022 131.178,25.012"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M89.743,53.101L100.136,29.763"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M100.374,50.959L94.767,68.716C93.594,71.28 90.666,72.539 87.995,71.627C84.688,70.497 83.265,66.61 85.06,63.615"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M103.901,39.795L102.782,43.334"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M91.648,68.628C91.648,69.723 90.759,70.611 89.663,70.611C88.567,70.611 87.679,69.723 87.679,68.628"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.007,83.799C81.911,83.799 81.022,82.911 81.022,81.816"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M98.107,83.294C97.011,83.294 96.122,82.407 96.122,81.312"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.514,40.667L76.917,55.191L84.277,67.912C84.277,67.912 83.036,58.603 81.979,50.867C81.216,45.286 80.549,40.524 80.514,40.667Z"
        android:fillColor="@color/color_primary"/>
    <path
        android:pathData="M74.286,87.049C74.286,82.375 77.84,78.585 82.224,78.585C83.594,78.585 84.883,78.955 86.008,79.607C89.019,81.351 90.531,85.065 89.866,88.648L89.308,91.66"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.42,90.088V87.049C106.42,82.374 102.866,78.585 98.481,78.585C97.112,78.585 95.823,78.955 94.698,79.607C93.192,80.479 92.062,81.843 91.391,83.438"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M90.841,88.648L91.088,89.982L91.508,92.247"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.803,51.479L114.705,45.141"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.644,24.433L115.459,35.794"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M115.459,37.637V39.514"
        android:strokeAlpha="0.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:fillAlpha="0.5"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M75.99,58.938L72.113,74.59"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M104.667,60.814L104.58,77.457"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M103.9,39.795L101.873,46.215L100.618,32.044L102.555,38.856L103.9,39.795Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M123.744,46.985L118.105,54.781"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M75.193,118.138H83.499"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"/>
    <path
        android:pathData="M83.006,119.497H74.286"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#E43C28"/>
    <path
        android:pathData="M98.278,118.138H106.584"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#EDA257"/>
    <path
        android:pathData="M106.091,119.497H97.371"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#E43C28"/>
    <path
        android:pathData="M74.286,116.523V125.241"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.421,116.523V125.241"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M95.854,112.533H106.584"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M75.562,112.521H83.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.441449"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.46,116.498H94.818L93.642,112.533H107.46V116.498Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M86.48,116.498H73.837L72.661,112.533H86.48V116.498Z"
        android:fillColor="#161E24"/>
    <path
        android:pathData="M172.568,27.407C172.568,28.087 172.015,28.639 171.334,28.639C170.653,28.639 170.101,28.087 170.101,27.407C170.101,26.726 170.653,26.174 171.334,26.174C172.015,26.174 172.568,26.726 172.568,27.407Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M171.333,66.193C171.333,66.873 170.781,67.425 170.1,67.425C169.418,67.425 168.866,66.873 168.866,66.193C168.866,65.512 169.418,64.961 170.1,64.961C170.781,64.961 171.333,65.512 171.333,66.193Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M182.513,23.925C182.513,24.375 182.147,24.74 181.697,24.74C181.246,24.74 180.881,24.375 180.881,23.925C180.881,23.475 181.246,23.11 181.697,23.11C182.147,23.11 182.513,23.475 182.513,23.925Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.662173"
        android:fillColor="#00000000"
        android:strokeColor="#161E24"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M149.452,23.446H148.556V22.551C148.556,22.388 148.425,22.257 148.262,22.257C148.1,22.257 147.968,22.388 147.968,22.551V23.446H147.072C146.91,23.446 146.778,23.577 146.778,23.739C146.778,23.901 146.91,24.033 147.072,24.033H147.968V24.928C147.968,25.09 148.1,25.222 148.262,25.222C148.425,25.222 148.556,25.09 148.556,24.928V24.033H149.452C149.615,24.033 149.746,23.901 149.746,23.739C149.746,23.577 149.615,23.446 149.452,23.446Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M53.514,25.445H51.738V23.671C51.738,23.349 51.478,23.088 51.155,23.088C50.833,23.088 50.573,23.349 50.573,23.671V25.445H48.797C48.474,25.445 48.214,25.706 48.214,26.028C48.214,26.349 48.474,26.61 48.797,26.61H50.573V28.384C50.573,28.706 50.833,28.967 51.155,28.967C51.478,28.967 51.738,28.706 51.738,28.384V26.61H53.514C53.836,26.61 54.097,26.349 54.097,26.028C54.097,25.706 53.836,25.445 53.514,25.445Z"
        android:fillColor="#EDA257"/>
    <path
        android:pathData="M66.287,10.044H65.303V9.061C65.303,8.883 65.159,8.739 64.98,8.739C64.802,8.739 64.658,8.883 64.658,9.061V10.044H63.673C63.495,10.044 63.351,10.189 63.351,10.367C63.351,10.545 63.495,10.69 63.673,10.69H64.658V11.673C64.658,11.851 64.802,11.995 64.98,11.995C65.159,11.995 65.303,11.851 65.303,11.673V10.69H66.287C66.466,10.69 66.61,10.545 66.61,10.367C66.61,10.189 66.466,10.044 66.287,10.044Z"
        android:fillColor="#EDA257"/>
  </group>
</vector>
