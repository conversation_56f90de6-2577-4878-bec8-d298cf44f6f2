<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="30dp"
    android:height="30dp"
    android:viewportWidth="30"
    android:viewportHeight="30">
  <path
      android:strokeWidth="1"
      android:pathData="M11.3333,18.5952H18.6667C22.4349,18.5952 25.5,21.6908 25.5,25.5238V26.3854C25.4084,26.41 25.2987,26.4384 25.1712,26.4699C24.7129,26.583 24.0236,26.7352 23.1103,26.8878C21.2839,27.1929 18.5614,27.5 15,27.5C11.4386,27.5 8.7161,27.1929 6.8897,26.8878C5.9764,26.7352 5.2871,26.583 4.8288,26.4699C4.7013,26.4384 4.5916,26.41 4.5,26.3854V25.5238C4.5,21.6908 7.5651,18.5952 11.3333,18.5952Z"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.75"
      android:strokeColor="@color/color_primary"/>
  <path
      android:pathData="M6.1366,20.2822C6.0072,20.4139 5.8826,20.5504 5.7632,20.6916C4.664,21.9905 4,23.6786 4,25.5238V26.7619C4,26.7619 5.5916,27.2779 8.6515,27.6436C8.989,27.684 9.3444,27.7225 9.7175,27.7583L6.1366,20.2822Z"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.6"/>
  <path
      android:strokeWidth="1"
      android:pathData="M9.3887,8.1905C9.3887,5.0421 11.9074,2.5 14.9998,2.5C18.0921,2.5 20.6109,5.0421 20.6109,8.1905C20.6109,9.7771 19.9711,11.5286 18.9253,12.8807C17.8775,14.2354 16.481,15.119 14.9998,15.119C13.5185,15.119 12.1221,14.2354 11.0743,12.8807C10.0284,11.5286 9.3887,9.7771 9.3887,8.1905Z"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.75"
      android:strokeColor="@color/color_primary"/>
  <path
      android:pathData="M8.8887,8.1904C8.8887,7.2576 9.0925,6.3729 9.4574,5.5795L14.9998,15.619C11.6252,15.619 8.8887,11.6088 8.8887,8.1904Z"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.6"/>
  <path
      android:strokeWidth="1"
      android:pathData="M20.7422,21.2006C20.7422,21.2006 21.2934,21.2741 22.0099,21.9129C22.7265,22.5518 22.8513,22.8868 22.8513,22.8868"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M23.8347,24.5345L23.8869,24.5345L23.8347,24.5345Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="23.9596"
          android:startX="23.208"
          android:endY="25.1947"
          android:endX="23.2944"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M23.8347,24.5345L23.8869,24.5345"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
</vector>
