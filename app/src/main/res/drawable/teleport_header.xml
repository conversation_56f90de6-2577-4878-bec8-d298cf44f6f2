<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="200dp"
    android:height="158dp"
    android:viewportWidth="200"
    android:viewportHeight="158">
  <path
      android:pathData="M133.636,20.927L133.636,20.927A0.568,0.568 65.247,0 1,133.299 20.197L134.346,17.36A0.568,0.568 65.247,0 1,135.076 17.024L135.076,17.024A0.568,0.568 65.247,0 1,135.412 17.753L134.366,20.591A0.568,0.568 65.247,0 1,133.636 20.927z"
      android:fillColor="@color/color_primary"/>
  <path
      android:pathData="M102.136,77.349m-69.154,0a69.154,69.154 0,1 1,138.308 0a69.154,69.154 0,1 1,-138.308 0"
      android:fillColor="#fff7f7"/>
  <path
      android:pathData="M41.624,12.632L51.045,36.216L138.394,36.454L147.858,12.632H41.624Z"
      android:fillColor="@color/color_tertiary"/>
  <path
      android:pathData="M50.801,35.065a11.688,43.948 90,1 1,87.896 -0a11.688,43.948 90,1 1,-87.896 -0z"
      android:fillColor="@color/color_tertiary_lighter"/>
  <path
      android:pathData="M65.46,37.64a29.455,6.545 0,1 0,58.909 0a29.455,6.545 0,1 0,-58.909 0z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M65.46,37.64a29.455,6.545 0,1 0,58.909 0a29.455,6.545 0,1 0,-58.909 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="31.0942"
          android:startX="94.914"
          android:endY="44.1851"
          android:endX="94.914"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M41.451,11.688a11.688,53.299 90,1 1,106.597 -0a11.688,53.299 90,1 1,-106.597 -0z"
      android:fillColor="@color/color_tertiary"/>
  <path
      android:pathData="M97.736,9.185C97.771,9.146 97.809,9.107 97.848,9.068C97.997,8.919 98.176,8.79 98.384,8.68C98.593,8.561 98.826,8.467 99.085,8.397C99.353,8.318 99.651,8.278 99.979,8.278C100.486,8.278 100.953,8.377 101.38,8.576C101.807,8.765 102.175,9.028 102.483,9.366C102.791,9.704 103.029,10.101 103.198,10.558C103.367,11.015 103.451,11.507 103.451,12.033C103.451,12.56 103.367,13.057 103.198,13.524C103.039,13.981 102.805,14.383 102.497,14.731C102.199,15.068 101.832,15.342 101.395,15.55C100.967,15.749 100.481,15.849 99.934,15.849C99.427,15.849 98.961,15.744 98.533,15.535C98.116,15.327 97.793,15.044 97.565,14.686H97.535V19.216H95.746V11.914H94.639L96.54,8.475L96.785,8.874L96.784,8.875L98.014,10.882L98.017,10.88L98.652,11.914H97.479C97.477,11.964 97.475,12.013 97.475,12.063C97.475,12.341 97.52,12.62 97.609,12.898C97.709,13.166 97.848,13.409 98.027,13.628C98.215,13.847 98.439,14.025 98.697,14.164C98.956,14.294 99.249,14.358 99.576,14.358C99.924,14.358 100.227,14.289 100.486,14.149C100.744,14.01 100.958,13.832 101.126,13.613C101.295,13.394 101.419,13.146 101.499,12.868C101.588,12.59 101.633,12.311 101.633,12.033C101.633,11.755 101.588,11.482 101.499,11.214C101.419,10.936 101.295,10.687 101.126,10.469C100.958,10.25 100.744,10.076 100.486,9.947C100.227,9.808 99.924,9.738 99.576,9.738C99.249,9.738 98.956,9.808 98.697,9.947C98.572,10.015 98.454,10.092 98.345,10.178L97.736,9.185ZM92.199,15.64H93.898V8.486H92.11V12.421C92.11,12.659 92.075,12.898 92.005,13.136C91.936,13.365 91.832,13.568 91.692,13.747C91.563,13.926 91.389,14.075 91.171,14.194C90.962,14.304 90.719,14.358 90.441,14.358C90.152,14.358 89.919,14.299 89.74,14.179C89.571,14.06 89.437,13.911 89.338,13.732C89.239,13.543 89.174,13.335 89.144,13.106C89.114,12.878 89.099,12.659 89.099,12.451V8.486H87.311V12.957C87.311,13.325 87.361,13.683 87.46,14.03C87.559,14.378 87.713,14.686 87.922,14.954C88.141,15.222 88.414,15.441 88.742,15.61C89.079,15.769 89.482,15.849 89.949,15.849C90.485,15.849 90.947,15.714 91.335,15.446C91.732,15.168 92.01,14.85 92.169,14.492H92.199V15.64Z"
      android:strokeAlpha="0.63"
      android:fillColor="@color/color_tertiary"
      android:fillType="evenOdd"
      android:fillAlpha="0.63"/>
  <path
      android:pathData="M97.736,9.185C97.771,9.146 97.809,9.107 97.848,9.068C97.997,8.919 98.176,8.79 98.384,8.68C98.593,8.561 98.826,8.467 99.085,8.397C99.353,8.318 99.651,8.278 99.979,8.278C100.486,8.278 100.953,8.377 101.38,8.576C101.807,8.765 102.175,9.028 102.483,9.366C102.791,9.704 103.029,10.101 103.198,10.558C103.367,11.015 103.451,11.507 103.451,12.033C103.451,12.56 103.367,13.057 103.198,13.524C103.039,13.981 102.805,14.383 102.497,14.731C102.199,15.068 101.832,15.342 101.395,15.55C100.967,15.749 100.481,15.849 99.934,15.849C99.427,15.849 98.961,15.744 98.533,15.535C98.116,15.327 97.793,15.044 97.565,14.686H97.535V19.216H95.746V11.914H94.639L96.54,8.475L96.785,8.874L96.784,8.875L98.014,10.882L98.017,10.88L98.652,11.914H97.479C97.477,11.964 97.475,12.013 97.475,12.063C97.475,12.341 97.52,12.62 97.609,12.898C97.709,13.166 97.848,13.409 98.027,13.628C98.215,13.847 98.439,14.025 98.697,14.164C98.956,14.294 99.249,14.358 99.576,14.358C99.924,14.358 100.227,14.289 100.486,14.149C100.744,14.01 100.958,13.832 101.126,13.613C101.295,13.394 101.419,13.146 101.499,12.868C101.588,12.59 101.633,12.311 101.633,12.033C101.633,11.755 101.588,11.482 101.499,11.214C101.419,10.936 101.295,10.687 101.126,10.469C100.958,10.25 100.744,10.076 100.486,9.947C100.227,9.808 99.924,9.738 99.576,9.738C99.249,9.738 98.956,9.808 98.697,9.947C98.572,10.015 98.454,10.092 98.345,10.178L97.736,9.185ZM92.199,15.64H93.898V8.486H92.11V12.421C92.11,12.659 92.075,12.898 92.005,13.136C91.936,13.365 91.832,13.568 91.692,13.747C91.563,13.926 91.389,14.075 91.171,14.194C90.962,14.304 90.719,14.358 90.441,14.358C90.152,14.358 89.919,14.299 89.74,14.179C89.571,14.06 89.437,13.911 89.338,13.732C89.239,13.543 89.174,13.335 89.144,13.106C89.114,12.878 89.099,12.659 89.099,12.451V8.486H87.311V12.957C87.311,13.325 87.361,13.683 87.46,14.03C87.559,14.378 87.713,14.686 87.922,14.954C88.141,15.222 88.414,15.441 88.742,15.61C89.079,15.769 89.482,15.849 89.949,15.849C90.485,15.849 90.947,15.714 91.335,15.446C91.732,15.168 92.01,14.85 92.169,14.492H92.199V15.64Z"
      android:strokeAlpha="0.63"
      android:fillType="evenOdd"
      android:fillAlpha="0.63">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="8.27783"
          android:startX="95.3811"
          android:endY="19.2165"
          android:endX="95.3811"
          android:type="linear">
        <item android:offset="0" android:color="@color/color_tertiary"/>
        <item android:offset="1" android:color="@color/color_tertiary"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M55.087,22.787L55.087,22.787A0.826,0.826 114.753,0 0,55.577 21.726L54.055,17.602A0.826,0.826 114.753,0 0,52.994 17.113L52.994,17.113A0.826,0.826 114.753,0 0,52.505 18.174L54.027,22.298A0.826,0.826 114.753,0 0,55.087 22.787z"
      android:fillColor="@color/color_primary"/>
  <path
      android:pathData="M114.742,17.704L114.742,17.704A0.826,0.826 61.667,0 1,114.187 16.676L115.448,12.465A0.826,0.826 61.667,0 1,116.476 11.911L116.476,11.911A0.826,0.826 61.667,0 1,117.031 12.939L115.77,17.15A0.826,0.826 61.667,0 1,114.742 17.704z"
      android:fillColor="@color/color_primary"/>
  <path
      android:pathData="M74.225,17.704L74.225,17.704A0.826,0.826 118.333,0 0,74.779 16.676L73.519,12.465A0.826,0.826 118.333,0 0,72.49 11.911L72.49,11.911A0.826,0.826 118.333,0 0,71.936 12.939L73.197,17.15A0.826,0.826 118.333,0 0,74.225 17.704z"
      android:fillColor="@color/color_primary"/>
  <path
      android:pathData="M11.876,85.932C11.558,85.897 11.234,85.879 10.905,85.879C6.545,85.879 3.01,89.023 3.01,92.903C3.01,93.129 3.022,93.353 3.046,93.573C3.151,94.559 2.278,95.41 1.165,95.41C0.522,95.41 0,95.874 0,96.447C0,97.019 0.522,97.483 1.165,97.483H47.135C47.778,97.483 48.3,97.019 48.3,96.447C48.3,95.874 47.778,95.41 47.135,95.41H45.377C43.69,95.41 42.314,94.22 42.255,92.72C42.254,92.699 42.253,92.678 42.252,92.657C42.117,90.111 39.843,88.023 36.986,87.826C36.155,87.769 35.359,87.872 34.629,88.101C33.56,83.194 28.705,79.489 22.877,79.489C17.945,79.489 13.71,82.142 11.876,85.932Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M11.876,85.932C11.558,85.897 11.234,85.879 10.905,85.879C6.545,85.879 3.01,89.023 3.01,92.903C3.01,93.129 3.022,93.353 3.046,93.573C3.151,94.559 2.278,95.41 1.165,95.41C0.522,95.41 0,95.874 0,96.447C0,97.019 0.522,97.483 1.165,97.483H47.135C47.778,97.483 48.3,97.019 48.3,96.447C48.3,95.874 47.778,95.41 47.135,95.41H45.377C43.69,95.41 42.314,94.22 42.255,92.72C42.254,92.699 42.253,92.678 42.252,92.657C42.117,90.111 39.843,88.023 36.986,87.826C36.155,87.769 35.359,87.872 34.629,88.101C33.56,83.194 28.705,79.489 22.877,79.489C17.945,79.489 13.71,82.142 11.876,85.932Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="97.4829"
          android:startX="24.1498"
          android:endY="79.4885"
          android:endX="24.1498"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M184.215,49.081C184.618,49.037 185.029,49.014 185.445,49.014C190.965,49.014 195.44,52.995 195.44,57.906C195.44,58.192 195.425,58.475 195.395,58.755C195.261,60.002 196.367,61.08 197.776,61.08C198.59,61.08 199.25,61.667 199.25,62.392C199.25,63.117 198.59,63.704 197.776,63.704H139.578C138.764,63.704 138.104,63.117 138.104,62.392C138.104,61.667 138.764,61.08 139.578,61.08H141.804C143.939,61.08 145.681,59.573 145.756,57.675C145.757,57.648 145.758,57.621 145.76,57.595C145.931,54.371 148.81,51.728 152.426,51.479C153.479,51.406 154.487,51.536 155.41,51.826C156.764,45.614 162.91,40.923 170.288,40.923C176.532,40.923 181.894,44.283 184.215,49.081Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M184.215,49.081C184.618,49.037 185.029,49.014 185.445,49.014C190.965,49.014 195.44,52.995 195.44,57.906C195.44,58.192 195.425,58.475 195.395,58.755C195.261,60.002 196.367,61.08 197.776,61.08C198.59,61.08 199.25,61.667 199.25,62.392C199.25,63.117 198.59,63.704 197.776,63.704H139.578C138.764,63.704 138.104,63.117 138.104,62.392C138.104,61.667 138.764,61.08 139.578,61.08H141.804C143.939,61.08 145.681,59.573 145.756,57.675C145.757,57.648 145.758,57.621 145.76,57.595C145.931,54.371 148.81,51.728 152.426,51.479C153.479,51.406 154.487,51.536 155.41,51.826C156.764,45.614 162.91,40.923 170.288,40.923C176.532,40.923 181.894,44.283 184.215,49.081Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="63.7041"
          android:startX="168.677"
          android:endY="40.9233"
          android:endX="168.677"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M174.308,88.352C174.561,88.325 174.818,88.31 175.08,88.31C178.545,88.31 181.353,90.809 181.353,93.892C181.353,94.071 181.344,94.249 181.325,94.425C181.241,95.208 181.935,95.884 182.819,95.884C183.331,95.884 183.745,96.253 183.745,96.708C183.745,97.163 183.331,97.531 182.819,97.531H146.29C145.779,97.531 145.364,97.163 145.364,96.708C145.364,96.253 145.779,95.884 146.29,95.884H147.687C149.027,95.884 150.121,94.938 150.168,93.747C150.168,93.73 150.169,93.713 150.17,93.697C150.277,91.673 152.084,90.014 154.354,89.857C155.015,89.812 155.648,89.894 156.227,90.076C157.077,86.176 160.935,83.232 165.566,83.232C169.485,83.232 172.851,85.341 174.308,88.352Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M174.308,88.352C174.561,88.325 174.818,88.31 175.08,88.31C178.545,88.31 181.353,90.809 181.353,93.892C181.353,94.071 181.344,94.249 181.325,94.425C181.241,95.208 181.935,95.884 182.819,95.884C183.331,95.884 183.745,96.253 183.745,96.708C183.745,97.163 183.331,97.531 182.819,97.531H146.29C145.779,97.531 145.364,97.163 145.364,96.708C145.364,96.253 145.779,95.884 146.29,95.884H147.687C149.027,95.884 150.121,94.938 150.168,93.747C150.168,93.73 150.169,93.713 150.17,93.697C150.277,91.673 152.084,90.014 154.354,89.857C155.015,89.812 155.648,89.894 156.227,90.076C157.077,86.176 160.935,83.232 165.566,83.232C169.485,83.232 172.851,85.341 174.308,88.352Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="97.5312"
          android:startX="164.555"
          android:endY="83.2322"
          android:endX="164.555"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M34.805,146.129a60.337,11.871 0,1 0,120.674 0a60.337,11.871 0,1 0,-120.674 0z"
      android:fillColor="#EBEBEB"/>
  <path
      android:pathData="M42.256,139.552L51.677,115.969L139.025,115.73L148.49,139.552H42.256Z"
      android:fillColor="@color/color_tertiary"/>
  <path
      android:pathData="M51.434,117.119a43.948,11.688 0,1 0,87.896 0a43.948,11.688 0,1 0,-87.896 0z"
      android:fillColor="@color/color_tertiary_lighter"/>
  <path
      android:pathData="M65.927,36.453h58.903v79.731h-58.903z"
      android:strokeAlpha="0.8"
      android:fillColor="#ffffff"
      android:fillAlpha="0.8"/>
  <path
      android:pathData="M42.083,140.496a53.299,11.688 0,1 0,106.597 0a53.299,11.688 0,1 0,-106.597 0z"
      android:fillColor="@color/color_tertiary"/>
  <path
      android:pathData="M97.736,137.095C97.771,137.056 97.809,137.017 97.848,136.978C97.997,136.829 98.176,136.7 98.384,136.59C98.593,136.471 98.826,136.377 99.085,136.307C99.353,136.228 99.651,136.188 99.979,136.188C100.486,136.188 100.953,136.287 101.38,136.486C101.807,136.675 102.175,136.938 102.483,137.276C102.791,137.614 103.029,138.011 103.198,138.468C103.367,138.925 103.451,139.417 103.451,139.943C103.451,140.47 103.367,140.967 103.198,141.434C103.039,141.891 102.805,142.293 102.497,142.641C102.199,142.979 101.832,143.252 101.395,143.461C100.967,143.659 100.481,143.759 99.934,143.759C99.427,143.759 98.961,143.654 98.533,143.446C98.116,143.237 97.793,142.954 97.565,142.596H97.535V147.127H95.746V139.824H94.639L96.54,136.385L96.785,136.784L96.784,136.785L98.014,138.792L98.017,138.79L98.652,139.824H97.479C97.477,139.874 97.475,139.924 97.475,139.973C97.475,140.251 97.52,140.53 97.609,140.808C97.709,141.076 97.848,141.32 98.027,141.538C98.215,141.757 98.439,141.936 98.697,142.075C98.956,142.204 99.249,142.268 99.576,142.268C99.924,142.268 100.227,142.199 100.486,142.06C100.744,141.921 100.958,141.742 101.126,141.523C101.295,141.305 101.419,141.056 101.499,140.778C101.588,140.5 101.633,140.222 101.633,139.943C101.633,139.665 101.588,139.392 101.499,139.124C101.419,138.846 101.295,138.597 101.126,138.379C100.958,138.16 100.744,137.986 100.486,137.857C100.227,137.718 99.924,137.648 99.576,137.648C99.249,137.648 98.956,137.718 98.697,137.857C98.572,137.925 98.454,138.002 98.345,138.088L97.736,137.095ZM92.199,143.55H93.898V136.397H92.11V140.331C92.11,140.569 92.075,140.808 92.005,141.046C91.936,141.275 91.832,141.478 91.692,141.657C91.563,141.836 91.389,141.985 91.171,142.104C90.962,142.214 90.719,142.268 90.441,142.268C90.152,142.268 89.919,142.209 89.74,142.09C89.571,141.97 89.437,141.821 89.338,141.642C89.239,141.454 89.174,141.245 89.144,141.016C89.114,140.788 89.099,140.569 89.099,140.361V136.397H87.311V140.867C87.311,141.235 87.361,141.593 87.46,141.94C87.559,142.288 87.713,142.596 87.922,142.864C88.141,143.133 88.414,143.351 88.742,143.52C89.079,143.679 89.482,143.759 89.949,143.759C90.485,143.759 90.947,143.624 91.335,143.356C91.732,143.078 92.01,142.76 92.169,142.402H92.199V143.55Z"
      android:strokeAlpha="0.63"
      android:fillColor="@color/color_tertiary"
      android:fillType="evenOdd"
      android:fillAlpha="0.63"/>
  <path
      android:pathData="M97.736,137.095C97.771,137.056 97.809,137.017 97.848,136.978C97.997,136.829 98.176,136.7 98.384,136.59C98.593,136.471 98.826,136.377 99.085,136.307C99.353,136.228 99.651,136.188 99.979,136.188C100.486,136.188 100.953,136.287 101.38,136.486C101.807,136.675 102.175,136.938 102.483,137.276C102.791,137.614 103.029,138.011 103.198,138.468C103.367,138.925 103.451,139.417 103.451,139.943C103.451,140.47 103.367,140.967 103.198,141.434C103.039,141.891 102.805,142.293 102.497,142.641C102.199,142.979 101.832,143.252 101.395,143.461C100.967,143.659 100.481,143.759 99.934,143.759C99.427,143.759 98.961,143.654 98.533,143.446C98.116,143.237 97.793,142.954 97.565,142.596H97.535V147.127H95.746V139.824H94.639L96.54,136.385L96.785,136.784L96.784,136.785L98.014,138.792L98.017,138.79L98.652,139.824H97.479C97.477,139.874 97.475,139.924 97.475,139.973C97.475,140.251 97.52,140.53 97.609,140.808C97.709,141.076 97.848,141.32 98.027,141.538C98.215,141.757 98.439,141.936 98.697,142.075C98.956,142.204 99.249,142.268 99.576,142.268C99.924,142.268 100.227,142.199 100.486,142.06C100.744,141.921 100.958,141.742 101.126,141.523C101.295,141.305 101.419,141.056 101.499,140.778C101.588,140.5 101.633,140.222 101.633,139.943C101.633,139.665 101.588,139.392 101.499,139.124C101.419,138.846 101.295,138.597 101.126,138.379C100.958,138.16 100.744,137.986 100.486,137.857C100.227,137.718 99.924,137.648 99.576,137.648C99.249,137.648 98.956,137.718 98.697,137.857C98.572,137.925 98.454,138.002 98.345,138.088L97.736,137.095ZM92.199,143.55H93.898V136.397H92.11V140.331C92.11,140.569 92.075,140.808 92.005,141.046C91.936,141.275 91.832,141.478 91.692,141.657C91.563,141.836 91.389,141.985 91.171,142.104C90.962,142.214 90.719,142.268 90.441,142.268C90.152,142.268 89.919,142.209 89.74,142.09C89.571,141.97 89.437,141.821 89.338,141.642C89.239,141.454 89.174,141.245 89.144,141.016C89.114,140.788 89.099,140.569 89.099,140.361V136.397H87.311V140.867C87.311,141.235 87.361,141.593 87.46,141.94C87.559,142.288 87.713,142.596 87.922,142.864C88.141,143.133 88.414,143.351 88.742,143.52C89.079,143.679 89.482,143.759 89.949,143.759C90.485,143.759 90.947,143.624 91.335,143.356C91.732,143.078 92.01,142.76 92.169,142.402H92.199V143.55Z"
      android:strokeAlpha="0.63"
      android:fillType="evenOdd"
      android:fillAlpha="0.63">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="136.188"
          android:startX="95.3811"
          android:endY="147.127"
          android:endX="95.3811"
          android:type="linear">
        <item android:offset="0" android:color="@color/color_tertiary"/>
        <item android:offset="1" android:color="@color/color_tertiary"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M65.927,116.184a29.455,6.545 0,1 0,58.909 0a29.455,6.545 0,1 0,-58.909 0z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M65.927,116.184a29.455,6.545 0,1 0,58.909 0a29.455,6.545 0,1 0,-58.909 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="109.639"
          android:startX="95.3813"
          android:endY="122.73"
          android:endX="95.3813"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M55.721,129.397L55.721,129.397A0.826,0.826 65.247,0 1,56.21 130.458L54.689,134.582A0.826,0.826 65.247,0 1,53.628 135.071L53.628,135.071A0.826,0.826 65.247,0 1,53.139 134.01L54.66,129.886A0.826,0.826 65.247,0 1,55.721 129.397z"
      android:fillColor="@color/color_primary"/>
  <path
      android:pathData="M134.511,129.397L134.511,129.397A0.826,0.826 114.753,0 0,134.022 130.458L135.543,134.582A0.826,0.826 114.753,0 0,136.604 135.071L136.604,135.071A0.826,0.826 114.753,0 0,137.093 134.01L135.572,129.886A0.826,0.826 114.753,0 0,134.511 129.397z"
      android:fillColor="@color/color_primary"/>
  <path
      android:pathData="M115.375,134.481L115.375,134.481A0.826,0.826 118.333,0 0,114.82 135.509L116.081,139.72A0.826,0.826 118.333,0 0,117.109 140.275L117.109,140.275A0.826,0.826 118.333,0 0,117.664 139.247L116.403,135.035A0.826,0.826 118.333,0 0,115.375 134.481z"
      android:fillColor="@color/color_primary"/>
  <path
      android:pathData="M74.858,134.481L74.858,134.481A0.826,0.826 61.667,0 1,75.412 135.509L74.151,139.72A0.826,0.826 61.667,0 1,73.123 140.275L73.123,140.275A0.826,0.826 61.667,0 1,72.569 139.247L73.829,135.035A0.826,0.826 61.667,0 1,74.858 134.481z"
      android:fillColor="@color/color_primary"/>
  <path
      android:pathData="M73.337,75.74L73.337,75.74A0.651,0.651 0,0 1,73.989 76.392L73.989,101.517A0.651,0.651 0,0 1,73.337 102.169L73.337,102.169A0.651,0.651 0,0 1,72.686 101.517L72.686,76.392A0.651,0.651 0,0 1,73.337 75.74z"
      android:strokeAlpha="0.5"
      android:fillAlpha="0.5">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="75.7402"
          android:startX="73.3375"
          android:endY="102.169"
          android:endX="73.3375"
          android:type="linear">
        <item android:offset="0.0277786" android:color="#00FF5A5F"/>
        <item android:offset="0.140767" android:color="#99FF5A5F"/>
        <item android:offset="0.557953" android:color="#FFFF5A5F"/>
        <item android:offset="0.870843" android:color="#B2FF5A5F"/>
        <item android:offset="0.983831" android:color="#00FF5A5F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M73.337,38.337L73.337,38.337A0.651,0.651 0,0 1,73.989 38.989L73.989,67.856A0.651,0.651 0,0 1,73.337 68.507L73.337,68.507A0.651,0.651 0,0 1,72.686 67.856L72.686,38.989A0.651,0.651 0,0 1,73.337 38.337z"
      android:strokeAlpha="0.2"
      android:fillAlpha="0.2">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="38.3374"
          android:startX="73.3375"
          android:endY="68.5072"
          android:endX="73.3375"
          android:type="linear">
        <item android:offset="0.0277786" android:color="#00FF5A5F"/>
        <item android:offset="0.140767" android:color="#99FF5A5F"/>
        <item android:offset="0.557953" android:color="#FFFF5A5F"/>
        <item android:offset="0.870843" android:color="#B2FF5A5F"/>
        <item android:offset="0.983831" android:color="#00FF5A5F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M80.818,68.259L80.818,68.259A0.651,0.651 0,0 1,81.47 68.911L81.47,86.557A0.651,0.651 0,0 1,80.818 87.208L80.818,87.208A0.651,0.651 0,0 1,80.167 86.557L80.167,68.911A0.651,0.651 0,0 1,80.818 68.259z"
      android:strokeAlpha="0.5"
      android:fillAlpha="0.5">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="68.2593"
          android:startX="80.8184"
          android:endY="87.2083"
          android:endX="80.8184"
          android:type="linear">
        <item android:offset="0.0277786" android:color="#00FF5A5F"/>
        <item android:offset="0.140767" android:color="#99FF5A5F"/>
        <item android:offset="0.557953" android:color="#FFFF5A5F"/>
        <item android:offset="0.870843" android:color="#B2FF5A5F"/>
        <item android:offset="0.983831" android:color="#00FF5A5F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M102.7,48.464L102.7,48.464A0.651,0.651 0,0 1,103.352 49.116L103.352,66.762A0.651,0.651 0,0 1,102.7 67.413L102.7,67.413A0.651,0.651 0,0 1,102.049 66.762L102.049,49.116A0.651,0.651 0,0 1,102.7 48.464z"
      android:strokeAlpha="0.5"
      android:fillAlpha="0.5">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="48.4644"
          android:startX="102.7"
          android:endY="67.4134"
          android:endX="102.7"
          android:type="linear">
        <item android:offset="0.0277786" android:color="#00FF5A5F"/>
        <item android:offset="0.140767" android:color="#99FF5A5F"/>
        <item android:offset="0.557953" android:color="#FFFF5A5F"/>
        <item android:offset="0.870843" android:color="#B2FF5A5F"/>
        <item android:offset="0.983831" android:color="#00FF5A5F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M89.89,88.954L89.89,88.954A0.651,0.651 0,0 1,90.542 89.606L90.542,114.732A0.651,0.651 0,0 1,89.89 115.384L89.89,115.384A0.651,0.651 0,0 1,89.239 114.732L89.239,89.606A0.651,0.651 0,0 1,89.89 88.954z"
      android:strokeAlpha="0.3"
      android:fillAlpha="0.3">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="88.9541"
          android:startX="89.8902"
          android:endY="115.384"
          android:endX="89.8902"
          android:type="linear">
        <item android:offset="0.0277786" android:color="#00FF5A5F"/>
        <item android:offset="0.140767" android:color="#99FF5A5F"/>
        <item android:offset="0.557953" android:color="#FFFF5A5F"/>
        <item android:offset="0.870843" android:color="#B2FF5A5F"/>
        <item android:offset="0.983831" android:color="#00FF5A5F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M89.89,51.513L89.89,51.513A0.651,0.651 0,0 1,90.542 52.165L90.542,77.291A0.651,0.651 0,0 1,89.89 77.943L89.89,77.943A0.651,0.651 0,0 1,89.239 77.291L89.239,52.165A0.651,0.651 0,0 1,89.89 51.513z"
      android:strokeAlpha="0.4"
      android:fillAlpha="0.4">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="51.5132"
          android:startX="89.8902"
          android:endY="77.9428"
          android:endX="89.8902"
          android:type="linear">
        <item android:offset="0.0277786" android:color="#00FF5A5F"/>
        <item android:offset="0.140767" android:color="#99FF5A5F"/>
        <item android:offset="0.557953" android:color="#FFFF5A5F"/>
        <item android:offset="0.870843" android:color="#B2FF5A5F"/>
        <item android:offset="0.983831" android:color="#00FF5A5F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M102.7,74.794L102.7,74.794A0.651,0.651 0,0 1,103.352 75.445L103.352,108.052A0.651,0.651 0,0 1,102.7 108.704L102.7,108.704A0.651,0.651 0,0 1,102.049 108.052L102.049,75.445A0.651,0.651 0,0 1,102.7 74.794z"
      android:strokeAlpha="0.4"
      android:fillAlpha="0.4">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="74.7935"
          android:startX="102.7"
          android:endY="108.704"
          android:endX="102.7"
          android:type="linear">
        <item android:offset="0.0277786" android:color="#00FF5A5F"/>
        <item android:offset="0.140767" android:color="#99FF5A5F"/>
        <item android:offset="0.557953" android:color="#FFFF5A5F"/>
        <item android:offset="0.870843" android:color="#B2FF5A5F"/>
        <item android:offset="0.983831" android:color="#00FF5A5F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M115.51,86.015L115.51,86.015A0.651,0.651 0,0 1,116.162 86.666L116.162,108.052A0.651,0.651 0,0 1,115.51 108.704L115.51,108.704A0.651,0.651 0,0 1,114.859 108.052L114.859,86.666A0.651,0.651 0,0 1,115.51 86.015z"
      android:strokeAlpha="0.5"
      android:fillAlpha="0.5">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="86.0146"
          android:startX="115.511"
          android:endY="108.704"
          android:endX="115.511"
          android:type="linear">
        <item android:offset="0.0277786" android:color="#00FF5A5F"/>
        <item android:offset="0.140767" android:color="#99FF5A5F"/>
        <item android:offset="0.557953" android:color="#FFFF5A5F"/>
        <item android:offset="0.870843" android:color="#B2FF5A5F"/>
        <item android:offset="0.983831" android:color="#00FF5A5F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M115.51,59.833L115.51,59.833A0.651,0.651 0,0 1,116.162 60.484L116.162,81.87A0.651,0.651 0,0 1,115.51 82.522L115.51,82.522A0.651,0.651 0,0 1,114.859 81.87L114.859,60.484A0.651,0.651 0,0 1,115.51 59.833z"
      android:strokeAlpha="0.4"
      android:fillAlpha="0.4">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="59.8325"
          android:startX="115.511"
          android:endY="82.5218"
          android:endX="115.511"
          android:type="linear">
        <item android:offset="0.0277786" android:color="#00FF5A5F"/>
        <item android:offset="0.140767" android:color="#99FF5A5F"/>
        <item android:offset="0.557953" android:color="#FFFF5A5F"/>
        <item android:offset="0.870843" android:color="#B2FF5A5F"/>
        <item android:offset="0.983831" android:color="#00FF5A5F"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
