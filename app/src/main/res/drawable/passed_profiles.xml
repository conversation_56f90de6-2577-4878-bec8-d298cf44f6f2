<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="87dp"
    android:height="86dp"
    android:viewportWidth="87"
    android:viewportHeight="86">
  <group>
    <clip-path
        android:pathData="M0.5,0.454h85.95v84.889h-85.95z"/>
    <path
        android:pathData="M0.5,0.454h85.95v84.889h-85.95z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M43.715,42.647m-42.192,0a42.192,42.192 0,1 1,84.385 0a42.192,42.192 0,1 1,-84.385 0"
        android:fillColor="#FBECDD"/>
    <path
        android:pathData="M-11.354,47.884C-11.548,47.862 -11.746,47.851 -11.946,47.851C-14.607,47.851 -16.763,49.77 -16.763,52.137C-16.763,52.275 -16.756,52.411 -16.741,52.546C-16.677,53.147 -17.21,53.667 -17.889,53.667C-18.281,53.667 -18.6,53.95 -18.6,54.299C-18.6,54.648 -18.281,54.931 -17.889,54.931H10.158C10.551,54.931 10.869,54.648 10.869,54.299C10.869,53.95 10.551,53.667 10.158,53.667H9.086C8.057,53.667 7.217,52.94 7.181,52.026C7.181,52.013 7.18,52 7.179,51.987C7.097,50.433 5.709,49.16 3.966,49.039C3.459,49.004 2.973,49.067 2.528,49.207C1.876,46.213 -1.086,43.952 -4.642,43.952C-7.651,43.952 -10.235,45.571 -11.354,47.884Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M-11.354,47.884C-11.548,47.862 -11.746,47.851 -11.946,47.851C-14.607,47.851 -16.763,49.77 -16.763,52.137C-16.763,52.275 -16.756,52.411 -16.741,52.546C-16.677,53.147 -17.21,53.667 -17.889,53.667C-18.281,53.667 -18.6,53.95 -18.6,54.299C-18.6,54.648 -18.281,54.931 -17.889,54.931H10.158C10.551,54.931 10.869,54.648 10.869,54.299C10.869,53.95 10.551,53.667 10.158,53.667H9.086C8.057,53.667 7.217,52.94 7.181,52.026C7.181,52.013 7.18,52 7.179,51.987C7.097,50.433 5.709,49.16 3.966,49.039C3.459,49.004 2.973,49.067 2.528,49.207C1.876,46.213 -1.086,43.952 -4.642,43.952C-7.651,43.952 -10.235,45.571 -11.354,47.884Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="-3.865"
            android:startY="54.931"
            android:endX="-3.865"
            android:endY="43.952"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M93.794,25.4C94.039,25.373 94.29,25.359 94.544,25.359C97.912,25.359 100.642,27.788 100.642,30.784C100.642,30.959 100.633,31.132 100.614,31.302C100.533,32.063 101.207,32.721 102.067,32.721C102.564,32.721 102.967,33.079 102.967,33.521C102.967,33.963 102.564,34.322 102.067,34.322H66.56C66.063,34.322 65.66,33.963 65.66,33.521C65.66,33.079 66.063,32.721 66.56,32.721H67.918C69.22,32.721 70.283,31.801 70.329,30.643C70.329,30.627 70.33,30.611 70.331,30.594C70.436,28.628 72.192,27.015 74.398,26.863C75.041,26.819 75.656,26.898 76.219,27.075C77.045,23.285 80.795,20.423 85.297,20.423C89.106,20.423 92.377,22.472 93.794,25.4Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M93.794,25.4C94.039,25.373 94.29,25.359 94.544,25.359C97.912,25.359 100.642,27.788 100.642,30.784C100.642,30.959 100.633,31.132 100.614,31.302C100.533,32.063 101.207,32.721 102.067,32.721C102.564,32.721 102.967,33.079 102.967,33.521C102.967,33.963 102.564,34.322 102.067,34.322H66.56C66.063,34.322 65.66,33.963 65.66,33.521C65.66,33.079 66.063,32.721 66.56,32.721H67.918C69.22,32.721 70.283,31.801 70.329,30.643C70.329,30.627 70.33,30.611 70.331,30.594C70.436,28.628 72.192,27.015 74.398,26.863C75.041,26.819 75.656,26.898 76.219,27.075C77.045,23.285 80.795,20.423 85.297,20.423C89.106,20.423 92.377,22.472 93.794,25.4Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="84.313"
            android:startY="34.322"
            android:endX="84.313"
            android:endY="20.423"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M87.748,49.361C87.902,49.344 88.059,49.335 88.219,49.335C90.333,49.335 92.047,50.859 92.047,52.74C92.047,52.85 92.041,52.958 92.029,53.065C91.978,53.543 92.402,53.956 92.941,53.956C93.253,53.956 93.506,54.181 93.506,54.458C93.506,54.736 93.253,54.961 92.941,54.961H70.654C70.342,54.961 70.089,54.736 70.089,54.458C70.089,54.181 70.342,53.956 70.654,53.956H71.506C72.324,53.956 72.991,53.379 73.02,52.652C73.02,52.641 73.02,52.631 73.021,52.621C73.087,51.387 74.189,50.374 75.574,50.279C75.977,50.251 76.363,50.301 76.717,50.412C77.235,48.033 79.589,46.237 82.414,46.237C84.806,46.237 86.859,47.523 87.748,49.361Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M87.748,49.361C87.902,49.344 88.059,49.335 88.219,49.335C90.333,49.335 92.047,50.859 92.047,52.74C92.047,52.85 92.041,52.958 92.029,53.065C91.978,53.543 92.402,53.956 92.941,53.956C93.253,53.956 93.506,54.181 93.506,54.458C93.506,54.736 93.253,54.961 92.941,54.961H70.654C70.342,54.961 70.089,54.736 70.089,54.458C70.089,54.181 70.342,53.956 70.654,53.956H71.506C72.324,53.956 72.991,53.379 73.02,52.652C73.02,52.641 73.02,52.631 73.021,52.621C73.087,51.387 74.189,50.374 75.574,50.279C75.977,50.251 76.363,50.301 76.717,50.412C77.235,48.033 79.589,46.237 82.414,46.237C84.806,46.237 86.859,47.523 87.748,49.361Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="81.797"
            android:startY="54.961"
            android:endX="81.797"
            android:endY="46.237"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M43.313,30.072V42.747L50.917,47.816"
        android:strokeLineJoin="round"
        android:strokeWidth="5.06976"
        android:fillColor="#00000000"
        android:strokeColor="@color/color_primary"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M70.597,46.171L65.529,41.101L60.458,46.171M66.288,42.369C66.288,54.968 56.074,65.183 43.474,65.183C30.874,65.183 20.66,54.968 20.66,42.369C20.66,29.769 30.874,19.555 43.474,19.555C51.844,19.555 59.161,24.062 63.131,30.782"
        android:strokeLineJoin="round"
        android:strokeWidth="5.06976"
        android:fillColor="#00000000"
        android:strokeColor="@color/color_primary"
        android:strokeLineCap="round"/>
  </group>
</vector>
