<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="30dp"
    android:height="30dp"
    android:viewportWidth="30"
    android:viewportHeight="30">
  <path
      android:pathData="M20.9375,10.176V10.2C15.274,10.2 10.6667,13.968 10.6667,18.6C10.6667,19.116 10.7307,19.656 10.8552,20.184C10.8987,20.376 10.8479,20.568 10.7174,20.724C10.5881,20.868 10.4032,20.94 10.1978,20.916C9.2638,20.808 8.3636,20.604 7.5177,20.304L4.3205,22.116C4.2275,22.176 4.1235,22.2 4.0208,22.2C3.8698,22.2 3.7212,22.14 3.6052,22.032C3.4118,21.852 3.3611,21.564 3.4807,21.336L4.7289,18.852C2.3521,17.136 1,14.664 1,12C1,7.032 5.7427,3 11.5729,3C16.2153,3 20.2379,5.508 21.642,9.264C21.7132,9.36 21.7543,9.48 21.7543,9.612C21.7543,9.936 21.4837,10.212 21.1502,10.212H21.126H21.1139L21.1139,10.212C21.0535,10.2 20.9931,10.188 20.9375,10.176ZM11.875,18.6C11.875,14.628 15.9398,11.4 20.9375,11.4C25.9352,11.4 30,14.628 30,18.6C30,20.736 28.7832,22.776 26.7194,24.144L27.5398,26.172C27.6329,26.412 27.5725,26.676 27.3888,26.844C27.274,26.94 27.1278,27 26.9792,27C26.8873,27 26.7955,26.976 26.7085,26.94L23.713,25.452C22.8044,25.68 21.8715,25.8 20.9375,25.8C15.9398,25.8 11.875,22.572 11.875,18.6Z"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.75"
      android:fillType="evenOdd"/>
  <group>
    <clip-path android:pathData="M20.9375,10.176V10.2C15.274,10.2 10.6667,13.968 10.6667,18.6C10.6667,19.116 10.7307,19.656 10.8552,20.184C10.8987,20.376 10.8479,20.568 10.7174,20.724C10.5881,20.868 10.4032,20.94 10.1978,20.916C9.2638,20.808 8.3636,20.604 7.5177,20.304L4.3205,22.116C4.2275,22.176 4.1235,22.2 4.0208,22.2C3.8698,22.2 3.7212,22.14 3.6052,22.032C3.4118,21.852 3.3611,21.564 3.4807,21.336L4.7289,18.852C2.3521,17.136 1,14.664 1,12C1,7.032 5.7427,3 11.5729,3C16.2153,3 20.2379,5.508 21.642,9.264C21.7132,9.36 21.7543,9.48 21.7543,9.612C21.7543,9.936 21.4837,10.212 21.1502,10.212H21.126H21.1139L21.1139,10.212C21.0535,10.2 20.9931,10.188 20.9375,10.176ZM11.875,18.6C11.875,14.628 15.9398,11.4 20.9375,11.4C25.9352,11.4 30,14.628 30,18.6C30,20.736 28.7832,22.776 26.7194,24.144L27.5398,26.172C27.6329,26.412 27.5725,26.676 27.3888,26.844C27.274,26.94 27.1278,27 26.9792,27C26.8873,27 26.7955,26.976 26.7085,26.94L23.713,25.452C22.8044,25.68 21.8715,25.8 20.9375,25.8C15.9398,25.8 11.875,22.572 11.875,18.6Z M 0,0"/>
    <path
        android:pathData="M20.9375,10.2V11.2H21.9375V10.2H20.9375ZM20.9375,10.176L21.1485,9.1985L19.9375,8.9371V10.176H20.9375ZM10.8552,20.184L11.8305,19.963L11.8285,19.9546L10.8552,20.184ZM10.7174,20.724L11.4615,21.3921L11.4732,21.3791L11.4844,21.3656L10.7174,20.724ZM10.1978,20.916L10.3139,19.9228L10.3127,19.9226L10.1978,20.916ZM7.5177,20.304L7.852,19.3615L7.4218,19.2089L7.0247,19.434L7.5177,20.304ZM4.3205,22.116L3.8274,21.246L3.8026,21.2601L3.7785,21.2756L4.3205,22.116ZM3.6052,22.032L2.9237,22.7639H2.9237L3.6052,22.032ZM3.4807,21.336L4.3662,21.8006L4.3703,21.7928L4.3742,21.785L3.4807,21.336ZM4.7289,18.852L5.6224,19.301L6.0049,18.5399L5.3143,18.0412L4.7289,18.852ZM21.642,9.264L20.7053,9.6142L20.7548,9.7466L20.8391,9.8602L21.642,9.264ZM21.1139,10.212L20.8714,11.1821L20.9908,11.212H21.1139V10.212ZM21.1139,10.212L21.3564,9.2418L21.3327,9.2359L21.3087,9.2311L21.1139,10.212ZM26.7194,24.144L26.1669,23.3105L25.486,23.7618L25.7924,24.519L26.7194,24.144ZM27.5398,26.172L28.4722,25.8105L28.4696,25.8037L28.4668,25.797L27.5398,26.172ZM27.3888,26.844L28.0303,27.6111L28.0473,27.5969L28.0637,27.5819L27.3888,26.844ZM26.7085,26.94L26.2636,27.8356L26.2944,27.8509L26.3261,27.864L26.7085,26.94ZM23.713,25.452L24.1579,24.5564L23.8275,24.3923L23.4697,24.4821L23.713,25.452ZM21.9375,10.2V10.176H19.9375V10.2H21.9375ZM11.6667,18.6C11.6667,14.6989 15.6289,11.2 20.9375,11.2V9.2C14.9192,9.2 9.6667,13.2371 9.6667,18.6H11.6667ZM11.8285,19.9546C11.7209,19.4983 11.6667,19.0353 11.6667,18.6H9.6667C9.6667,19.1967 9.7405,19.8137 9.8818,20.4134L11.8285,19.9546ZM11.4844,21.3656C11.8005,20.9879 11.9478,20.4809 11.8304,19.963L9.8799,20.405C9.8666,20.3463 9.8669,20.281 9.8831,20.2181C9.899,20.1563 9.9265,20.111 9.9504,20.0824L11.4844,21.3656ZM10.0818,21.9092C10.6054,21.9704 11.1124,21.7809 11.4615,21.3921L9.9733,20.0559C10.013,20.0117 10.0681,19.9717 10.1352,19.9466C10.2025,19.9214 10.2656,19.9171 10.3139,19.9228L10.0818,21.9092ZM7.1835,21.2465C8.1051,21.5734 9.0794,21.7933 10.083,21.9094L10.3127,19.9226C9.4482,19.8227 8.622,19.6346 7.852,19.3615L7.1835,21.2465ZM4.8136,22.986L8.0108,21.174L7.0247,19.434L3.8274,21.246L4.8136,22.986ZM4.0208,23.2C4.2797,23.2 4.58,23.1385 4.8625,22.9564L3.7785,21.2756C3.8749,21.2134 3.9674,21.2 4.0208,21.2V23.2ZM2.9237,22.7639C3.2151,23.0352 3.604,23.2 4.0208,23.2V21.2C4.1356,21.2 4.2272,21.2448 4.2866,21.3001L2.9237,22.7639ZM2.5952,20.8714C2.2605,21.5094 2.4094,22.285 2.9237,22.7639L4.2866,21.3001C4.4143,21.419 4.4617,21.6186 4.3662,21.8006L2.5952,20.8714ZM3.8354,18.403L2.5872,20.887L4.3742,21.785L5.6224,19.301L3.8354,18.403ZM0,12C0,15.0234 1.539,17.7823 4.1435,19.6628L5.3143,18.0412C3.1652,16.4897 2,14.3046 2,12H0ZM11.5729,2C5.3479,2 0,6.3344 0,12H2C2,7.7296 6.1375,4 11.5729,4V2ZM22.5786,8.9138C21.0012,4.6942 16.549,2 11.5729,2V4C15.8817,4 19.4745,6.3218 20.7053,9.6142L22.5786,8.9138ZM22.7543,9.612C22.7543,9.2688 22.6462,8.939 22.4448,8.6678L20.8391,9.8602C20.7803,9.781 20.7543,9.6912 20.7543,9.612H22.7543ZM21.1502,11.212C22.0372,11.212 22.7543,10.487 22.7543,9.612H20.7543C20.7543,9.385 20.9301,9.212 21.1502,9.212V11.212ZM21.126,11.212H21.1502V9.212H21.126V11.212ZM21.1139,11.212H21.126V9.212H21.1139V11.212ZM20.8714,11.1821L20.8714,11.1821L21.3565,9.2419L21.3564,9.2418L20.8714,11.1821ZM20.7265,11.1535C20.7916,11.1676 20.8605,11.1812 20.9191,11.1928L21.3087,9.2311C21.2465,9.2188 21.1945,9.2085 21.1485,9.1985L20.7265,11.1535ZM20.9375,10.4C15.6145,10.4 10.875,13.8733 10.875,18.6H12.875C12.875,15.3827 16.2652,12.4 20.9375,12.4V10.4ZM31,18.6C31,13.8733 26.2605,10.4 20.9375,10.4V12.4C25.6098,12.4 29,15.3827 29,18.6H31ZM27.2719,24.9775C29.5594,23.4612 31,21.1331 31,18.6H29C29,20.3389 28.007,22.0908 26.1669,23.3105L27.2719,24.9775ZM28.4668,25.797L27.6464,23.769L25.7924,24.519L26.6128,26.547L28.4668,25.797ZM28.0637,27.5819C28.561,27.127 28.7097,26.4231 28.4722,25.8105L26.6074,26.5335C26.556,26.4009 26.5839,26.225 26.7139,26.1061L28.0637,27.5819ZM26.9792,28C27.3931,28 27.7607,27.8365 28.0303,27.6111L26.7473,26.0769C26.7873,26.0435 26.8625,26 26.9792,26V28ZM26.3261,27.864C26.4916,27.9325 26.7167,28 26.9792,28V26C27.0201,26 27.0505,26.0053 27.0678,26.0093C27.0848,26.0133 27.0928,26.0168 27.0909,26.016L26.3261,27.864ZM23.2682,26.3476L26.2636,27.8356L27.1534,26.0444L24.1579,24.5564L23.2682,26.3476ZM20.9375,26.8C21.9571,26.8 22.9716,26.669 23.9564,26.4219L23.4697,24.4821C22.6371,24.691 21.786,24.8 20.9375,24.8V26.8ZM10.875,18.6C10.875,23.3267 15.6145,26.8 20.9375,26.8V24.8C16.2652,24.8 12.875,21.8173 12.875,18.6H10.875Z"
        android:fillColor="@color/color_primary"/>
  </group>
  <path
      android:strokeWidth="1"
      android:pathData="M24.5039,14.6346C24.5039,14.6346 25.3848,14.9202 26.0139,15.5118C26.643,16.1034 26.9667,16.6344 26.9667,16.6344"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M27.5926,17.7949L27.6779,17.9891L27.5926,17.7949Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="17.4281"
          android:startX="27.1016"
          android:endY="18.4257"
          android:endX="27.1713"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M27.5926,17.7949L27.6779,17.9891"
      android:strokeLineJoin="round"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M11.875,18.6C11.875,22.572 15.9398,25.8 20.9375,25.8C21.5883,25.8 13.0765,15.3436 13.0765,15.3436C13.0765,15.3436 12.4963,16.0389 12.1429,16.9481C11.9382,17.475 11.875,18.1581 11.875,18.6Z"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M1.8511,8.8581C1.8511,8.8581 1.4347,11.0286 1.8511,13.2961C2.2724,15.5912 3.5337,17.9887 4.7293,18.852L3.4811,21.336C3.3615,21.564 3.4122,21.852 3.6056,22.032C3.7216,22.14 3.8702,22.2 4.0212,22.2C4.1239,22.2 4.2278,22.176 4.3209,22.116L7.5181,20.304C8.364,20.604 9.2642,20.808 10.1982,20.916C10.4036,20.94 10.5885,20.868 10.7178,20.724C10.8483,20.568 10.8991,20.376 10.8556,20.184C10.7311,19.656 10.6671,18.6 10.6671,18.6L1.8511,8.8581Z"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.6"/>
</vector>
