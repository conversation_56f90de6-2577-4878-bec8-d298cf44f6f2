<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="30dp"
    android:height="34dp"
    android:viewportWidth="30"
    android:viewportHeight="34">
  <path
      android:pathData="M13.8688,19.9108L14.1554,20.2053L14.4421,19.9108C15.1489,19.1846 16.112,18.7762 17.1309,18.7762C19.2071,18.7762 20.8966,20.4657 20.8966,22.5418C20.8966,23.4707 20.5252,24.39 19.7903,25.2084L19.7902,25.2086C19.5075,25.5238 19.1322,25.9642 18.7098,26.4598C18.6174,26.5683 18.5227,26.6795 18.4262,26.7925C17.8863,27.4251 17.2863,28.1229 16.698,28.7725C16.108,29.4237 15.5374,30.0178 15.056,30.4466C14.8146,30.6615 14.6051,30.8264 14.4335,30.9353C14.348,30.9895 14.2791,31.0252 14.2262,31.0465C14.182,31.0643 14.1598,31.0675 14.1554,31.0681C14.1511,31.0675 14.1289,31.0643 14.0846,31.0465C14.0318,31.0252 13.9629,30.9895 13.8774,30.9353C13.7059,30.8264 13.4963,30.6615 13.255,30.4466C12.7736,30.0179 12.2031,29.4237 11.6132,28.7725C11.0249,28.123 10.425,27.4252 9.8852,26.7925C9.789,26.6799 9.6946,26.5691 9.6025,26.461C9.1798,25.9649 8.8042,25.524 8.5213,25.2086L8.5211,25.2084C7.7862,24.3899 7.4143,23.4706 7.4143,22.5418C7.4143,20.4657 9.1044,18.7762 11.1799,18.7762C12.1989,18.7762 13.162,19.1846 13.8688,19.9108Z"
      android:strokeWidth="0.8"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.75"
      android:strokeColor="@color/color_primary"/>
  <path
      android:pathData="M7.6571,21.1562C7.6571,21.1562 7.27,20.1071 7.0143,22.5418C7.0143,23.5832 7.4326,24.5949 8.2235,25.4756C9.5845,26.9931 13.1338,31.4682 14.1554,31.4682C14.6461,31.4682 15.5813,30.278 15.5813,30.278L7.6571,21.1562Z"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M17.7356,20.0879C17.7356,20.0879 18.2467,20.294 18.5956,20.6713C18.9445,21.0486 19.1146,21.3773 19.1146,21.3773"
      android:strokeLineJoin="round"
      android:strokeWidth="0.595095"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M19.4374,22.0921L19.48,22.2109L19.4374,22.0921Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="21.8539"
          android:startX="19.1611"
          android:endY="22.449"
          android:endX="19.1611"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M19.4374,22.0921L19.48,22.2109"
      android:strokeLineJoin="round"
      android:strokeWidth="0.595095"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M20.8316,8.7892L21.0645,9.0284L21.2974,8.7892C21.8495,8.2219 22.6018,7.9028 23.3978,7.9028C25.0197,7.9028 26.3395,9.2226 26.3395,10.8445C26.3395,11.5697 26.0496,12.288 25.4749,12.928L25.4747,12.9282C25.2529,13.1755 24.9585,13.521 24.6273,13.9097C24.5548,13.9948 24.4806,14.0819 24.405,14.1705C23.9816,14.6666 23.5112,15.2137 23.0499,15.7229C22.5874,16.2335 22.1402,16.699 21.7631,17.0349C21.5741,17.2032 21.4103,17.3321 21.2765,17.417C21.2098,17.4593 21.1564,17.4869 21.1158,17.5032C21.0853,17.5155 21.069,17.5185 21.0645,17.5193C21.06,17.5185 21.0437,17.5155 21.0132,17.5032C20.9726,17.4869 20.9192,17.4593 20.8525,17.417C20.7187,17.3321 20.5549,17.2032 20.3659,17.0349C19.9889,16.6991 19.5418,16.2335 19.0793,15.7229C18.6181,15.2137 18.1477,14.6666 17.7244,14.1705C17.649,14.0822 17.575,13.9953 17.5028,13.9105C17.1713,13.5215 16.8766,13.1756 16.6547,12.9282L16.6546,12.928C16.0798,12.2879 15.7895,11.5696 15.7895,10.8445C15.7895,9.2227 17.1097,7.9028 18.7311,7.9028C19.5271,7.9028 20.2795,8.2219 20.8316,8.7892Z"
      android:strokeWidth="0.65"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.75"
      android:strokeColor="@color/color_primary"/>
  <path
      android:pathData="M15.9682,9.7579C15.9682,9.7579 15.6647,8.9352 15.4641,10.8444C15.4641,11.6611 15.7922,12.4544 16.4124,13.1451C17.4796,14.3351 20.263,17.8444 21.0641,17.8444C21.4489,17.8444 22.1822,16.9111 22.1822,16.9111L15.9682,9.7579Z"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M23.9293,8.9778C23.9293,8.9778 24.3301,9.1394 24.6037,9.4353C24.8773,9.7311 25.0107,9.9889 25.0107,9.9889"
      android:strokeLineJoin="round"
      android:strokeWidth="0.466667"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M25.2642,10.5495L25.2976,10.6427L25.2642,10.5495Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="10.3627"
          android:startX="25.0476"
          android:endY="10.8294"
          android:endX="25.0476"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M25.2642,10.5495L25.2976,10.6427"
      android:strokeLineJoin="round"
      android:strokeWidth="0.466667"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M10.8858,3.6264L11.0649,3.8104L11.2441,3.6264C11.6789,3.1797 12.2714,2.9283 12.8983,2.9283C14.1755,2.9283 15.2149,3.9678 15.2149,5.245C15.2149,5.8163 14.9865,6.382 14.5342,6.8857L14.5341,6.8858C14.3599,7.08 14.1286,7.3514 13.8684,7.6568C13.8114,7.7237 13.7531,7.7921 13.6937,7.8618C13.361,8.2516 12.9914,8.6815 12.6289,9.0816C12.2654,9.4829 11.9139,9.8488 11.6174,10.1129C11.4688,10.2452 11.3398,10.3467 11.2344,10.4136C11.1818,10.447 11.1395,10.4689 11.1072,10.4819C11.0813,10.4923 11.068,10.4945 11.0649,10.4949C11.0619,10.4945 11.0485,10.4923 11.0226,10.4819C10.9903,10.4689 10.9481,10.447 10.8955,10.4136C10.79,10.3467 10.6611,10.2452 10.5125,10.1129C10.216,9.8488 9.8646,9.4829 9.5012,9.0816C9.1387,8.6815 8.7691,8.2516 8.4365,7.8618C8.3773,7.7924 8.3191,7.7241 8.2623,7.6575C8.0019,7.3518 7.7704,7.0802 7.5961,6.8858L7.596,6.8857C7.1436,6.3819 6.9149,5.8162 6.9149,5.245C6.9149,3.9678 7.9547,2.9283 9.2316,2.9283C9.8584,2.9283 10.4509,3.1797 10.8858,3.6264Z"
      android:strokeWidth="0.5"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.75"
      android:strokeColor="@color/color_primary"/>
  <path
      android:pathData="M7.061,4.3913C7.061,4.3913 6.8225,3.7449 6.6649,5.245C6.6649,5.8867 6.9227,6.51 7.41,7.0527C8.2486,7.9877 10.4355,10.745 11.0649,10.745C11.3672,10.745 11.9434,10.0117 11.9434,10.0117L7.061,4.3913Z"
      android:fillColor="@color/color_primary"
      android:fillAlpha="0.6"/>
  <path
      android:pathData="M12.8163,3.2783C12.8163,3.2783 13.1312,3.4053 13.3462,3.6378C13.5611,3.8702 13.666,4.0728 13.666,4.0728"
      android:strokeLineJoin="round"
      android:strokeWidth="0.366667"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M13.8652,4.5132L13.8914,4.5865L13.8652,4.5132Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="4.36652"
          android:startX="13.6949"
          android:endY="4.73318"
          android:endX="13.6949"
          android:type="linear">
        <item android:offset="0" android:color="#7F007755"/>
        <item android:offset="1" android:color="#7F000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M13.8652,4.5132L13.8914,4.5865"
      android:strokeLineJoin="round"
      android:strokeWidth="0.366667"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
</vector>
