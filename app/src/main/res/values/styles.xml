<resources>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
    </style>

    <style name="RowSettingsNotificationsTwoViewHolderSwitchOnSwitchTheme">
        <item name="colorControlActivated">@color/color_primary</item>
    </style>

    <style name="TabTextAppearance.Selected" parent="TextAppearance.Design.Tab">
        <item name="android:fontFamily">@font/inter_extrabold</item>
        <item name="fontFamily">@font/inter_extrabold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">20sp</item>
        <item name="textAllCaps">false</item>
    </style>
    <style name="TabTextAppearance.Unselected" parent="TextAppearance.Design.Tab">
        <item name="android:fontFamily">@font/inter_extrabold</item>
        <item name="fontFamily">@font/inter_extrabold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">20sp</item>
        <item name="textAllCaps">false</item>
    </style>

</resources>
