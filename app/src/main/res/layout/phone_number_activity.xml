<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/white">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginStart="@dimen/phone_number_activity_group_constraint_layout_margin_start"
		android:layout_marginTop="64dp"
		android:layout_marginEnd="@dimen/phone_number_activity_group_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<TextView
			android:id="@+id/what_syour_number_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginEnd="@dimen/phone_number_activity_what_syour_number_text_view_margin_end"
			android:fontFamily="@font/inter_bold"
			android:gravity="left"
			android:lineSpacingMultiplier="1"
			android:text="@string/ph_no_title"
			android:textColor="@color/grey1"
			android:textSize="@dimen/phone_number_activity_what_syour_number_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp" />

		<TextView
			android:id="@+id/we_ll_text_you_acod_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/phone_number_activity_we_ll_text_you_acod_text_view_margin_top"
			android:fontFamily="@font/inter"
			android:gravity="start"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/ph_no_desc"
			android:textColor="@color/grey1"
			android:textSize="@dimen/phone_number_activity_we_ll_text_you_acod_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/what_syour_number_text_view" />

		<TextView
			android:id="@+id/height_text_view"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginTop="10dp"
			android:fontFamily="@font/inter"
			android:gravity="start"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/ph_no_country_code"
			android:textColor="@color/grey1"
			android:textSize="@dimen/phone_number_activity_text_view_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/we_ll_text_you_acod_text_view" />

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/rectangle_constraint_layout"
			android:layout_width="@dimen/phone_number_activity_rectangle_constraint_layout_width"
			android:layout_height="@dimen/phone_number_activity_rectangle_constraint_layout_height"
			android:layout_marginTop="2dp"
			android:background="@color/grey4"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/height_text_view" />

		<EditText
			android:id="@+id/phone_number_edit_text"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginStart="@dimen/phone_number_activity_e_g5555555555_edit_text_margin_start"
			android:layout_marginTop="10dp"
			android:background="@color/white"
			android:fontFamily="@font/inter"
			android:gravity="start"
			android:hint="eg. 55555555"
			android:inputType="phone"
			android:lineSpacingMultiplier="1"
			android:maxLength="10"
			android:textColor="@color/grey1"
			android:textColorHint="@color/grey4"
			android:textSize="@dimen/phone_number_activity_e_g5555555555_edit_text_text_size"
			app:layout_constraintLeft_toRightOf="@+id/height_text_view"
			app:layout_constraintTop_toBottomOf="@+id/we_ll_text_you_acod_text_view" />

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/rectangle_two_constraint_layout"
			android:layout_width="@dimen/phone_number_activity_rectangle_two_constraint_layout_width"
			android:layout_height="@dimen/phone_number_activity_rectangle_two_constraint_layout_height"
			android:layout_marginStart="@dimen/phone_number_activity_rectangle_two_constraint_layout_margin_start"
			android:layout_marginTop="2dp"
			android:background="@color/grey4"
			app:layout_constraintLeft_toRightOf="@+id/rectangle_constraint_layout"
			app:layout_constraintTop_toBottomOf="@+id/phone_number_edit_text" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/phone_number_activity_fields_constraint_layout_width"
		android:layout_height="@dimen/phone_number_activity_fields_constraint_layout_height"
		android:layout_marginStart="@dimen/phone_number_activity_fields_constraint_layout_margin_start"
		android:layout_marginTop="@dimen/phone_number_activity_fields_constraint_layout_margin_top"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintTop_toTopOf="parent"
		tools:layout_editor_absoluteX="24dp"
		tools:layout_editor_absoluteY="241dp"/>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/button_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/phone_number_activity_button_constraint_layout_height"
		android:layout_marginStart="@dimen/phone_number_activity_button_constraint_layout_margin_start"
		android:layout_marginTop="24dp"
		android:layout_marginEnd="@dimen/phone_number_activity_button_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout">

		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/phone_number_activity_button_large_active_button_height"
			android:background="@drawable/bottom_button_disabled_state"
			android:enabled="false"
			android:text="@string/send_code_text"
			android:textColor="@color/white"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp" />
	</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>