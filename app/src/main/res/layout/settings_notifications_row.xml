<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/row_question_copy6_two_view_holder_row_question_copy6_height"
    android:background="@color/white">

    <TextView
        android:id="@+id/setting_item_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/row_question_copy6_two_view_holder_setting_text_view_margin_start"
        android:fontFamily="@font/inter"
        android:gravity="start"
        android:lineSpacingMultiplier="1.09"
        android:text="@string/settings_notifications_title"
        android:textColor="@color/grey2"
        android:textSize="@dimen/row_question_copy6_two_view_holder_setting_text_view_text_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="24dp"
        tools:layout_editor_absoluteY="18dp"/>

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/notifications_item_switch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/row_question_copy6_two_view_holder_switch_on_copy2_switch_margin_end"
        android:checked="true"
        android:theme="@style/RowSettingsNotificationsTwoViewHolderSwitchOnSwitchTheme"
        android:thumbTint="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="259dp"
        tools:layout_editor_absoluteY="14dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>