<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ImageButton
        android:id="@+id/settings_header_left_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:layout_marginTop="12dp"
        android:background="@null"
        android:elevation="2dp"
        android:padding="10dp"
        android:scaleType="center"
        android:src="@drawable/ic_icon_arrow_left"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/inter_bold"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:lineSpacingMultiplier="1.09"
        android:text="Teleport Mode"
        android:textColor="@color/grey1"
        android:textSize="20sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/teleport_header_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="center"
        android:layout_marginTop="24dp"
        android:src="@drawable/teleport_header"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title_text_view" />

    <TextView
        android:id="@+id/teleport_desc_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="18sp"
        android:fontFamily="@font/inter"
        android:gravity="center"
        android:lineSpacingMultiplier="0.86"
        android:text="@string/teleport_desc"
        android:textColor="@color/grey2"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/teleport_header_image" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/teleport_divider"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/grey5"
        android:layout_marginTop="18dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/teleport_desc_text">

        <TextView
            android:id="@+id/teleport_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_sex_preference_text_view_margin_start"
            android:fontFamily="@font/inter_bold"
            android:gravity="center_vertical"
            android:lineSpacingMultiplier="0.86"
            android:text="CURRENT LOCATION"
            android:textColor="@color/grey3"
            android:textSize="12sp"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/teleport_current_location_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_row_with_value_constraint_layout_margin_top"
        android:background="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/teleport_divider">

        <TextView
            android:id="@+id/location_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:fontFamily="@font/inter_bold"
            android:gravity="start"
            android:lineSpacingMultiplier="1.1"
            android:text="Your current location"
            android:textColor="@color/grey1"
            android:textSize="16sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:drawableStartCompat="@drawable/ic_icon_location_pin"
            android:drawablePadding="8dp"/>

        <ImageView
            android:id="@+id/location_chevron"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="24dp"
            android:scaleType="center"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            android:src="@drawable/ic_selection_tick"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/other_unlocked_divider"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/grey5"
        android:layout_marginTop="16dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/teleport_current_location_container">

        <TextView
            android:id="@+id/other_unlock_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_sex_preference_text_view_margin_start"
            android:fontFamily="@font/inter_bold"
            android:gravity="center_vertical"
            android:lineSpacingMultiplier="0.86"
            android:text="@string/preferences_other_unlocked_cities"
            android:textColor="@color/grey3"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/add_another_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="8dp"
        android:fontFamily="@font/inter_semibold"
        android:gravity="start"
        android:lineSpacingMultiplier="1.09"
        android:padding="2dp"
        android:text="@string/add_new_city"
        android:drawablePadding="6dp"
        android:textColor="@color/grey1"
        android:textSize="16sp"
        app:layout_constraintTop_toBottomOf="@+id/other_unlocked_divider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:drawableStartCompat="@drawable/ic_add" />

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#F2F2F2"
        android:layout_marginTop="8dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/add_another_text_view" />

    <FrameLayout
        android:id="@+id/locations_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toBottomOf="@+id/view_divider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>