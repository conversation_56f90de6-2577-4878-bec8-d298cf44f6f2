<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_normal_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/settings_settings_supernova_activity_header_normal_constraint_layout_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp">

        <ImageButton
            android:id="@+id/notifications_header_left_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_tail_left_image_view_margin_start"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group4_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@+id/button_next"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_normal_constraint_layout"
        app:layout_constraintVertical_bias="0.72"
        app:layout_constraintVertical_chainStyle="spread_inside">

        <TextView
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginTop="36dp"
            android:layout_marginEnd="48dp"
            android:fontFamily="@font/inter_bold"
            android:gravity="left"
            android:lineSpacingMultiplier="0.97"
            android:text="Who are you interested in?"
            android:textColor="@color/grey1"
            android:textSize="27sp"
            app:layout_constraintHorizontal_bias="0.53"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            android:fontFamily="@font/inter"
            android:lineSpacingMultiplier="0.96"
            android:text="You can change this later."
            android:textColor="@color/grey1"
            android:textSize="18sp"
            android:layout_marginTop="8dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="103dp" />

        <android.widget.Button
            android:id="@+id/button_preference_men"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="48dp"
            android:background="@drawable/button_border_blue"
            android:fontFamily="@font/inter"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="Men"
            android:textAllCaps="false"
            android:textColor="@color/grey1"
            android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
            app:layout_constraintHorizontal_bias="0.468"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/desc" />

        <android.widget.Button
            android:id="@+id/button_preference_women"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="48dp"
            android:background="@drawable/button_border_blue"
            android:fontFamily="@font/inter"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="Women"
            android:textAllCaps="false"
            android:textColor="@color/grey1"
            android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
            app:layout_constraintHorizontal_bias="0.468"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/button_preference_men"
            tools:text="@string/sex_preference_activity_interested_in_women_button_text" />

        <android.widget.Button
            android:id="@+id/button_preference_everyone"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="48dp"
            android:background="@drawable/button_border_blue"
            android:fontFamily="@font/inter"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="Everyone"
            android:visibility="gone"
            android:textAllCaps="false"
            android:textColor="@color/grey1"
            android:textSize="@dimen/sex_preference_activity_radio_button_unselected_button_text_size"
            app:layout_constraintHorizontal_bias="0.468"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/button_preference_women" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <android.widget.Button
        android:id="@+id/button_next"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/first_name_activity_button_large_active_button_height"
        android:layout_marginStart="36dp"
        android:layout_marginTop="36dp"
        android:layout_marginEnd="36dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/bottom_button_disabled_state"
        android:enabled="false"
        android:lineSpacingMultiplier="1"
        android:text="Next"
        android:textColor="@color/white"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/group4_constraint_layout"
        app:layout_constraintVertical_bias="1.0" />

</androidx.constraintlayout.widget.ConstraintLayout>