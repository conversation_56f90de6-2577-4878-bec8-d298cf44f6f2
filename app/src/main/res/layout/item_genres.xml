<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="0dp">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/genreButton"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/grey2"
        android:textSize="16sp"
        android:textAllCaps="false"
        app:backgroundTint="@android:color/transparent"
        app:cornerRadius="12dp"
        app:strokeColor="@color/grey4"
        android:text="Action"
        app:strokeWidth="1dp"
        android:minHeight="0dp"
        android:minWidth="0dp"
        android:layout_marginEnd="6dp"/>
</RelativeLayout>