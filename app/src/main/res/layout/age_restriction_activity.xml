<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/white"
	android:fitsSystemWindows="true">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group_constraint_layout"
		android:layout_width="@dimen/age_restriction_activity_group_constraint_layout_width"
		android:layout_height="@dimen/age_restriction_activity_group_constraint_layout_height"
		android:layout_marginTop="60dp"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/header_onboarding_no_icon_constraint_layout"
		tools:layout_editor_absoluteX="24dp"
		tools:layout_editor_absoluteY="104dp">
	
		<TextView
            android:layout_width="@dimen/age_restriction_activity_lovebeat_is_only_for_ttext_view_width"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/age_restriction_activity_lovebeat_is_only_for_ttext_view_margin_top"
			android:fontFamily="@font/inter_bold"
			android:gravity="center"
			android:lineSpacingMultiplier="0.97"
			android:text="@string/age_restriction_activity_lovebeat_is_only_for_ttext_view_text"
			android:textColor="@color/grey1"
			android:textSize="@dimen/age_restriction_activity_lovebeat_is_only_for_ttext_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="-1dp"/>
	
		<TextView
			android:id="@+id/desc"
            android:layout_width="@dimen/age_restriction_activity_check_back_after_you_text_view_width"
			android:layout_height="wrap_content"
			android:layout_marginBottom="@dimen/age_restriction_activity_check_back_after_you_text_view_margin_bottom"
			android:fontFamily="@font/inter"
			android:gravity="center"
			android:lineSpacingMultiplier="1.18"
			android:text="@string/age_restriction_activity_check_back_after_you_text_view_text"
			android:textColor="@color/grey1"
			android:textSize="@dimen/age_restriction_activity_check_back_after_you_text_view_text_size"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="70dp"/>
	</androidx.constraintlayout.widget.ConstraintLayout>

	<android.widget.Button
		android:id="@+id/button_large_active_button"
		style="?android:attr/borderlessButtonStyle"
		android:theme="@style/BottomCTAButton"
		android:layout_width="0dp"
		android:layout_height="@dimen/age_restriction_activity_button_large_active_button_height"
		android:layout_marginStart="@dimen/age_restriction_activity_button_large_active_button_margin_start"
		android:layout_marginEnd="@dimen/age_restriction_activity_button_large_active_button_margin_end"
		android:layout_marginBottom="32dp"
		android:layout_marginTop="48dp"
		android:background="@drawable/button_border_blue"
		android:text="@string/age_restriction_activity_button_large_active_button_text"
		app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout"
		android:textColor="@color/color_primary"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent" />

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/header_onboarding_no_icon_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/age_restriction_activity_header_onboarding_no_icon_constraint_layout_height"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent"
		tools:layout_editor_absoluteX="0dp"
		tools:layout_editor_absoluteY="0dp">
	
		<View
			android:id="@+id/rectangle_two_constraint_layout"
			android:layout_width="0dp"
			android:layout_height="@dimen/age_restriction_activity_rectangle_two_constraint_layout_height"
			android:layout_marginLeft="@dimen/age_restriction_activity_rectangle_two_constraint_layout_margin_start"
			android:layout_marginRight="@dimen/age_restriction_activity_rectangle_two_constraint_layout_margin_end"
			android:layout_marginTop="@dimen/age_restriction_activity_rectangle_two_constraint_layout_margin_top"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="-1dp"
			tools:layout_editor_absoluteY="20dp"/>
	
		<TextView
			android:id="@+id/right_text_view"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginRight="@dimen/age_restriction_activity_right_text_view_margin_end"
			android:layout_marginTop="@dimen/age_restriction_activity_right_text_view_margin_top"
			android:fontFamily="@font/inter"
			android:gravity="end"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/age_restriction_activity_right_text_view_text"
			android:textColor="@color/grey1"
			android:textSize="@dimen/age_restriction_activity_right_text_view_text_size"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="300dp"
			tools:layout_editor_absoluteY="31dp"/>
	
		<TextView
			android:id="@+id/left_text_view"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_marginLeft="@dimen/age_restriction_activity_left_text_view_margin_start"
			android:layout_marginTop="@dimen/age_restriction_activity_left_text_view_margin_top"
			android:fontFamily="@font/inter"
			android:gravity="start"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/age_restriction_activity_left_text_view_text"
			android:textColor="@color/grey1"
			android:textSize="@dimen/age_restriction_activity_left_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="16dp"
			tools:layout_editor_absoluteY="31dp"/>
	</androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>