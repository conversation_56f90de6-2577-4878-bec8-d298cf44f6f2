<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/white"
	android:fitsSystemWindows="true">

	<include
		android:id="@+id/action_bar"
		layout="@layout/custom_action_bar"
		android:layout_width="match_parent"
		android:layout_height="?attr/actionBarSize" />

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group6_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginTop="72dp"
		android:layout_marginBottom="12dp"
		app:layout_constraintVertical_bias="0"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/action_bar"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/group5_constraint_layout"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginStart="20dp"
			android:layout_marginTop="16dp"
			android:layout_marginEnd="20dp"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout">

			<androidx.constraintlayout.widget.ConstraintLayout
				android:id="@+id/group4_constraint_layout"
				android:layout_width="0dp"
				android:layout_height="wrap_content"
				android:layout_marginTop="2dp"
				android:layout_marginBottom="2dp"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintEnd_toEndOf="parent"
				app:layout_constraintStart_toStartOf="parent"
				app:layout_constraintTop_toTopOf="parent">

				<androidx.constraintlayout.widget.ConstraintLayout
					android:id="@+id/image_empty_copy0_constraint_layout"
					android:layout_width="@dimen/images3_activity_image_empty_copy5_constraint_layout_width"
					android:layout_height="@dimen/images3_activity_image_empty_copy3_constraint_layout_height"
					app:layout_constraintBottom_toTopOf="@+id/image_empty_copy3_constraint_layout"
					app:layout_constraintEnd_toStartOf="@+id/image_empty_copy1_constraint_layout"
					app:layout_constraintStart_toStartOf="parent"
					app:layout_constraintTop_toTopOf="parent">

					<androidx.constraintlayout.widget.ConstraintLayout
						android:layout_width="0dp"
						android:layout_height="@dimen/images3_activity_rectangle_constraint_layout_height"
						android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
						android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
						android:background="@drawable/images3_activity_rectangle_constraint_layout_background"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="0dp"
						tools:layout_editor_absoluteY="5dp" />

					<ImageView
						android:id="@+id/icon_plus_addphoto_image_view0"
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:src="@drawable/ic_add"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="69dp"
						tools:layout_editor_absoluteY="0dp" />

					<ImageView
						android:id="@+id/icon_camera_image_view0"
						android:layout_width="match_parent"
						android:layout_height="match_parent"
						android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
						android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
						android:scaleType="center"
						android:src="@drawable/icon_camera"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="28dp"
						tools:layout_editor_absoluteY="33dp" />
				</androidx.constraintlayout.widget.ConstraintLayout>

				<androidx.constraintlayout.widget.ConstraintLayout
					android:id="@+id/image_empty_copy1_constraint_layout"
					android:layout_width="95dp"
					android:layout_height="@dimen/images3_activity_image_empty_copy3_constraint_layout_height"
					app:layout_constraintLeft_toLeftOf="parent"
					app:layout_constraintRight_toRightOf="parent"
					app:layout_constraintTop_toTopOf="parent"
					tools:layout_editor_absoluteX="124dp"
					tools:layout_editor_absoluteY="5dp">

					<androidx.constraintlayout.widget.ConstraintLayout
						android:layout_width="@dimen/images3_activity_rectangle_constraint_layout_height"
						android:layout_height="@dimen/images3_activity_rectangle_constraint_layout_height"
						android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
						android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
						android:background="@drawable/images3_activity_rectangle_constraint_layout_background"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteY="3dp" />

					<ImageView
						android:id="@+id/icon_plus_addphoto_image_view1"
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:src="@drawable/ic_add"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="69dp"
						tools:layout_editor_absoluteY="0dp" />

					<ImageView
						android:id="@+id/icon_camera_image_view1"
						android:layout_width="match_parent"
						android:layout_height="match_parent"
						android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
						android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
						android:scaleType="center"
						android:src="@drawable/icon_camera"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="28dp"
						tools:layout_editor_absoluteY="33dp" />
				</androidx.constraintlayout.widget.ConstraintLayout>

				<androidx.constraintlayout.widget.ConstraintLayout
					android:id="@+id/image_empty_copy2_constraint_layout"
					android:layout_width="@dimen/images3_activity_image_empty_copy3_constraint_layout_width"
					android:layout_height="@dimen/images3_activity_image_empty_copy3_constraint_layout_height"
					android:layout_marginBottom="12dp"
					app:layout_constraintBottom_toTopOf="@+id/image_empty_copy5_constraint_layout"
					app:layout_constraintEnd_toEndOf="parent"
					app:layout_constraintStart_toEndOf="@+id/image_empty_copy1_constraint_layout"
					app:layout_constraintTop_toTopOf="parent">

					<androidx.constraintlayout.widget.ConstraintLayout
						android:layout_width="90dp"
						android:layout_height="@dimen/images3_activity_rectangle_constraint_layout_height"
						android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
						android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
						android:background="@drawable/images3_activity_rectangle_constraint_layout_background"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent" />

					<ImageView
						android:id="@+id/icon_plus_addphoto_image_view2"
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:src="@drawable/ic_add"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="69dp"
						tools:layout_editor_absoluteY="0dp" />

					<ImageView
						android:id="@+id/icon_camera_image_view2"
						android:layout_width="match_parent"
						android:layout_height="match_parent"
						android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
						android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
						android:scaleType="center"
						android:src="@drawable/icon_camera"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="28dp"
						tools:layout_editor_absoluteY="33dp" />
				</androidx.constraintlayout.widget.ConstraintLayout>

				<androidx.constraintlayout.widget.ConstraintLayout
					android:id="@+id/image_empty_copy3_constraint_layout"
					android:layout_width="@dimen/images3_activity_image_empty_copy3_constraint_layout_width"
					android:layout_height="@dimen/images3_activity_image_empty_copy3_constraint_layout_height"
					android:layout_marginStart="12dp"
					android:layout_marginTop="10dp"
					android:layout_marginEnd="12dp"
					android:layout_marginBottom="10dp"
					app:layout_constraintBottom_toBottomOf="parent"
					app:layout_constraintEnd_toStartOf="@+id/image_empty_copy4_constraint_layout"
					app:layout_constraintStart_toStartOf="parent"
					app:layout_constraintTop_toBottomOf="@+id/image_empty_copy0_constraint_layout">

					<androidx.constraintlayout.widget.ConstraintLayout
						android:id="@+id/rectangle_constraint_layout"
						android:layout_width="0dp"
						android:layout_height="@dimen/images3_activity_rectangle_constraint_layout_height"
						android:layout_marginTop="@dimen/images3_activity_rectangle_constraint_layout_margin_top"
						android:layout_marginEnd="@dimen/images3_activity_rectangle_constraint_layout_margin_end"
						android:background="@drawable/images3_activity_rectangle_constraint_layout_background"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="0dp"
						tools:layout_editor_absoluteY="5dp" />

					<ImageView
						android:id="@+id/icon_plus_addphoto_image_view3"
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:src="@drawable/ic_add"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="69dp"
						tools:layout_editor_absoluteY="0dp" />

					<ImageView
						android:id="@+id/icon_camera_image_view3"
						android:layout_width="match_parent"
						android:layout_height="match_parent"
						android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
						android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
						android:scaleType="center"
						android:src="@drawable/icon_camera"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="28dp"
						tools:layout_editor_absoluteY="33dp" />
				</androidx.constraintlayout.widget.ConstraintLayout>

				<androidx.constraintlayout.widget.ConstraintLayout
					android:id="@+id/image_empty_copy4_constraint_layout"
					android:layout_width="@dimen/images3_activity_image_empty_copy4_constraint_layout_width"
					android:layout_height="@dimen/images3_activity_image_empty_copy4_constraint_layout_height"
					android:layout_marginLeft="12dp"
					android:layout_marginTop="10dp"
					android:layout_marginRight="12dp"
					android:layout_marginBottom="10dp"
					app:layout_constraintBottom_toBottomOf="parent"
					app:layout_constraintLeft_toLeftOf="@+id/image_empty_copy5_constraint_layout"
					app:layout_constraintRight_toRightOf="@+id/image_empty_copy3_constraint_layout"
					app:layout_constraintTop_toBottomOf="@+id/image_empty_copy1_constraint_layout">

					<androidx.constraintlayout.widget.ConstraintLayout
						android:id="@+id/rectangle_two_constraint_layout"
						android:layout_width="0dp"
						android:layout_height="@dimen/images3_activity_rectangle_two_constraint_layout_height"
						android:layout_marginTop="@dimen/images3_activity_rectangle_two_constraint_layout_margin_top"
						android:layout_marginEnd="@dimen/images3_activity_rectangle_two_constraint_layout_margin_end"
						android:background="@drawable/images3_activity_rectangle_two_constraint_layout_background"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="0dp"
						tools:layout_editor_absoluteY="5dp" />

					<ImageView
						android:id="@+id/icon_plus_addphoto_image_view4"
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:src="@drawable/ic_add"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="68dp"
						tools:layout_editor_absoluteY="0dp" />

					<ImageView
						android:id="@+id/icon_camera_image_view4"
						android:layout_width="match_parent"
						android:layout_height="match_parent"
						android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
						android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
						android:scaleType="center"
						android:src="@drawable/icon_camera"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="27dp"
						tools:layout_editor_absoluteY="33dp" />
				</androidx.constraintlayout.widget.ConstraintLayout>

				<androidx.constraintlayout.widget.ConstraintLayout
					android:id="@+id/image_empty_copy5_constraint_layout"
					android:layout_width="@dimen/images3_activity_image_empty_copy5_constraint_layout_width"
					android:layout_height="@dimen/images3_activity_image_empty_copy5_constraint_layout_height"
					android:layout_marginStart="12dp"
					android:layout_marginTop="10dp"
					android:layout_marginEnd="12dp"
					android:layout_marginBottom="10dp"
					app:layout_constraintBottom_toBottomOf="@+id/image_empty_copy2_constraint_layout"
					app:layout_constraintEnd_toEndOf="parent"
					app:layout_constraintStart_toEndOf="@+id/image_empty_copy4_constraint_layout"
					app:layout_constraintTop_toBottomOf="parent">

					<androidx.constraintlayout.widget.ConstraintLayout
						android:id="@+id/rectangle_three_constraint_layout"
						android:layout_width="0dp"
						android:layout_height="@dimen/images3_activity_rectangle_three_constraint_layout_height"
						android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
						android:layout_marginEnd="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
						android:background="@drawable/images3_activity_rectangle_three_constraint_layout_background"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="0dp"
						tools:layout_editor_absoluteY="5dp" />

					<ImageView
						android:id="@+id/icon_plus_addphoto_image_view5"
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:src="@drawable/ic_add"
						app:layout_constraintRight_toRightOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="69dp"
						tools:layout_editor_absoluteY="0dp" />

					<ImageView
						android:id="@+id/icon_camera_image_view5"
						android:layout_width="match_parent"
						android:layout_height="match_parent"
						android:layout_marginTop="@dimen/images3_activity_rectangle_three_constraint_layout_margin_top"
						android:layout_marginRight="@dimen/images3_activity_rectangle_three_constraint_layout_margin_end"
						android:scaleType="center"
						android:src="@drawable/icon_camera"
						app:layout_constraintLeft_toLeftOf="parent"
						app:layout_constraintTop_toTopOf="parent"
						tools:layout_editor_absoluteX="28dp"
						tools:layout_editor_absoluteY="33dp" />
				</androidx.constraintlayout.widget.ConstraintLayout>

			</androidx.constraintlayout.widget.ConstraintLayout>
		</androidx.constraintlayout.widget.ConstraintLayout>

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/group_constraint_layout"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginStart="36dp"
			android:layout_marginTop="12dp"
			android:layout_marginEnd="24dp"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toTopOf="parent">

			<TextView
				android:id="@+id/let_schoose_your_ph_text_view"
				android:layout_width="0dp"
				android:layout_height="wrap_content"
				android:fontFamily="@font/inter_bold"
				android:lineSpacingMultiplier="1"
				android:text="@string/photo_upload_screen_title"
				android:textColor="@color/grey1"
				android:textSize="@dimen/images3_activity_let_schoose_your_ph_text_view_text_size"
				app:layout_constraintEnd_toEndOf="parent"
				app:layout_constraintStart_toStartOf="parent"
				app:layout_constraintTop_toTopOf="parent" />

			<TextView
				android:id="@+id/please_upload_at_lea_text_view"
				android:layout_width="0dp"
				android:layout_height="wrap_content"
				android:layout_marginTop="@dimen/images3_activity_please_upload_at_lea_text_view_margin_top"
				android:fontFamily="@font/inter"
				android:lineSpacingMultiplier="1.18"
				android:text="@string/photo_upload_screen_desc"
				android:textColor="@color/grey1"
				android:textSize="@dimen/images3_activity_please_upload_at_lea_text_view_text_size"
				app:layout_constraintEnd_toEndOf="parent"
				app:layout_constraintStart_toStartOf="parent"
				app:layout_constraintTop_toBottomOf="@+id/let_schoose_your_ph_text_view" />
		</androidx.constraintlayout.widget.ConstraintLayout>

		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/intro1_activity_button_large_active_button_height"
			android:text="@string/next"
			android:enabled="false"
			android:layout_marginTop="36dp"
			android:layout_marginStart="24dp"
			android:layout_marginEnd="24dp"
			android:layout_marginBottom="36dp"
			android:background="@drawable/bottom_button_disabled_state"
			android:textColor="@color/white"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/group5_constraint_layout"
			app:layout_constraintVertical_bias="1" />

	</androidx.constraintlayout.widget.ConstraintLayout>

	<ProgressBar
		android:id="@+id/images_progress_bar"
		android:layout_width="wrap_content"
		android:layout_height="wrap_content"
		android:visibility="gone"
		app:layout_constraintStart_toStartOf="parent"
		app:layout_constraintEnd_toEndOf="parent"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>