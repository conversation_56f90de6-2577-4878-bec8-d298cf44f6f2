<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group4_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.295">

        <TextView
            android:id="@+id/you_are_atext_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="44dp"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="0.97"
            android:text="@string/sex_preference_activity_interested_in_title"
            android:textColor="@color/grey1"
            android:textSize="@dimen/sex_preference_activity_you_are_atext_view_text_size"
            app:layout_constraintHorizontal_bias="0.53"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <android.widget.Button
            android:id="@+id/button_preference_man"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="@dimen/sex_preference_activity_radio_button_unselected_button_height"
            android:layout_marginTop="16dp"
            android:background="@drawable/button_border_blue"
            android:fontFamily="@font/inter"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="Men"
            android:textAllCaps="false"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            android:textColor="@color/grey1"
            android:textSize="16sp"
            app:layout_constraintHorizontal_bias="0.468"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/you_are_atext_view" />

        <android.widget.Button
            android:id="@+id/button_preference_woman"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="@dimen/sex_preference_activity_radio_button_unselected_button_height"
            android:layout_marginTop="12dp"
            android:background="@drawable/button_border_blue"
            android:fontFamily="@font/inter"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="Women"
            android:textAllCaps="false"
            android:textColor="@color/grey1"
            android:textSize="16sp"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            app:layout_constraintHorizontal_bias="0.468"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/button_preference_man"
            tools:text="@string/sex_preference_activity_interested_in_women_button_text" />

        <android.widget.Button
            android:id="@+id/button_preference_both"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="@dimen/sex_preference_activity_radio_button_unselected_button_height"
            android:layout_marginTop="12dp"
            android:background="@drawable/button_border_blue"
            android:fontFamily="@font/inter"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="Everyone"
            android:visibility="gone"
            android:textAllCaps="false"
            android:textColor="@color/grey1"
            android:textSize="16sp"
            android:layout_marginStart="48dp"
            android:layout_marginEnd="48dp"
            app:layout_constraintHorizontal_bias="0.468"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/button_preference_woman" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_normal_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/settings_settings_supernova_activity_header_normal_constraint_layout_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp">

        <ImageButton
            android:id="@+id/notifications_header_left_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/preferences_men_secopy2supernova_activity_tail_left_image_view_margin_start"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>