<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/favorite_image_view"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/favorite_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:fontFamily="@font/inter"
        android:gravity="left"
        android:lineSpacingMultiplier="1.1"
        android:maxLines="2"
        android:textColor="@color/grey1"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/favorite_image_view"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/line"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="@color/grey4"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/favorite_image_view"
        app:layout_constraintEnd_toEndOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>