<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="12dp"
    android:layout_marginStart="12dp"
    android:layout_marginEnd="12dp"
    android:clickable="true"
    android:focusable="true"
    android:background="@color/white"
    android:foreground="@drawable/card_view_border"
    app:cardCornerRadius="4dp">

    <LinearLayout xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/other_location_item_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/city_image"
            android:layout_width="130dp"
            android:layout_height="65dp"
            android:scaleType="centerCrop"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/city_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="32dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/inter_bold"
            android:textColor="@color/grey1"
            android:textSize="16sp"
            tools:text="San Francisco, CA" />
    </LinearLayout>
</androidx.cardview.widget.CardView>