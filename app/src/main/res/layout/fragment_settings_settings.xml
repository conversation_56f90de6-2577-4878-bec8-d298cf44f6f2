<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/settings_view_recycler_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/settings_home_supernova_activity_view_recycler_view_margin_top"
        app:layout_constraintBottom_toTopOf="@+id/app_version"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_normal_constraint_layout"
        tools:listitem="@layout/settings_row" />

    <TextView
        android:id="@+id/app_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:textAlignment="center"
        android:layout_marginBottom="10dp"
        android:fontFamily="@font/inter"
        android:textColor="@color/grey1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/settings_view_recycler_view" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_normal_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/settings_settings_supernova_activity_header_normal_constraint_layout_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp">

        <ImageButton
            android:id="@+id/settings_header_left_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="18dp"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/title_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            android:text="@string/settings_settings_supernova_activity_title_text_view_text"
            android:textColor="@color/grey1"
            android:textSize="@dimen/settings_settings_supernova_activity_title_text_view_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>