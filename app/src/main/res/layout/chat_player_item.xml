<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/chat_payer_item_layout"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="50dp"
    android:background="@color/color_tertiary_lighter"
    android:layout_height="wrap_content">

    <ImageButton
        android:id="@+id/play_button"
        android:layout_width="wrap_content"
        android:layout_height="36dp"
        android:background="@null"
        android:src="@drawable/ic_play_black"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

<!--    <TextView-->
<!--        android:id="@+id/placeHolderTV"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_gravity="center"-->
<!--        android:layout_toEndOf="@+id/play_button"-->
<!--        android:fontFamily="@font/inter"-->
<!--        android:gravity="start"-->
<!--        android:layout_marginStart="8dp"-->
<!--        android:text="Click on play to listen"-->
<!--        android:textColor="@color/grey2"-->
<!--        app:layout_constraintStart_toEndOf="@+id/play_button"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"/>-->

    <!--    <SeekBar-->
    <!--        android:id="@+id/seekbar"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_gravity="center"-->
    <!--        android:layout_marginTop="4dp"-->
    <!--        android:layout_toEndOf="@+id/play_button" />-->
</androidx.constraintlayout.widget.ConstraintLayout>

