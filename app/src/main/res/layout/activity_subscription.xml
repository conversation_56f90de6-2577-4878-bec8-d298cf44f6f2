<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/topLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_normal_constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/close_button"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="18dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_close"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/grey3" />

        <View
            android:id="@+id/view_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#F2F2F2"
            android:layout_marginTop="12dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/close_button" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_plus_benefits"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_normal_constraint_layout"
        tools:ignore="ContentDescription">

        <TextView
            android:id="@+id/subscription_title_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:textColor="@color/grey2"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/subscription_title_text"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0"
            app:layout_constraintVertical_chainStyle="spread_inside" />

        <TextView
            android:id="@+id/subscription_title_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="@string/upgrade_to_lovebeat_plus"
            android:textColor="@color/grey1"
            android:textSize="20sp"
            app:layout_constraintBottom_toTopOf="@+id/subscription_sub_title_text"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/subscription_title_header"
            app:layout_constraintVertical_bias="0.5" />

        <TextView
            android:id="@+id/subscription_sub_title_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1"
            android:text="@string/influencer_offer_title"
            android:textColor="@color/grey2"
            android:textSize="14sp"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/subscription_benefits_list"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/subscription_title_text"
            app:layout_constraintVertical_bias="0.5" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/subscription_benefits_list"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:scrollbars="vertical"
            app:layout_constraintBottom_toTopOf="@+id/button_join_lovebeat_plus"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/subscription_sub_title_text"
            tools:listitem="@layout/subscriptions_benefits_list_item" />

        <android.widget.Button
            android:id="@+id/button_join_lovebeat_plus"
            style="?android:attr/borderlessButtonStyle"
            android:layout_width="0dp"
            android:layout_height="@dimen/intro1_activity_button_large_active_button_height"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/bottom_button_active_state"
            android:text="@string/join_lovebeat_plus"
            android:textColor="@color/white"
            android:theme="@style/BottomCTAButton"
            app:layout_constraintBottom_toTopOf="@+id/restore_text_view"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/subscription_benefits_list"
            app:layout_constraintVertical_weight="0"
            app:layout_goneMarginBottom="24dp" />

        <TextView
            android:id="@+id/restore_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="24dp"
            android:fontFamily="@font/inter"
            android:gravity="center"
            android:lineSpacingMultiplier="1.18"
            android:text="@string/subscription_restore_title"
            android:textColor="@color/grey1"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/button_join_lovebeat_plus"
            app:layout_constraintVertical_bias="0" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>


