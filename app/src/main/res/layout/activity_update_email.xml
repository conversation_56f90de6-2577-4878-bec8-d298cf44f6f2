<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:focusableInTouchMode="true"
    android:fitsSystemWindows="true">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/nav_bar_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/edit_profile1_activity_nav_bar_constraint_layout_height"
        android:layout_marginRight="@dimen/edit_profile1_activity_nav_bar_constraint_layout_margin_end"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp">

        <TextView
            android:id="@+id/title_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            android:text="Update email"
            android:textColor="@color/grey1"
            android:textSize="@dimen/edit_profile1_activity_title_text_view_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/cancel_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/edit_profile1_activity_left_text_view_margin_start"
            android:fontFamily="@font/inter"
            android:gravity="start"
            android:lineSpacingMultiplier="1.1"
            android:text="@string/edit_profile1_activity_right_text_view_text"
            android:textColor="@color/grey1"
            android:textSize="@dimen/edit_profile1_activity_left_text_view_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/save_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/edit_profile1_activity_right_text_view_margin_end"
            android:fontFamily="@font/inter"
            android:gravity="end"
            android:lineSpacingMultiplier="1.1"
            android:text="@string/edit_profile1_activity_left_text_view_text"
            android:textColor="@color/grey1"
            android:textSize="@dimen/edit_profile1_activity_right_text_view_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/button_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/email_activity_button_constraint_layout_height"
        android:layout_marginStart="@dimen/email_activity_button_constraint_layout_margin_start"
        android:layout_marginEnd="@dimen/email_activity_button_constraint_layout_margin_end"
        android:layout_marginTop="@dimen/email_activity_button_constraint_layout_margin_top"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout"
        tools:layout_editor_absoluteX="24dp"
        tools:layout_editor_absoluteY="292dp">

        <android.widget.Button
            android:id="@+id/button_enable_location"
            style="?android:attr/borderlessButtonStyle"
            android:theme="@style/BottomCTAButton"
            android:layout_width="0dp"
            android:layout_height="@dimen/email_activity_button_large_active_button_height"
            android:layout_marginEnd="@dimen/email_activity_button_large_active_button_margin_end"
            android:background="@drawable/bottom_button_disabled_state"
            android:enabled="false"
            android:gravity="center"
            android:textColor="@color/white"
            android:text="@string/next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="0dp"
            tools:layout_editor_absoluteY="0dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/email_activity_group_constraint_layout_margin_start"
        android:layout_marginTop="64dp"
        android:layout_marginRight="@dimen/email_activity_group_constraint_layout_margin_end"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/this_ensures_that_yo_text_view"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:fontFamily="@font/inter"
            android:gravity="left"
            android:text="Enter your new email address."
            android:textColor="@color/grey1"
            android:textSize="@dimen/email_activity_this_ensures_that_yo_text_view_text_size"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/field_short_placeholder_constraint_layout"
            android:layout_width="@dimen/email_activity_field_short_placeholder_constraint_layout_width"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/this_ensures_that_yo_text_view">

            <EditText
                android:id="@+id/email_edit_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/email_activity_value_edit_text_margin_end"
                android:background="@color/white"
                android:fontFamily="@font/inter"
                android:gravity="left"
                android:hint="@string/email_screen_hint"
                android:inputType="textEmailAddress"
                android:lineSpacingMultiplier="1"
                android:textColor="@color/grey1"
                android:textColorHint="@color/grey4"
                android:textSize="@dimen/email_activity_value_edit_text_text_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/rectangle_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="@dimen/email_activity_rectangle_constraint_layout_height"
                android:background="@color/grey4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                tools:layout_editor_absoluteX="0dp"
                tools:layout_editor_absoluteY="35dp" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>