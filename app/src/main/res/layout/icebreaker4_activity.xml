<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/white"
	android:fitsSystemWindows="true">

	<include
		layout="@layout/custom_action_bar"
		android:layout_width="match_parent"
		android:layout_height="?attr/actionBarSize" />

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/text_box_empty_copy_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginStart="@dimen/icebreaker4_activity_text_box_empty_copy_constraint_layout_margin_start"
		android:layout_marginTop="64dp"
		android:layout_marginEnd="@dimen/icebreaker4_activity_text_box_empty_copy_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<EditText
			android:id="@+id/icebreaker2_edit_text"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginStart="@dimen/icebreaker4_activity_placeholder_edit_text_margin_start"
			android:layout_marginTop="@dimen/icebreaker4_activity_placeholder_edit_text_margin_top"
			android:background="@color/white"
			android:fontFamily="@font/inter"
			android:gravity="left"
			android:hint="@string/icebreaker4_activity_placeholder_edit_text_hint"
			android:inputType="textMultiLine|textCapSentences"
			android:lineSpacingMultiplier="1"
			android:maxLength="140"
			android:textColor="@color/grey1"
			android:textColorHint="@color/grey4"
			android:textSize="18sp"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/field_title_text_view" />

		<TextView
			android:id="@+id/field_title_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/icebreaker4_activity_field_title_text_view_margin_top"
			android:layout_marginEnd="24dp"
			android:fontFamily="@font/inter_bold"
			android:gravity="left"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/icebreaker4_activity_field_title_text_view_text"
			android:textColor="@color/grey1"
			android:textSize="@dimen/icebreaker4_activity_field_title_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<TextView
			android:id="@+id/height_text_view"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:fontFamily="@font/inter"
			android:gravity="right"
			android:letterSpacing="0.07"
			android:lineSpacingMultiplier="1.27"
			android:text="@string/icebreaker4_activity_text_view_text_view_text"
			android:textColor="@color/grey1"
			android:textSize="@dimen/icebreaker4_activity_text_view_text_view_text_size"
			app:layout_constraintTop_toBottomOf="@+id/icebreaker2_edit_text"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintRight_toRightOf="parent" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/button_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/icebreaker4_activity_button_constraint_layout_height"
		android:layout_marginStart="@dimen/icebreaker4_activity_button_constraint_layout_margin_start"
		android:layout_marginTop="52dp"
		android:layout_marginEnd="@dimen/icebreaker4_activity_button_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/text_box_empty_copy_constraint_layout">

		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/icebreaker4_activity_button_large_active_button_height"
			android:layout_marginEnd="@dimen/icebreaker4_activity_button_large_active_button_margin_end"
			android:background="@drawable/bottom_button_disabled_state"
			android:enabled="false"
			android:text="Next"
			android:textColor="@color/white"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp" />
	</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>