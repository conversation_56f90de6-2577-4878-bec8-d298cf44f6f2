<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="54dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_primary">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/song_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:maxLines="1"
        android:fontFamily="@font/inter_medium"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:layout_marginTop="4dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintEnd_toStartOf="@+id/mute_image"
        app:layout_constraintStart_toEndOf="@+id/song_image"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="La la bheemla"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/song_sub_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:maxLines="1"
        android:fontFamily="@font/inter"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="2dp"
        app:layout_constraintEnd_toStartOf="@+id/mute_image"
        app:layout_constraintStart_toEndOf="@+id/song_image"
        app:layout_constraintTop_toBottomOf="@+id/song_title"
        tools:text="La la bheemla"/>

    <ImageView
        android:id="@+id/song_image"
        android:layout_width="54dp"
        android:layout_height="54dp"
        android:scaleType="centerCrop"
        tools:srcCompat="@tools:sample/avatars"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/mute_image"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:scaleType="centerCrop"
        android:padding="6dp"
        android:layout_marginEnd="8dp"
        android:src="@drawable/speaker_wave_2_svgrepo_com"
        app:layout_constraintEnd_toStartOf="@+id/spotify_image"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <ImageView
        android:id="@+id/spotify_image"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:scaleType="centerCrop"
        android:layout_marginEnd="4dp"
        android:padding="6dp"
        android:src="@drawable/spotify_logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>