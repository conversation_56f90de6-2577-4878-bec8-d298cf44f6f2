<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:facebook="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ImageView
        android:id="@+id/image_view_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/logo"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toStartOf="@+id/title"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/title" />


    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="84dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="center"
        android:text="@string/app_name"
        android:textColor="@color/grey1"
        android:textSize="34sp"
        android:layout_marginStart="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toEndOf="@+id/image_view_logo"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/sub_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="36dp"
        android:fontFamily="@font/inter_semibold"
        android:gravity="center"
        android:text="@string/app_sub_title"
        android:textColor="@color/grey2"
        android:textSize="26sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/title" />

    <TextView
        android:id="@+id/by_proceeding_you_ag_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/intro1_activity_by_proceeding_you_ag_text_view_margin_start"
        android:layout_marginEnd="@dimen/intro1_activity_by_proceeding_you_ag_text_view_margin_start"
        android:fontFamily="@font/inter"
        android:gravity="center"
        android:lineSpacingMultiplier="1.29"
        android:text="@string/proceed_agreement_text"
        android:textColor="@color/grey1"
        android:textColorLink="@color/color_secondary"
        android:textSize="@dimen/intro1_activity_by_proceeding_you_ag_text_view_text_size"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="48dp"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <android.widget.Button
        android:id="@+id/phone_sign_in_button"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="@dimen/intro1_activity_button_large_active_button_height"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/bottom_button_active_state_ripple"
        android:text="@string/continue_ph_no_text"
        android:textColor="@color/white"
        android:visibility="gone"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintBottom_toTopOf="@+id/facebook_sign_in_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent" />

    <com.facebook.login.widget.LoginButton
        android:id="@+id/facebook_sign_in_button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/button_rounded_facebook"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:textSize="18sp"
        app:circularflow_radiusInDP="15dp"
        app:layout_constraintBottom_toTopOf="@+id/google_sign_in_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        facebook:com_facebook_login_text="LOGIN" />

    <android.widget.Button
        android:id="@+id/google_sign_in_button"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/button_rounded_gmail"
        android:drawableStart="@drawable/google_icon_logo"
        android:drawablePadding="-32dp"
        android:paddingStart="10dp"
        android:text="@string/continue_gmail_text"
        android:textColor="@color/grey2"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintBottom_toTopOf="@+id/twitter_sign_in_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <android.widget.Button
        android:id="@+id/twitter_sign_in_button"
        style="?android:attr/borderlessButtonStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/button_rounded_gmail"
        android:backgroundTint="@color/black"
        android:drawableStart="@drawable/twitter_logo"
        android:drawablePadding="-32dp"
        android:paddingStart="10dp"
        android:text="@string/continue_twitter_text"
        android:textColor="@color/white"
        android:theme="@style/BottomCTAButton"
        app:layout_constraintBottom_toTopOf="@+id/by_proceeding_you_ag_text_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="62dp"
        android:layout_height="62dp"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>