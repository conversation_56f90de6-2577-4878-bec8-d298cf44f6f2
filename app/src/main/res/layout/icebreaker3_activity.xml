<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="#FFF"
	android:fitsSystemWindows="true">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginTop="2dp"
		android:layout_marginBottom="2dp"
		android:background="#FFF"
		android:elevation="1dp"
		app:layout_constraintBottom_toTopOf="@+id/table_recycler_view"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<TextView
			android:id="@+id/skip_text_view"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_gravity="end"
			android:layout_marginTop="4dp"
			android:layout_marginEnd="16dp"
			android:padding="10dp"
			android:text="@string/cancel"
			android:textColor="@color/grey2"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<TextView
			android:id="@+id/you_re_halfway_there_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginLeft="24dp"
			android:layout_marginTop="16dp"
			android:layout_marginRight="24dp"
			android:fontFamily="@font/inter_bold"
			android:gravity="left"
			android:lineSpacingMultiplier="1"
			android:text="@string/icebreaker3_activity_you_re_halfway_there_text_view_text"
			android:textColor="@color/grey1"
			android:textSize="@dimen/icebreaker3_activity_you_re_halfway_there_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/skip_text_view" />

		<TextView
			android:id="@+id/select_one_more_ques_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginStart="24dp"
			android:layout_marginTop="@dimen/icebreaker3_activity_select_one_more_ques_text_view_margin_top"
			android:layout_marginEnd="24dp"
			android:layout_marginBottom="16dp"
			android:fontFamily="@font/inter"
			android:gravity="left"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/icebreaker3_activity_select_one_more_ques_text_view_text"
			android:textColor="@color/grey1"
			android:textSize="@dimen/icebreaker3_activity_select_one_more_ques_text_view_text_size"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintStart_toStartOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/you_re_halfway_there_text_view" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.recyclerview.widget.RecyclerView
		android:id="@+id/table_recycler_view"
		android:layout_width="0dp"
		android:layout_height="0dp"
		android:layout_marginTop="2dp"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintLeft_toLeftOf="parent"
		android:background="#00FFFFFF"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout"
		tools:listitem="@layout/row_question_two_two_view_holder" />

</androidx.constraintlayout.widget.ConstraintLayout>