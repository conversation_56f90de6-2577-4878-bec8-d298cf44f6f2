<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/white"
	android:fitsSystemWindows="true">

	<include
		layout="@layout/custom_action_bar"
		android:layout_width="match_parent"
		android:layout_height="?attr/actionBarSize" />

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/button_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/email_activity_button_constraint_layout_height"
		android:layout_marginStart="@dimen/email_activity_button_constraint_layout_margin_start"
		android:layout_marginTop="52dp"
		android:layout_marginEnd="@dimen/email_activity_button_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group_constraint_layout">

		<android.widget.Button
			android:id="@+id/button_enable_location"
			style="?android:attr/borderlessButtonStyle"
			android:theme="@style/BottomCTAButton"
			android:layout_width="0dp"
			android:layout_height="@dimen/email_activity_button_large_active_button_height"
			android:background="@drawable/bottom_button_active_state_ripple"
			android:text="@string/next"
			android:textColor="@color/white"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="@dimen/email_activity_group_constraint_layout_height"
		android:layout_marginLeft="@dimen/email_activity_group_constraint_layout_margin_start"
		android:layout_marginTop="64dp"
		android:layout_marginRight="@dimen/email_activity_group_constraint_layout_margin_end"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<TextView
			android:id="@+id/what_syour_email_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginRight="@dimen/email_activity_what_syour_email_text_view_margin_end"
			android:fontFamily="@font/inter_bold"
			android:gravity="left"
			android:lineSpacingMultiplier="1"
			android:text="@string/email_screen_title"
			android:textColor="@color/grey1"
			android:textSize="@dimen/email_activity_what_syour_email_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toTopOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="0dp" />

		<TextView
			android:id="@+id/this_ensures_that_yo_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginTop="@dimen/email_activity_this_ensures_that_yo_text_view_margin_top"
			android:fontFamily="@font/inter"
			android:gravity="left"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/email_screen_desc"
			android:textColor="@color/grey1"
			android:textSize="@dimen/email_activity_this_ensures_that_yo_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/what_syour_email_text_view" />

		<androidx.constraintlayout.widget.ConstraintLayout
			android:id="@+id/field_short_placeholder_constraint_layout"
			android:layout_width="@dimen/email_activity_field_short_placeholder_constraint_layout_width"
			android:layout_height="@dimen/email_activity_field_short_placeholder_constraint_layout_height"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			tools:layout_editor_absoluteX="0dp"
			tools:layout_editor_absoluteY="106dp">

			<EditText
				android:id="@+id/email_edit_text"
				android:layout_width="0dp"
				android:layout_height="wrap_content"
				android:layout_marginRight="@dimen/email_activity_value_edit_text_margin_end"
				android:background="@color/white"
				android:fontFamily="@font/inter"
				android:gravity="left"
				android:hint="@string/email_screen_hint"
				android:inputType="textEmailAddress"
				android:lineSpacingMultiplier="1"
				android:textColor="@color/grey1"
				android:textColorHint="@color/grey4"
				android:textSize="@dimen/email_activity_value_edit_text_text_size"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				app:layout_constraintTop_toTopOf="parent" />

			<androidx.constraintlayout.widget.ConstraintLayout
				android:id="@+id/rectangle_constraint_layout"
				android:layout_width="0dp"
				android:layout_height="@dimen/email_activity_rectangle_constraint_layout_height"
				android:background="@color/grey4"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintLeft_toLeftOf="parent"
				app:layout_constraintRight_toRightOf="parent"
				tools:layout_editor_absoluteX="0dp"
				tools:layout_editor_absoluteY="35dp" />
		</androidx.constraintlayout.widget.ConstraintLayout>
	</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>