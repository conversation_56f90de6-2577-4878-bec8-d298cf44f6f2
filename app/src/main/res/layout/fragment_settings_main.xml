<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="@color/white">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/view_recycler_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/settings_home_supernova_activity_view_recycler_view_margin_top"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/user_profile_image_view"
        tools:listitem="@layout/settings_row" />

    <TextView
        android:id="@+id/user_name_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16sp"
        android:layout_marginTop="32dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="start"
        android:textColor="@color/grey1"
        android:textSize="24sp"
        app:layout_constraintLeft_toRightOf="@+id/user_profile_image_view"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/upgrade_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16sp"
        android:layout_marginTop="4dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="start"
        android:text="@string/upgrade_to_lovebeat_plus"
        android:textColor="@color/color_primary"
        android:textSize="16sp"
        app:layout_constraintStart_toEndOf="@+id/user_profile_image_view"
        app:layout_constraintTop_toBottomOf="@+id/user_name_text_view" />

    <ImageView
        android:id="@+id/user_profile_image_view"
        android:layout_width="72dp"
        android:layout_height="72dp"
        android:layout_marginStart="@dimen/settings_home_supernova_activity_oval_image_view_margin_start"
        android:layout_marginTop="32dp"
        android:scaleType="fitXY"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/plus_image"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="16sp"
        android:layout_marginTop="6dp"
        android:scaleType="fitXY"
        android:visibility="gone"
        android:src="@drawable/ic_plus"
        app:layout_constraintStart_toEndOf="@+id/user_profile_image_view"
        app:layout_constraintTop_toBottomOf="@+id/user_name_text_view" />

    <TextView
        android:id="@+id/plus_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginTop="2dp"
        android:fontFamily="@font/inter"
        android:gravity="start"
        android:text="@string/lovebeat_plus_member"
        android:textColor="@color/grey2"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@+id/plus_image"
        app:layout_constraintTop_toBottomOf="@+id/user_name_text_view" />
</androidx.constraintlayout.widget.ConstraintLayout>