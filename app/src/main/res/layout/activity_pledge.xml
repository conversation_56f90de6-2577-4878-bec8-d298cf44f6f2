<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group4_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        app:layout_constraintVertical_bias="0"
        android:layout_marginBottom="6dp"
        app:layout_constraintBottom_toTopOf="@+id/pledge_agree_button"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/account_header_left_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/preferences_men_secopy2supernova_activity_tail_left_image_view_margin_start"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_arrow_left"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/pledge_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="24dp"
            android:layout_marginTop="6dp"
            android:fontFamily="@font/inter_bold"
            android:gravity="start"
            android:lineSpacingMultiplier="1.29"
            android:text="The LoveBeats Pledge"
            android:textColor="@color/grey1"
            android:textSize="22sp"
            app:layout_constraintStart_toEndOf="@+id/account_header_left_image_view"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <ImageView
            android:id="@+id/pledge_image"
            android:layout_width="0dp"
            android:layout_height="200dp"
            android:layout_marginTop="36dp"
            android:scaleType="centerCrop"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="48dp"
            android:src="@drawable/ic_pledge"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/account_header_left_image_view" />

        <TextView
            android:id="@+id/pledge_desc"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/height_celebration3_activity_let_sproceed_with_stext_view_margin_top"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:fontFamily="@font/inter"
            android:gravity="start"
            android:linksClickable="true"
            android:lineSpacingMultiplier="1.09"
            android:text="@string/pledge_desc"
            android:textColor="@color/grey2"
            android:textSize="16sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/pledge_image" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <android.widget.Button
        android:id="@+id/pledge_agree_button"
        style="?android:attr/borderlessButtonStyle"
        android:theme="@style/BottomCTAButton"
        android:layout_width="0dp"
        android:layout_height="@dimen/age_restriction_activity_button_large_active_button_height"
        android:layout_marginStart="@dimen/age_restriction_activity_button_large_active_button_margin_start"
        android:layout_marginEnd="@dimen/age_restriction_activity_button_large_active_button_margin_end"
        android:layout_marginTop="36dp"
        android:background="@drawable/bottom_button_active_state"
        android:text="I agree"
        android:textColor="@color/white"
        app:layout_constraintTop_toBottomOf="@+id/group4_constraint_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>