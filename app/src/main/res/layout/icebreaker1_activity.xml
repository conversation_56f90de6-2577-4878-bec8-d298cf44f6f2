<androidx.constraintlayout.widget.ConstraintLayout
	xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:app="http://schemas.android.com/apk/res-auto"
	xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
	android:layout_height="match_parent"
	android:background="@color/white"
	android:fitsSystemWindows="true">

	<androidx.constraintlayout.widget.ConstraintLayout
		android:id="@+id/group2_constraint_layout"
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_marginTop="2dp"
		android:background="#FFF"
		android:elevation="1dp"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toTopOf="parent">

		<TextView
			android:id="@+id/skip_text_view"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_gravity="end"
			android:layout_marginTop="4dp"
			android:layout_marginEnd="16dp"
			android:padding="10dp"
			android:text="@string/cancel"
			android:textColor="@color/grey2"
			app:layout_constraintEnd_toEndOf="parent"
			app:layout_constraintTop_toTopOf="parent" />

		<TextView
			android:id="@+id/let_sshow_off_your_text_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginLeft="24dp"
			android:layout_marginTop="16dp"
			android:layout_marginRight="24dp"
			android:fontFamily="@font/inter_bold"
			android:gravity="left"
			android:lineSpacingMultiplier="1"
			android:text="@string/icebreaker1_activity_let_sshow_off_your_text_view_text"
			android:textColor="@color/grey1"
			android:textSize="@dimen/icebreaker1_activity_let_sshow_off_your_text_view_text_size"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/skip_text_view" />

		<TextView
			android:id="@+id/choose_two_profile_qtext_view"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_marginLeft="24dp"
			android:layout_marginTop="@dimen/icebreaker1_activity_choose_two_profile_qtext_view_margin_top"
			android:layout_marginRight="24dp"
			android:layout_marginBottom="16dp"
			android:fontFamily="@font/inter"
			android:gravity="left"
			android:lineSpacingMultiplier="1.09"
			android:text="@string/icebreaker1_activity_choose_two_profile_qtext_view_text"
			android:textColor="@color/grey1"
			android:textSize="@dimen/icebreaker1_activity_choose_two_profile_qtext_view_text_size"
			app:layout_constraintBottom_toBottomOf="parent"
			app:layout_constraintLeft_toLeftOf="parent"
			app:layout_constraintRight_toRightOf="parent"
			app:layout_constraintTop_toBottomOf="@+id/let_sshow_off_your_text_view" />
	</androidx.constraintlayout.widget.ConstraintLayout>

	<androidx.recyclerview.widget.RecyclerView
		android:id="@+id/table_recycler_view"
		android:layout_width="0dp"
		android:layout_height="0dp"
		android:layout_marginTop="2dp"
		android:background="#00FFFFFF"
		app:layout_constraintBottom_toBottomOf="parent"
		app:layout_constraintLeft_toLeftOf="parent"
		app:layout_constraintRight_toRightOf="parent"
		app:layout_constraintTop_toBottomOf="@+id/group2_constraint_layout"
		tools:listitem="@layout/row_question_view_holder" />

</androidx.constraintlayout.widget.ConstraintLayout>