<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true">

    <TextView
        android:id="@+id/likes_you_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="36dp"
        android:fontFamily="@font/inter_bold"
        android:text="@string/likes_you"
        android:textColor="@color/grey1"
        android:textSize="24sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/likes_you_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:src="@drawable/blank_likes_you"
        app:layout_constraintBottom_toTopOf="@+id/liked_you_text_view"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/likes_you_text_view"
        tools:ignore="ContentDescription" />

    <TextView
        android:id="@+id/liked_you_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="24dp"
        android:layout_marginTop="32dp"
        android:layout_marginRight="24dp"
        android:fontFamily="@font/inter_bold"
        android:gravity="center"
        android:textColor="@color/grey1"
        android:text="No likes to show just yet"
        android:textSize="20sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/likes_you_image_view" />

    <TextView
        android:id="@+id/upgrade_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="36dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="36dp"
        android:fontFamily="@font/inter"
        android:gravity="center"
        android:text="@string/likes_you_desc"
        android:textColor="#646C70"
        android:textSize="@dimen/enable_location_activity_enable_your_location_text_view_text_size"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/liked_you_text_view" />

    <android.widget.Button
        android:id="@+id/button_upgrade"
        style="?android:attr/borderlessButtonStyle"
        android:theme="@style/BottomCTAButton"
        android:background="@drawable/bottom_button_active_state_ripple"
        android:layout_width="0dp"
        android:layout_height="@dimen/enable_location_activity_button_large_active_button_height"
        android:layout_marginLeft="24dp"
        android:layout_marginTop="18dp"
        android:layout_marginRight="24dp"
        android:text="@string/likes_you_button"
        android:textColor="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/upgrade_text_view" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="62dp"
        android:layout_height="62dp"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>