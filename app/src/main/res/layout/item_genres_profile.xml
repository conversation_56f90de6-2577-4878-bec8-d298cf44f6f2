<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:padding="0dp">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/genreButton"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:textAllCaps="false"
        app:backgroundTint="@color/color_primary"
        app:cornerRadius="12dp"
        app:strokeColor="@color/grey4"
        app:strokeWidth="1dp"
        android:minHeight="0dp"
        android:minWidth="0dp"
        android:layout_marginEnd="6dp"
        tools:text="Action"/>
</RelativeLayout>