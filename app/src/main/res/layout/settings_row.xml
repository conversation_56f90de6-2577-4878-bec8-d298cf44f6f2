<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/row_question_copy_view_holder_row_question_copy_height"
    android:background="@color/white">

    <TextView
        android:id="@+id/your_favorite_hobby_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/row_question_copy_view_holder_your_favorite_hobby_text_view_margin_start"
        android:fontFamily="@font/inter"
        android:gravity="start"
        android:lineSpacingMultiplier="1.09"
        android:text="@string/settings_main_item_1"
        android:textColor="@color/grey2"
        android:textSize="@dimen/row_question_copy_view_holder_your_favorite_hobby_text_view_text_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="24dp"
        tools:layout_editor_absoluteY="18dp"/>

    <ImageView
        android:id="@+id/stre_right_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/row_question_copy_view_holder_stre_right_image_view_margin_end"
        android:scaleType="center"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="299dp"
        tools:layout_editor_absoluteY="24dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>