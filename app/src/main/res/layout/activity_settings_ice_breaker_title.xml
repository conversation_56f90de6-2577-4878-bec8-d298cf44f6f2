<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/text_box_empty_copy_constraint_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/icebreaker2_activity_text_box_empty_copy_constraint_layout_margin_start"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="@dimen/icebreaker2_activity_text_box_empty_copy_constraint_layout_margin_end"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/save_text_view">

            <EditText
                android:id="@+id/icebreaker1_edit_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/icebreaker2_activity_placeholder_edit_text_margin_start"
                android:layout_marginTop="@dimen/icebreaker2_activity_placeholder_edit_text_margin_top"
                android:background="@color/white"
                android:fontFamily="@font/inter"
                android:gravity="left"
                android:hint="@string/icebreaker2_activity_placeholder_edit_text_hint"
                android:inputType="textMultiLine|textCapSentences"
                android:lineSpacingMultiplier="1"
                android:maxLength="140"
                android:textColor="@color/grey1"
                android:textColorHint="@color/grey3"
                android:textSize="18sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/field_title_text_view" />

            <TextView
                android:id="@+id/field_title_text_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/icebreaker2_activity_field_title_text_view_margin_top"
                android:layout_marginEnd="24dp"
                android:fontFamily="@font/inter_bold"
                android:gravity="left"
                android:lineSpacingMultiplier="1.09"
                android:text="@string/icebreaker2_activity_field_title_text_view_text"
                android:textColor="@color/grey1"
                android:textSize="@dimen/icebreaker2_activity_field_title_text_view_text_size"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/counter_text_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter"
                android:gravity="end"
                android:letterSpacing="0.07"
                android:lineSpacingMultiplier="1.27"
                android:text="@string/icebreaker2_activity_text_view_text_view_text"
                android:textColor="@color/grey1"
                android:textSize="@dimen/icebreaker2_activity_text_view_text_view_text_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/icebreaker1_edit_text"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/button_constraint_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/icebreaker2_activity_button_constraint_layout_margin_start"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="@dimen/icebreaker2_activity_button_constraint_layout_margin_end"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/text_box_empty_copy_constraint_layout">

            <android.widget.Button
                android:id="@+id/button_enable_location"
                style="?android:attr/borderlessButtonStyle"
                android:theme="@style/BottomCTAButton"
                android:layout_width="0dp"
                android:layout_height="@dimen/icebreaker2_activity_button_large_active_button_height"
                android:background="@drawable/bottom_button_disabled_state"
                android:enabled="false"
                android:text="@string/icebreaker2_activity_button_large_active_button_text"
                android:textColor="@color/grey4"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <android.widget.Button
                android:id="@+id/choose_new_question_button"
                style="?android:attr/borderlessButtonStyle"
                android:theme="@style/BottomCTAButton"
                android:layout_width="0dp"
                android:layout_height="@dimen/icebreaker2_activity_button_large_active_button_height"
                android:layout_marginTop="8dp"
                android:background="@drawable/button_border_blue"
                android:text="Choose new question"
                android:textColor="@color/color_primary"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/button_enable_location" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/cancel_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="32dp"
            android:layout_marginEnd="16dp"
            android:padding="10dp"
            android:text="Cancel"
            android:textColor="#646C70"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/save_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginStart="16dp"
            android:layout_marginTop="32dp"
            android:padding="10dp"
            android:text="Save"
            android:textColor="#646C70"
            android:textSize="16sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/settings_icebreaker_progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>