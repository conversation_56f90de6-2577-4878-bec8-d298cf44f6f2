<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Attribution Debug Console"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/debug_info_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#f0f0f0"
            android:padding="12dp"
            android:fontFamily="monospace"
            android:textSize="12sp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Simulate Attribution"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/simulate_google_ads_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Simulate Google Ads Attribution"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/simulate_meta_ads_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Simulate Meta Ads Attribution"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/simulate_organic_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Simulate Organic Attribution"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test Events"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/test_onboarding_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test Onboarding Complete"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/test_subscription_success_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test Subscription Success"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/test_subscription_failure_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test Subscription Failure"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Utilities"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/reset_attribution_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Reset Attribution Data"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/validate_flow_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Validate Attribution Flow"
            android:layout_marginBottom="8dp" />

    </LinearLayout>
</ScrollView>
