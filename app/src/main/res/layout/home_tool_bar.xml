<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="0dp"
    android:layout_height="50dp"
    android:background="@color/white"
    android:layout_alignParentStart="true"
    android:layout_alignParentEnd="true"
    android:layout_alignParentTop="true"
    android:layout_marginTop="-24dp">

    <TextView
        android:id="@+id/browse_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="Browse"
        android:textSize="18sp"
        android:fontFamily="@font/inter_bold"
        android:textColor="@color/grey1"/>

    <ImageView
        android:id="@+id/settings_top_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_filter"
        android:padding="10dp"
        android:layout_gravity="end"/>



</androidx.appcompat.widget.Toolbar>