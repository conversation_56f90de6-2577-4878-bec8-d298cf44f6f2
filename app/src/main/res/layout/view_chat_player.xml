<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.media3.ui.PlayerView
        android:id="@+id/chat_player_view"
        android:layout_width="50dp"
        android:layout_height="36dp"
        app:hide_on_touch="false"
        app:controller_layout_id="@layout/chat_player_item"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:show_timeout="0" />

</androidx.constraintlayout.widget.ConstraintLayout>