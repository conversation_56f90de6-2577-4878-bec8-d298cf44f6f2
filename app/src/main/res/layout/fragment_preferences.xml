<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_view_scroll_view_margin_top"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header_normal_constraint_layout">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/view_scroll_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="15dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/teleport_divider"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/grey5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/teleport_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_sex_preference_text_view_margin_start"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="4dp"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="0.86"
                    android:text="@string/preferences_teleport_title"
                    android:textColor="@color/grey3"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="8dp"
                    tools:layout_editor_absoluteY="3dp"
                    tools:text="@string/preferences_teleport_title" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/teleport_current_location_container"
                android:layout_width="0dp"
                android:layout_height="@dimen/preferences_men_secopy2supernova_activity_row_with_value_constraint_layout_height"
                android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_row_with_value_constraint_layout_margin_top"
                android:background="@color/white"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/teleport_divider">

                <TextView
                    android:id="@+id/location_textview"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_i_minterested_in_text_view_margin_start"
                    android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_i_minterested_in_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text="@string/preferences_teleport_location"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/preferences_men_secopy2supernova_activity_i_minterested_in_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="12dp"
                    tools:layout_editor_absoluteY="8dp" />

                <TextView
                    android:id="@+id/teleported_textview"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_men_text_view_margin_start"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="Teleported to:"
                    android:visibility="gone"
                    android:textColor="@color/color_primary"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/location_textview" />

                <TextView
                    android:id="@+id/location_desc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:text="@string/preferences_teleport_location_desc"
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/preferences_men_secopy2supernova_activity_men_text_view_text_size"
                    app:layout_constraintStart_toEndOf="@+id/teleported_textview"
                    app:layout_constraintTop_toBottomOf="@+id/location_textview"
                    tools:layout_editor_absoluteX="12dp"
                    tools:layout_editor_absoluteY="30dp"
                    app:drawableStartCompat="@drawable/ic_icon_location_pin"
                    android:drawablePadding="4dp"
                    android:drawableTint="@color/grey2"/>

                <ImageView
                    android:id="@+id/location_chevron"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/preferences_men_secopy2supernova_activity_stre_right_image_view_margin_end"
                    android:scaleType="center"
                    android:src="@drawable/ic_arrow_right"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="299dp"
                    tools:layout_editor_absoluteY="24dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/slider_age_range_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="@dimen/preferences_men_secopy2supernova_activity_slider_age_range_constraint_layout_height"
                android:layout_marginLeft="@dimen/preferences_men_secopy2supernova_activity_slider_age_range_constraint_layout_margin_start"
                android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_slider_age_range_constraint_layout_margin_top"
                android:layout_marginRight="@dimen/preferences_men_secopy2supernova_activity_slider_age_range_constraint_layout_margin_end"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/slider_distance_constraint_layout"
                tools:layout_editor_absoluteX="12dp"
                tools:layout_editor_absoluteY="376dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/group4_constraint_layout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="0dp"
                    tools:layout_editor_absoluteY="0dp">

                    <io.apptik.widget.MultiSlider
                        android:id="@+id/age_rage_group_seek_bar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:thumb="@drawable/custom_thumb"
                        app:drawThumbsApart="true"
                        app:stepsThumbsApart="7"
                        android:layout_marginEnd="@dimen/preferences_men_secopy2supernova_activity_group_seek_bar_margin_end"
                        android:layout_marginTop="12dp"
                        app:layout_constraintTop_toBottomOf="@+id/title_text_view"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        android:thumbTint="@color/color_tertiary"
                        app:rangeColor="@color/color_tertiary"
                        app:thumbColor="@color/color_tertiary" />

                    <TextView
                        android:id="@+id/title_text_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_title_text_view_margin_top"
                        android:fontFamily="@font/inter_bold"
                        android:gravity="start"
                        android:lineSpacingMultiplier="1.09"
                        android:text="@string/preferences_men_secopy2supernova_activity_title_text_view_text"
                        android:textColor="@color/grey1"
                        android:textSize="@dimen/preferences_men_secopy2supernova_activity_title_text_view_text_size"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:layout_editor_absoluteX="0dp"
                        tools:layout_editor_absoluteY="-1dp" />

                    <TextView
                        android:id="@+id/value_text_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_value_text_view_margin_top"
                        android:fontFamily="@font/inter"
                        android:gravity="end"
                        android:text="@string/preferences_men_secopy2supernova_activity_value_text_view_text"
                        android:lineSpacingMultiplier="1.09"
                        android:textColor="@color/grey2"
                        android:textSize="@dimen/preferences_men_secopy2supernova_activity_value_text_view_text_size"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:layout_editor_absoluteX="228dp"
                        tools:layout_editor_absoluteY="-1dp"
                        tools:text="@string/preferences_men_secopy2supernova_activity_value_text_view_text" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/slider_distance_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_slider_distance_constraint_layout_margin_start"
                android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_slider_distance_constraint_layout_margin_top"
                android:layout_marginEnd="@dimen/preferences_men_secopy2supernova_activity_slider_distance_constraint_layout_margin_end"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divider_with_text_copy2_constraint_layout">

                <io.apptik.widget.MultiSlider
                    android:id="@+id/distance_group_two_seek_bar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/preferences_men_secopy2supernova_activity_group_two_seek_bar_margin_end"
                    app:thumbNumber="1"
                    android:layout_marginTop="12dp"
                    app:layout_constraintTop_toBottomOf="@+id/sex_text_view"
                    android:thumb="@drawable/custom_thumb"
                    android:thumbTint="@color/color_tertiary"
                    app:rangeColor="@color/color_tertiary"
                    app:thumbColor="@color/color_tertiary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent" />

                <TextView
                    android:id="@+id/sex_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_sex_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text="@string/preferences_men_secopy2supernova_activity_sex_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/preferences_men_secopy2supernova_activity_sex_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/value_two_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_value_two_text_view_margin_top"
                    android:fontFamily="@font/inter"
                    android:gravity="end"
                    android:lineSpacingMultiplier="1.09"
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/preferences_men_secopy2supernova_activity_value_two_text_view_text_size"
                    android:text="@string/preferences_men_secopy2supernova_activity_value_two_text_view_text"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>



            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/divider_with_text_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:background="@color/grey5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/teleport_current_location_container">

                <TextView
                    android:id="@+id/sex_preference_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="4dp"
                    android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_sex_preference_text_view_margin_start"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="0.86"
                    android:textColor="@color/grey3"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="8dp"
                    tools:layout_editor_absoluteY="3dp"
                    tools:text="@string/preferences_men_secopy2supernova_activity_sex_preference_text_view_text" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/divider_with_text_copy_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="@dimen/preferences_men_secopy2supernova_activity_divider_with_text_copy_constraint_layout_height"
                android:layout_marginTop="24dp"
                android:background="@color/grey5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/filters_disabled_textView">

                <TextView
                    android:id="@+id/sex_preference_two_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_sex_preference_two_text_view_margin_start"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="0.86"
                    android:textColor="@color/grey3"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="8dp"
                    tools:layout_editor_absoluteY="3dp"
                    tools:text="@string/preferences_men_secopy2supernova_activity_sex_preference_two_text_view_text" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/divider_with_text_copy2_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:background="@color/grey5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/slider_distance_two_constraint_layout">

                <TextView
                    android:id="@+id/sex_preference_three_text_view"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_sex_preference_three_text_view_margin_start"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="4dp"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="0.86"
                    android:textColor="@color/grey3"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="8dp"
                    tools:layout_editor_absoluteY="3dp"
                    tools:text="@string/preferences_men_secopy2supernova_activity_sex_preference_three_text_view_text" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/row_with_value_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_row_with_value_constraint_layout_margin_top"
                android:background="@color/white"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divider_with_text_constraint_layout"
                tools:layout_editor_absoluteX="0dp"
                tools:layout_editor_absoluteY="27dp">

                <TextView
                    android:id="@+id/i_minterested_in_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_i_minterested_in_text_view_margin_start"
                    android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_i_minterested_in_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text="@string/preferences_men_secopy2supernova_activity_i_minterested_in_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/preferences_men_secopy2supernova_activity_i_minterested_in_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="12dp"
                    tools:layout_editor_absoluteY="8dp" />

                <TextView
                    android:id="@+id/men_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_men_text_view_margin_start"
                    android:layout_marginTop="8dp"
                    android:fontFamily="@font/inter"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.1"
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/preferences_men_secopy2supernova_activity_men_text_view_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/i_minterested_in_text_view"
                    tools:layout_editor_absoluteX="12dp"
                    tools:layout_editor_absoluteY="30dp" />

                <ImageView
                    android:id="@+id/stre_right_image_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/preferences_men_secopy2supernova_activity_stre_right_image_view_margin_end"
                    android:scaleType="center"
                    android:src="@drawable/ic_arrow_right"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:layout_editor_absoluteX="299dp"
                    tools:layout_editor_absoluteY="24dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/filters_disabled_textView"
                android:text="Just a heads up, height filters will be hidden when “Everyone” is selected."
                android:textSize="12sp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@color/off_yellow"
                android:gravity="center"
                android:padding="10dp"
                android:fontFamily="@font/inter"
                android:textColor="@color/grey2"
                android:layout_marginTop="16dp"
                android:layout_marginStart="14dp"
                android:layout_marginEnd="14dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/row_with_value_constraint_layout"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>



            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/slider_distance_two_constraint_layout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/preferences_men_secopy2supernova_activity_slider_distance_two_constraint_layout_margin_start"
                android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_slider_distance_two_constraint_layout_margin_top"
                android:layout_marginEnd="@dimen/preferences_men_secopy2supernova_activity_slider_distance_two_constraint_layout_margin_end"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/divider_with_text_copy_constraint_layout">

                <TextView
                    android:id="@+id/sex_two_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_sex_two_text_view_margin_top"
                    android:fontFamily="@font/inter_bold"
                    android:gravity="start"
                    android:lineSpacingMultiplier="1.09"
                    android:text="@string/preferences_men_secopy2supernova_activity_sex_two_text_view_text"
                    android:textColor="@color/grey1"
                    android:textSize="@dimen/preferences_men_secopy2supernova_activity_sex_two_text_view_text_size"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/value_four_text_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/preferences_men_secopy2supernova_activity_value_four_text_view_margin_top"
                    android:fontFamily="@font/inter"
                    android:gravity="end"
                    android:text="@string/preferences_men_secopy2supernova_activity_value_four_text_view_text"
                    android:lineSpacingMultiplier="1.09"
                    android:textColor="@color/grey2"
                    android:textSize="@dimen/preferences_men_secopy2supernova_activity_value_four_text_view_text_size"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <io.apptik.widget.MultiSlider
                    android:id="@+id/member_height_seek_bar"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/preferences_men_secopy2supernova_activity_group_four_seek_bar_margin_end"
                    android:layout_marginTop="12dp"
                    app:layout_constraintTop_toBottomOf="@+id/sex_two_text_view"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:drawThumbsApart="true"
                    android:thumb="@drawable/custom_thumb"
                    android:thumbTint="@color/color_tertiary"
                    app:rangeColor="@color/color_tertiary"
                    app:thumbColor="@color/color_tertiary"/>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_normal_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="@dimen/preferences_men_secopy2supernova_activity_header_normal_constraint_layout_height"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="0dp">

        <ImageButton
            android:id="@+id/preferences_header_left_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/preferences_men_secopy2supernova_activity_tail_left_image_view_margin_start"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:elevation="2dp"
            android:padding="10dp"
            android:scaleType="center"
            android:src="@drawable/ic_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/title_three_text_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_bold"
            android:gravity="center"
            android:lineSpacingMultiplier="1.09"
            android:text="@string/preferences_men_secopy2supernova_activity_title_three_text_view_text"
            android:textColor="@color/grey1"
            android:textSize="@dimen/preferences_men_secopy2supernova_activity_title_three_text_view_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>