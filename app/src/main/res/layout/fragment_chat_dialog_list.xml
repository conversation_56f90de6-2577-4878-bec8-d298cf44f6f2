<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="24dp"
        android:paddingTop="12dp"
        android:paddingBottom="4dp"
        android:gravity="start"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/matches_tab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Matches"
            android:textSize="20sp"
            android:paddingBottom="8dp"
            android:fontFamily="@font/inter_extrabold"
            android:background="?android:attr/selectableItemBackground"
            android:textColor="@color/grey1" />

        <TextView
            android:id="@+id/requests_tab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Requests"
            android:textSize="20sp"
            android:layout_marginStart="32dp"
            android:paddingBottom="8dp"
            android:fontFamily="@font/inter_extrabold"
            android:background="?android:attr/selectableItemBackground"
            android:textColor="@color/grey1" />

        <TextView
            android:id="@+id/requests_badge"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginStart="4dp"
            android:background="@drawable/red_circle_bg"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:gravity="center"
            android:visibility="gone"
            tools:text="4" />

    </LinearLayout>

    <View
        android:id="@+id/tab_indicator"
        android:layout_width="60dp"
        android:layout_height="2dp"
        android:layout_marginTop="4dp"
        android:background="@color/color_primary"
        android:translationX="0dp"
        android:layout_marginStart="27dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tab_layout"
        tools:ignore="MissingConstraints" />

    <com.stfalcon.chatkit.dialogs.DialogsList
        android:id="@+id/dialogsList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:dialogAvatarHeight="65dp"
        app:dialogAvatarWidth="65dp"
        app:dialogMessageAvatarEnabled="false"
        app:dialogTitleTextSize="12sp"
        app:dialogTitleTextColor="#25333D"
        app:dialogTitleTextStyle="bold"
        android:layout_marginTop="16dp"
        app:dialogDateSize="12sp"
        app:dialogDateColor="#585F63"
        app:dialogMessageTextColor="#25333D"
        app:dialogMessageTextSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tab_indicator" />

    <ProgressBar
        android:id="@+id/chat_dialog_progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/blank_chat_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/icon_empty_magnifying_glass_image_view"
            android:layout_width="200dp"
            android:layout_height="200dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:lottie_autoPlay="false"
            app:lottie_loop="false"
            android:scaleType="centerCrop"
            app:lottie_rawRes="@raw/blank_messages" />

        <TextView
            android:id="@+id/chat_blank_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No Matches Yet"
            android:textSize="18sp"
            android:textStyle="bold"
            android:fontFamily="@font/inter_bold"
            android:textColor="@color/grey1"
            android:layout_gravity="center_horizontal"
            app:layout_constraintTop_toBottomOf="@+id/icon_empty_magnifying_glass_image_view"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/chat_blank_desc"
            android:layout_width="@dimen/blank_matches_activity_when_you_match_with_text_view_width"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:fontFamily="@font/inter"
            android:gravity="center"
            android:text="@string/blank_matches_activity_when_you_match_with_text_view_text"
            android:textColor="@color/grey2"
            android:textSize="@dimen/blank_screen_desc_text_size"
            android:layout_marginStart="24sp"
            android:layout_marginEnd="24sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/chat_blank_title" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>