<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:id="@+id/subscriptions_benefits_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/subscription_item_image"
        android:layout_width="136dp"
        android:layout_height="112dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:src="@drawable/ic_woman_thinking" />

    <ImageView
        android:id="@+id/subscription_item_blue_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_marginStart="24dp"
        android:layout_toEndOf="@+id/subscription_item_image"
        android:scaleType="center"
        android:src="@drawable/subscription_blue_line" />

    <TextView
        android:id="@+id/subscription_item_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="8dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="16dp"
        android:layout_toEndOf="@+id/subscription_item_blue_line"
        android:fontFamily="@font/inter_bold"
        android:textColor="@color/grey1"
        android:textSize="16sp"
        tools:text="See all likes" />

    <TextView
        android:id="@+id/subscription_item_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="8dp"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="16dp"
        android:layout_toEndOf="@+id/subscription_item_blue_line"
        android:layout_below="@+id/subscription_item_name"
        android:fontFamily="@font/inter"
        android:textColor="@color/grey2"
        android:textSize="12sp"
        tools:text="Save time by seeing everyone that’s like you." />

    <View
        android:id="@+id/view_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_below="@+id/subscription_item_image"
        android:layout_marginTop="12dp"
        android:background="#F2F2F2" />
</RelativeLayout>