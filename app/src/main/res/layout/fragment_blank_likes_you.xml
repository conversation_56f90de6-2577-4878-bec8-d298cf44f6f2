<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/group_constraint_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/icon_empty_magnifying_glass_image_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:src="@drawable/blank_likes_you"
            android:scaleType="centerCrop" />

        <TextView
            android:layout_width="@dimen/blank_matches_activity_when_you_match_with_text_view_width"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:fontFamily="@font/inter"
            android:gravity="center"
            android:text="@string/blank_likes_you"
            android:textColor="@color/grey1"
            android:textSize="@dimen/blank_screen_desc_text_size"
            android:layout_marginStart="24sp"
            android:layout_marginEnd="24sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/icon_empty_magnifying_glass_image_view" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>