<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/row_question_two_two_view_holder_row_question_height">

    <ImageView
        android:id="@+id/icon_question_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/row_question_two_two_view_holder_icon_question_image_view_margin_start"
        android:src="@drawable/ic_icon_question_mark"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="11dp"
        tools:layout_editor_absoluteY="21dp"/>

    <TextView
        android:id="@+id/your_favorite_hobby_text_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:fontFamily="@font/inter"
        android:gravity="left"
        android:lineSpacingMultiplier="1.1"
        android:maxLines="2"
        android:text="@string/row_question_two_two_view_holder_your_favorite_hobby_text_view_text"
        android:textColor="@color/grey1"
        android:textSize="@dimen/row_question_two_two_view_holder_your_favorite_hobby_text_view_text_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/stre_right_image_view"
        app:layout_constraintStart_toEndOf="@+id/icon_question_image_view"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/stre_right_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/row_question_two_two_view_holder_stre_right_image_view_margin_end"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="304dp"
        tools:layout_editor_absoluteY="25dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>